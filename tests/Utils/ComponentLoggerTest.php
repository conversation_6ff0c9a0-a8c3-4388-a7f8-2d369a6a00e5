<?php
namespace Tests\Utils;

use PHPUnit\Framework\TestCase;
use App\Utils\ComponentLogger;

/**
 * Test class for ComponentLogger functionality
 */
class ComponentLoggerTest extends TestCase
{
    private $testLogDir;
    private $originalErrorLog;
    
    protected function setUp(): void
    {
        // Create a temporary directory for test logs
        $this->testLogDir = sys_get_temp_dir() . '/component_logger_tests_' . uniqid();
        if (!is_dir($this->testLogDir)) {
            mkdir($this->testLogDir, 0777, true);
        }
        
        // Store original error_log setting
        $this->originalErrorLog = ini_get('error_log');
        
        // Set error_log to our test directory so ComponentLogger uses it
        ini_set('error_log', $this->testLogDir . '/php_errors.log');
    }
    
    protected function tearDown(): void
    {
        // Restore original error_log setting
        ini_set('error_log', $this->originalErrorLog);
        
        // Clean up test log files
        if (is_dir($this->testLogDir)) {
            $files = glob($this->testLogDir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
            rmdir($this->testLogDir);
        }
    }
    
    /**
     * Test basic logger instantiation
     */
    public function testLoggerInstantiation()
    {
        $logger = new ComponentLogger('test_component');
        
        $this->assertInstanceOf(ComponentLogger::class, $logger);
        $this->assertEquals('test_component', $logger->getComponentName());
        $this->assertStringContainsString('test_component_errors.log', $logger->getLogFile());
    }
    
    /**
     * Test log file path generation
     */
    public function testLogFilePathGeneration()
    {
        $logger = new ComponentLogger('my_component');
        $logFile = $logger->getLogFile();
        
        // Should end with component_name_errors.log
        $this->assertStringEndsWith('my_component_errors.log', $logFile);
        
        // Should be in the same directory as PHP's error_log
        $expectedDir = dirname(ini_get('error_log'));
        $actualDir = dirname($logFile);
        $this->assertEquals($expectedDir, $actualDir);
    }
    
    /**
     * Test error level logging
     */
    public function testErrorLevelLogging()
    {
        $logger = new ComponentLogger('test_error');
        $message = 'This is an error message';
        
        $logger->error($message);
        
        $logContent = file_get_contents($logger->getLogFile());
        $this->assertStringContainsString('[ERROR]', $logContent);
        $this->assertStringContainsString($message, $logContent);
        $this->assertMatchesRegularExpression('/\[\d{2}-\w{3}-\d{4} \d{2}:\d{2}:\d{2} \w+\]/', $logContent);
    }
    
    /**
     * Test warning level logging
     */
    public function testWarningLevelLogging()
    {
        $logger = new ComponentLogger('test_warning');
        $message = 'This is a warning message';
        
        $logger->warning($message);
        
        $logContent = file_get_contents($logger->getLogFile());
        $this->assertStringContainsString('[WARNING]', $logContent);
        $this->assertStringContainsString($message, $logContent);
    }
    
    /**
     * Test info level logging
     */
    public function testInfoLevelLogging()
    {
        $logger = new ComponentLogger('test_info');
        $message = 'This is an info message';
        
        $logger->info($message);
        
        $logContent = file_get_contents($logger->getLogFile());
        $this->assertStringContainsString('[INFO]', $logContent);
        $this->assertStringContainsString($message, $logContent);
    }
    
    /**
     * Test debug level logging
     */
    public function testDebugLevelLogging()
    {
        $logger = new ComponentLogger('test_debug');
        $message = 'This is a debug message';
        
        $logger->debug($message);
        
        $logContent = file_get_contents($logger->getLogFile());
        $this->assertStringContainsString('[DEBUG]', $logContent);
        $this->assertStringContainsString($message, $logContent);
    }
    
    /**
     * Test generic log method with default level
     */
    public function testGenericLogMethodDefaultLevel()
    {
        $logger = new ComponentLogger('test_generic');
        $message = 'This is a generic log message';
        
        $logger->log($message);
        
        $logContent = file_get_contents($logger->getLogFile());
        $this->assertStringContainsString('[ERROR]', $logContent); // Default should be ERROR
        $this->assertStringContainsString($message, $logContent);
    }
    
    /**
     * Test generic log method with specific level
     */
    public function testGenericLogMethodWithSpecificLevel()
    {
        $logger = new ComponentLogger('test_specific');
        $message = 'This is a specific level message';
        
        $logger->log($message, ComponentLogger::WARNING);
        
        $logContent = file_get_contents($logger->getLogFile());
        $this->assertStringContainsString('[WARNING]', $logContent);
        $this->assertStringContainsString($message, $logContent);
    }
    
    /**
     * Test multiple log entries
     */
    public function testMultipleLogEntries()
    {
        $logger = new ComponentLogger('test_multiple');
        
        $logger->error('Error message');
        $logger->warning('Warning message');
        $logger->info('Info message');
        $logger->debug('Debug message');
        
        $logContent = file_get_contents($logger->getLogFile());
        
        $this->assertStringContainsString('[ERROR] Error message', $logContent);
        $this->assertStringContainsString('[WARNING] Warning message', $logContent);
        $this->assertStringContainsString('[INFO] Info message', $logContent);
        $this->assertStringContainsString('[DEBUG] Debug message', $logContent);
        
        // Should have 4 log entries (count by level markers)
        $this->assertEquals(1, substr_count($logContent, '[ERROR]'));
        $this->assertEquals(1, substr_count($logContent, '[WARNING]'));
        $this->assertEquals(1, substr_count($logContent, '[INFO]'));
        $this->assertEquals(1, substr_count($logContent, '[DEBUG]'));
    }
    
    /**
     * Test different component names create different log files
     */
    public function testDifferentComponentsCreateDifferentLogFiles()
    {
        $logger1 = new ComponentLogger('component_one');
        $logger2 = new ComponentLogger('component_two');
        
        $logger1->info('Message from component one');
        $logger2->info('Message from component two');
        
        $logFile1 = $logger1->getLogFile();
        $logFile2 = $logger2->getLogFile();
        
        // Should be different files
        $this->assertNotEquals($logFile1, $logFile2);
        
        // Each should contain only its own message
        $content1 = file_get_contents($logFile1);
        $content2 = file_get_contents($logFile2);
        
        $this->assertStringContainsString('Message from component one', $content1);
        $this->assertStringNotContainsString('Message from component two', $content1);
        
        $this->assertStringContainsString('Message from component two', $content2);
        $this->assertStringNotContainsString('Message from component one', $content2);
    }
    
    /**
     * Test log constants are defined correctly
     */
    public function testLogConstants()
    {
        $this->assertEquals('ERROR', ComponentLogger::ERROR);
        $this->assertEquals('WARNING', ComponentLogger::WARNING);
        $this->assertEquals('INFO', ComponentLogger::INFO);
        $this->assertEquals('DEBUG', ComponentLogger::DEBUG);
    }
    
    /**
     * Test component name validation and sanitization
     */
    public function testComponentNameHandling()
    {
        // Test normal component name
        $logger1 = new ComponentLogger('normal_component');
        $this->assertEquals('normal_component', $logger1->getComponentName());
        
        // Test component name with special characters (should still work)
        $logger2 = new ComponentLogger('component-with-dashes');
        $this->assertEquals('component-with-dashes', $logger2->getComponentName());
        $this->assertStringContainsString('component-with-dashes_errors.log', $logger2->getLogFile());
    }
    
    /**
     * Test timestamp format in log entries
     */
    public function testTimestampFormat()
    {
        $logger = new ComponentLogger('test_timestamp');
        $logger->info('Test timestamp message');
        
        $logContent = file_get_contents($logger->getLogFile());
        
        // Should match format: [02-Jul-2025 03:09:35 UTC]
        $this->assertMatchesRegularExpression(
            '/\[\d{2}-\w{3}-\d{4} \d{2}:\d{2}:\d{2} \w+\]/',
            $logContent,
            'Timestamp format should match [DD-MMM-YYYY HH:MM:SS TZ]'
        );
    }
    
    /**
     * Test log message with special characters and encoding
     */
    public function testSpecialCharactersInLogMessage()
    {
        $logger = new ComponentLogger('test_special');
        $specialMessage = 'Message with special chars: àáâãäå ñ 中文 🚀 "quotes" \'apostrophes\' & <tags>';

        $logger->info($specialMessage);

        $logContent = file_get_contents($logger->getLogFile());
        $this->assertStringContainsString($specialMessage, $logContent);
    }

    /**
     * Test empty log message
     */
    public function testEmptyLogMessage()
    {
        $logger = new ComponentLogger('test_empty');
        $logger->info('');

        $logContent = file_get_contents($logger->getLogFile());
        $this->assertStringContainsString('[INFO]', $logContent);
        // Should still have timestamp and level, just empty message
        $this->assertMatchesRegularExpression('/\[INFO\]\s*$/', trim($logContent));
    }

    /**
     * Test very long log message
     */
    public function testVeryLongLogMessage()
    {
        $logger = new ComponentLogger('test_long');
        $longMessage = str_repeat('This is a very long message. ', 100);

        $logger->error($longMessage);

        $logContent = file_get_contents($logger->getLogFile());
        $this->assertStringContainsString('[ERROR]', $logContent);
        $this->assertStringContainsString($longMessage, $logContent);
    }

    /**
     * Test log message with newlines
     */
    public function testLogMessageWithNewlines()
    {
        $logger = new ComponentLogger('test_newlines');
        $messageWithNewlines = "Line 1\nLine 2\nLine 3";

        $logger->warning($messageWithNewlines);

        $logContent = file_get_contents($logger->getLogFile());
        $this->assertStringContainsString('[WARNING]', $logContent);
        $this->assertStringContainsString($messageWithNewlines, $logContent);
    }

    /**
     * Test concurrent logging (simulate multiple instances)
     */
    public function testConcurrentLogging()
    {
        $logger1 = new ComponentLogger('test_concurrent');
        $logger2 = new ComponentLogger('test_concurrent'); // Same component name

        $logger1->info('Message from logger 1');
        $logger2->error('Message from logger 2');

        // Both should write to the same file
        $this->assertEquals($logger1->getLogFile(), $logger2->getLogFile());

        $logContent = file_get_contents($logger1->getLogFile());
        $this->assertStringContainsString('Message from logger 1', $logContent);
        $this->assertStringContainsString('Message from logger 2', $logContent);
        $this->assertStringContainsString('[INFO]', $logContent);
        $this->assertStringContainsString('[ERROR]', $logContent);
    }

    /**
     * Test fallback when error_log is set to syslog
     */
    public function testFallbackWhenErrorLogIsSyslog()
    {
        // Temporarily set error_log to syslog
        $originalErrorLog = ini_get('error_log');
        ini_set('error_log', 'syslog');

        try {
            $logger = new ComponentLogger('test_syslog_fallback');
            $logFile = $logger->getLogFile();

            // Should fallback to a writable directory
            $this->assertStringEndsWith('test_syslog_fallback_errors.log', $logFile);

            // Should be able to write to it
            $logger->info('Test message for syslog fallback');
            $this->assertFileExists($logFile);

            $logContent = file_get_contents($logFile);
            $this->assertStringContainsString('[INFO]', $logContent);
            $this->assertStringContainsString('Test message for syslog fallback', $logContent);

        } finally {
            // Restore original setting
            ini_set('error_log', $originalErrorLog);
        }
    }

    /**
     * Test invalid log level handling
     */
    public function testInvalidLogLevel()
    {
        $logger = new ComponentLogger('test_invalid_level');

        // Should not throw an exception, should use the invalid level as-is
        $logger->log('Test message', 'INVALID_LEVEL');

        $logContent = file_get_contents($logger->getLogFile());
        $this->assertStringContainsString('[INVALID_LEVEL]', $logContent);
        $this->assertStringContainsString('Test message', $logContent);
    }

    /**
     * Test log file permissions and writeability
     */
    public function testLogFileWriteability()
    {
        $logger = new ComponentLogger('test_writeable');
        $logFile = $logger->getLogFile();

        // Log a message to create the file
        $logger->info('Test writeability');

        // File should exist and be readable
        $this->assertFileExists($logFile);
        $this->assertFileIsReadable($logFile);
        $this->assertFileIsWritable($logFile);
    }

    /**
     * Test component name edge cases
     */
    public function testComponentNameEdgeCases()
    {
        // Test with numbers
        $logger1 = new ComponentLogger('component123');
        $this->assertEquals('component123', $logger1->getComponentName());

        // Test with underscores and dashes
        $logger2 = new ComponentLogger('my_component-v2');
        $this->assertEquals('my_component-v2', $logger2->getComponentName());

        // Test with single character
        $logger3 = new ComponentLogger('a');
        $this->assertEquals('a', $logger3->getComponentName());
        $this->assertStringEndsWith('a_errors.log', $logger3->getLogFile());
    }

    /**
     * Test that log entries are properly appended
     */
    public function testLogEntriesAreAppended()
    {
        $logger = new ComponentLogger('test_append');

        $logger->info('First message');
        $firstContent = file_get_contents($logger->getLogFile());

        $logger->error('Second message');
        $secondContent = file_get_contents($logger->getLogFile());

        // Second content should contain both messages
        $this->assertStringContainsString('First message', $secondContent);
        $this->assertStringContainsString('Second message', $secondContent);

        // Second content should be longer than first
        $this->assertGreaterThan(strlen($firstContent), strlen($secondContent));
    }

    /**
     * Test audit trail logging patterns used in TagController
     */
    public function testAuditTrailLoggingPatterns()
    {
        $logger = new ComponentLogger('test_audit');

        // Test tag operation logging patterns
        $logger->info("TAG CREATE SUCCESS: Created new top-level tag 2105 with name 'New Category' (slug: 'new-category')");
        $logger->info("TAG UPDATE SUCCESS: Tag 2106 renamed from 'Old Name' to 'New Name' - affects 5 articles: 123, 456, 789, 101, 112");
        $logger->info("TAG DELETE COMPLETE: Successfully deleted tag 2107 (Test Tag) and removed from 3 articles: 200, 201, 202");
        $logger->info("PARENT CHANGE OPERATION: Tag 2108 moving from parent 21 to parent 22 at position 1");
        $logger->info("ARTICLE LINK SUCCESS: Linked article 300 to tag 2109 (Category Name) with sort value 5");
        $logger->info("ARTICLE UNLINK SUCCESS: Unlinked article 301 from tag 2110 (Another Category)");

        $logContent = file_get_contents($logger->getLogFile());

        // Verify all audit trail patterns are logged correctly
        $this->assertStringContainsString('TAG CREATE SUCCESS', $logContent);
        $this->assertStringContainsString('TAG UPDATE SUCCESS', $logContent);
        $this->assertStringContainsString('TAG DELETE COMPLETE', $logContent);
        $this->assertStringContainsString('PARENT CHANGE OPERATION', $logContent);
        $this->assertStringContainsString('ARTICLE LINK SUCCESS', $logContent);
        $this->assertStringContainsString('ARTICLE UNLINK SUCCESS', $logContent);

        // Verify specific details are captured
        $this->assertStringContainsString("affects 5 articles: 123, 456, 789, 101, 112", $logContent);
        $this->assertStringContainsString("removed from 3 articles: 200, 201, 202", $logContent);
        $this->assertStringContainsString("with sort value 5", $logContent);
    }

    /**
     * Test bulk operation logging patterns
     */
    public function testBulkOperationLogging()
    {
        $logger = new ComponentLogger('test_bulk');

        // Test bulk linking operation
        $logger->info("BULK LINK OPERATION COMPLETE: Tag 2111 (Bulk Category) - 8 successful, 2 failed out of 10 articles");
        $logger->warning("BULK LINK FAILURES: Failed to link articles 999, 998 to tag 2111 (Bulk Category)");

        // Test individual bulk operations
        $logger->info("BULK LINK SUCCESS: Linked article 400 to tag 2111 with sort 1");
        $logger->error("BULK LINK FAILED: Failed to link article 401 to tag 2111 - DB error: Duplicate entry");

        $logContent = file_get_contents($logger->getLogFile());

        // Verify bulk operation patterns
        $this->assertStringContainsString('BULK LINK OPERATION COMPLETE', $logContent);
        $this->assertStringContainsString('8 successful, 2 failed out of 10 articles', $logContent);
        $this->assertStringContainsString('BULK LINK FAILURES', $logContent);
        $this->assertStringContainsString('Failed to link articles 999, 998', $logContent);
        $this->assertStringContainsString('BULK LINK SUCCESS', $logContent);
        $this->assertStringContainsString('BULK LINK FAILED', $logContent);
    }

    /**
     * Test error and warning logging for critical operations
     */
    public function testCriticalOperationErrorLogging()
    {
        $logger = new ComponentLogger('test_critical');

        // Test critical error patterns
        $logger->error("TAG CREATE FAILED: Failed to create tag 2112 with name 'Failed Tag' - DB error: Table doesn't exist");
        $logger->error("TAG DELETE FAILED: Failed to delete tag 2113 (Critical Tag) from blog_cat table - DB error: Foreign key constraint");
        $logger->warning("TAG DELETE: About to delete 15 tag references for tag 2114 (Warning Tag) from articles: 500, 501, 502");

        $logContent = file_get_contents($logger->getLogFile());

        // Verify critical error patterns
        $this->assertStringContainsString('[ERROR]', $logContent);
        $this->assertStringContainsString('[WARNING]', $logContent);
        $this->assertStringContainsString('TAG CREATE FAILED', $logContent);
        $this->assertStringContainsString('TAG DELETE FAILED', $logContent);
        $this->assertStringContainsString('DB error:', $logContent);
        $this->assertStringContainsString('from articles: 500, 501, 502', $logContent);
    }

    /**
     * Test hierarchical operation logging
     */
    public function testHierarchicalOperationLogging()
    {
        $logger = new ComponentLogger('test_hierarchy');

        // Test parent-child relationship logging
        $logger->info("TAG CREATE CHILD SUCCESS: Created new child tag 211501 under parent 2115 with name 'Child Category' (slug: 'child-category')");
        $logger->info("SAME PARENT REORDERING: Tag 211502 within parent 2115 from position to position 3");
        $logger->info("PARENT CHANGE: Tag 211503 -> 211601 affects articles: 600, 601, 602");
        $logger->info("PARENT CHANGE: Updated blog_cat table - changed tag ID from 211503 to 211601");
        $logger->info("PARENT CHANGE: Updated blog_tag table - changed tag references from 211503 to 211601 for 3 articles");

        $logContent = file_get_contents($logger->getLogFile());

        // Verify hierarchical operation patterns
        $this->assertStringContainsString('TAG CREATE CHILD SUCCESS', $logContent);
        $this->assertStringContainsString('under parent 2115', $logContent);
        $this->assertStringContainsString('SAME PARENT REORDERING', $logContent);
        $this->assertStringContainsString('PARENT CHANGE:', $logContent);
        $this->assertStringContainsString('affects articles: 600, 601, 602', $logContent);
        $this->assertStringContainsString('Updated blog_cat table', $logContent);
        $this->assertStringContainsString('Updated blog_tag table', $logContent);
    }

    /**
     * Test that article IDs are properly logged for audit trail
     */
    public function testArticleIdAuditTrail()
    {
        $logger = new ComponentLogger('test_article_audit');

        // Test various article ID logging scenarios
        $logger->info("TAG MOVE OPERATION: Tag 2116 affects 12 articles: 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711");
        $logger->info("TAG DELETE: About to delete 5 tag references for tag 2117 (Audit Tag) from articles: 800, 801, 802, 803, 804");
        $logger->info("TAG DELETE SUCCESS: Deleted 5 tag references for tag 2117 (Audit Tag) from blog_tag table");

        $logContent = file_get_contents($logger->getLogFile());

        // Verify article ID tracking
        $this->assertStringContainsString('affects 12 articles:', $logContent);
        $this->assertStringContainsString('700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711', $logContent);
        $this->assertStringContainsString('from articles: 800, 801, 802, 803, 804', $logContent);
        $this->assertStringContainsString('Deleted 5 tag references', $logContent);

        // Count occurrences to ensure proper logging
        $this->assertEquals(1, substr_count($logContent, 'affects 12 articles'));
        $this->assertEquals(1, substr_count($logContent, 'from articles: 800, 801, 802, 803, 804'));
    }
}
