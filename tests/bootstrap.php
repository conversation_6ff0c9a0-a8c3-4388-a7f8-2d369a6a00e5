<?php
/**
 * PHPUnit Bootstrap file
 * 
 * This file sets up the testing environment by including both
 * Composer's autoloader and the custom CMS autoloader.
 */

// Include Composer's autoloader for PHPUnit and dependencies
require_once __DIR__ . '/../vendor/autoload.php';

// Define constants needed by the CMS autoloader
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

if (!defined('ROOT')) {
    define('ROOT', realpath(__DIR__ . '/..') . DS);
}

// Include the custom CMS autoloader for App classes
require_once __DIR__ . '/../www/autoload.php';
