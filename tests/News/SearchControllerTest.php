<?php
namespace Tests\News;

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\Attributes\CoversNothing;

// Create mock classes for testing
if (!class_exists('MockDB')) {
    class MockDB
    {
        private $whereConditions = [];
        private $selectFields = '';
        private $fromTable = '';
        private $orderBy = '';
        private $bindKeys = [];
        
        public function select($fields = '*') { 
            $this->selectFields = $fields;
            return $this; 
        }
        
        public function from($table) { 
            $this->fromTable = $table;
            return $this; 
        }
        
        public function where($field, $operator = null, $value1 = null, $value2 = null) {
            if ($operator === null) {
                // Single parameter case: where('field = value')
                $this->whereConditions[] = $field;
            } elseif ($value1 === null) {
                // Two parameter case: where('field', 'value')
                $this->whereConditions[] = [$field, '=', $operator];
            } elseif ($value2 === null) {
                // Three parameter case: where('field', 'operator', 'value')
                $this->whereConditions[] = [$field, $operator, $value1];
            } else {
                // Four parameter case: where('field', 'between', 'value1', 'value2')
                $this->whereConditions[] = [$field, $operator, $value1, $value2];
            }
            return $this;
        }
        
        public function order($orderBy) {
            $this->orderBy = $orderBy;
            return $this;
        }
        
        public function pgno($page, &$total = null, &$max = null) {
            $total = 100; // Mock total
            $max = 10;    // Mock max pages
            return []; // Mock results
        }
        
        public function bindKey($key, $value) {
            $this->bindKeys[$key] = $value;
            return ':' . $key;
        }
        
        public function run($query, $params = []) {
            return $this;
        }
        
        public function fetchColumn() {
            return '2101'; // Mock category
        }
        
        public function getWhereConditions() {
            return $this->whereConditions;
        }
        
        public function getLastRegexCondition() {
            foreach (array_reverse($this->whereConditions) as $condition) {
                if (is_array($condition) && count($condition) >= 3) {
                    $operator = strtoupper($condition[1]);
                    if ($operator === 'REGEXP' || $operator === 'NOT REGEXP') {
                        return $condition;
                    }
                }
            }
            return null;
        }

        public function getAllRegexConditions() {
            $regexConditions = [];
            foreach ($this->whereConditions as $condition) {
                if (is_array($condition) && count($condition) >= 3) {
                    $operator = strtoupper($condition[1]);
                    if ($operator === 'REGEXP' || $operator === 'NOT REGEXP') {
                        $regexConditions[] = $condition;
                    }
                }
            }
            return $regexConditions;
        }
        
        public function clearConditions() {
            $this->whereConditions = [];
            $this->bindKeys = [];
        }
    }
}

/**
 * Test class for SearchController functionality
 * Focuses on testing the regex escaping fix for search keywords
 */
#[CoversNothing]
class SearchControllerTest extends TestCase
{
    private $mockDb;
    private $searchController;
    
    protected function setUp(): void
    {
        // Create a mock database object
        $this->mockDb = new MockDB();
        
        // Reset POST/GET data
        $_POST = [];
        $_GET = [];
        
        // Create a test controller that extends the Base class with getKeywSQL method
        $this->searchController = new class($this->mockDb) {
            private $db;
            private $blog = [
                'cat_base' => '21',
                'user_acs' => 5,
                'news_inc_event' => false,
                'cat_event' => '2199'
            ];
            private $uid = null;
            
            public function __construct($db) {
                $this->db = $db;
            }
            
            public function getDb() {
                return $this->db;
            }
            
            public function isStaff() {
                return false;
            }

            public function baseSQL($skip_event_check = 0) {
                $sql = $this->db->from(['n' => 'blog_topic'])
                    ->where('n.cat', 'LIKE', $this->blog['cat_base'] . '%');
                if ($this->isStaff()) {
                    $sql = $sql->where('n.acs < 9');
                } else {
                    $sql = $sql->where('n.acs < 7')->where('n.created', '<', date('Y-m-d H:i:s'))
                        ->where('n.acs_only', [0, $this->blog['user_acs']]);
                }
                return $sql;
            }
            
            // This is the method we're testing - copied from Base.php with our fix
            public function getKeywSQL($sql, $keyw) {
                if (!$keyw || !is_array($keyw) || !isset($keyw[0])) {
                    return $sql;
                }
                $col = "concat(n.headline, ' ', n.summary, ' ', n.txt)";
                foreach ($keyw['AND'] as $k) {
                    if (strpos($k, '*') !== false) {
                        $sql = $sql->where($col, 'LIKE', '%' . str_replace('*', '%', $k) . '%');
                    } else {
                        $sql = $this->addSafeRegexCondition($sql, $col, 'REGEXP', $k);
                    }
                }
                foreach ($keyw['NOT'] as $k) {
                    if (strpos($k, '*') !== false) {
                        $sql = $sql->where($col, 'NOT LIKE', '%' . str_replace('*', '%', $k) . '%');
                    } else {
                        $sql = $this->addSafeRegexCondition($sql, $col, 'NOT REGEXP', $k);
                    }
                }
                if ($keyw['OR']) {
                    foreach ($keyw['OR'] as $k => $v) {
                        if (strpos($k, '*') !== false) {
                            $keyw['OR'][$k] = [$col, 'LIKE', '%' . str_replace('*', '%', $v) . '%'];
                        } else {
                            $keyw['OR'][$k] = $this->getSafeRegexCondition($col, 'REGEXP', $v);
                        }
                    }
                    $sql = $sql->where('OR', $keyw['OR']);
                }
                return $sql;
            }

            private function addSafeRegexCondition($sql, $col, $operator, $keyword) {
                if ($this->isRegexSafe($keyword)) {
                    $escapedK = preg_quote($keyword, '/');
                    return $sql->where($col, $operator, '[[:<:]]' . $escapedK . '[[:>:]]');
                } else {
                    $likeOperator = $operator === 'NOT REGEXP' ? 'NOT LIKE' : 'LIKE';
                    return $sql->where($col, $likeOperator, '%' . $keyword . '%');
                }
            }

            private function getSafeRegexCondition($col, $operator, $keyword) {
                if ($this->isRegexSafe($keyword)) {
                    $escapedK = preg_quote($keyword, '/');
                    return [$col, $operator, '[[:<:]]' . $escapedK . '[[:>:]]'];
                } else {
                    $likeOperator = $operator === 'NOT REGEXP' ? 'NOT LIKE' : 'LIKE';
                    return [$col, $likeOperator, '%' . $keyword . '%'];
                }
            }

            private function isRegexSafe($keyword) {
                if (preg_match('/\[:[a-z]+:\]/', $keyword)) {
                    return false;
                }
                if (strlen($keyword) > 100) {
                    return false;
                }
                if (preg_match('/^[\[\]{}()*+?.\\\\^$|]+$/', $keyword)) {
                    return false;
                }
                if (preg_match('/(.)\1{20,}/', $keyword)) {
                    return false;
                }
                return true;
            }
        };
    }
    
    /**
     * Test that normal keywords work without escaping
     */
    public function testNormalKeywordsWork()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $keyw = [
            0 => 'climate AND change environment -politics', // This is required by getKeywSQL
            'AND' => ['climate', 'change'],
            'OR' => ['environment'],
            'NOT' => ['politics']
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        $conditions = $this->mockDb->getWhereConditions();

        // Debug: Print all conditions to see what's happening
        // var_dump('Keyword array:', $keyw);
        // var_dump('All conditions:', $conditions);

        // Should have conditions for AND, NOT, and OR
        $this->assertGreaterThan(3, count($conditions));

        // Check that we have regex conditions
        $regexConditions = $this->mockDb->getAllRegexConditions();
        $this->assertGreaterThan(0, count($regexConditions), 'Should have at least one REGEXP condition');

        // Check that normal words are properly escaped (but look the same)
        $regexCondition = $this->mockDb->getLastRegexCondition();
        $this->assertNotNull($regexCondition, 'Should have a REGEXP condition');
        $this->assertStringContainsString('[[:<:]]', $regexCondition[2]);
        $this->assertStringContainsString('[[:>:]]', $regexCondition[2]);
    }
    
    /**
     * Test that keywords with parentheses are properly escaped
     */
    public function testKeywordsWithParenthesesAreEscaped()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $keyw = [
            0 => 'test(ing)',
            'AND' => ['test(ing)'],
            'OR' => [],
            'NOT' => []
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        $regexCondition = $this->mockDb->getLastRegexCondition();
        $this->assertNotNull($regexCondition);

        // Parentheses should be escaped
        $this->assertStringContainsString('[[:<:]]test\\(ing\\)[[:>:]]', $regexCondition[2]);
        $this->assertStringNotContainsString('test(ing)', $regexCondition[2]);
    }
    
    /**
     * Test that keywords with square brackets are properly escaped
     */
    public function testKeywordsWithSquareBracketsAreEscaped()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $keyw = [
            0 => 'data[analysis]',
            'AND' => ['data[analysis]'],
            'OR' => [],
            'NOT' => []
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        $regexCondition = $this->mockDb->getLastRegexCondition();
        $this->assertNotNull($regexCondition);

        // Square brackets should be escaped
        $this->assertStringContainsString('[[:<:]]data\\[analysis\\][[:>:]]', $regexCondition[2]);
    }

    /**
     * Test that keywords with plus signs are properly escaped
     */
    public function testKeywordsWithPlusSignsAreEscaped()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $keyw = [
            0 => 'cost+benefit',
            'AND' => ['cost+benefit'],
            'OR' => [],
            'NOT' => []
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        $regexCondition = $this->mockDb->getLastRegexCondition();
        $this->assertNotNull($regexCondition);

        // Plus signs should be escaped
        $this->assertStringContainsString('[[:<:]]cost\\+benefit[[:>:]]', $regexCondition[2]);
    }

    /**
     * Test that keywords with question marks are properly escaped
     */
    public function testKeywordsWithQuestionMarksAreEscaped()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $keyw = [
            0 => 'question?mark',
            'AND' => ['question?mark'],
            'OR' => [],
            'NOT' => []
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        $regexCondition = $this->mockDb->getLastRegexCondition();
        $this->assertNotNull($regexCondition);

        // Question marks should be escaped
        $this->assertStringContainsString('[[:<:]]question\\?mark[[:>:]]', $regexCondition[2]);
    }

    /**
     * Test that keywords with asterisks use LIKE instead of REGEXP
     */
    public function testKeywordsWithAsterisksUseLike()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $keyw = [
            0 => 'test*',
            'AND' => ['test*'],
            'OR' => [],
            'NOT' => []
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        $conditions = $this->mockDb->getWhereConditions();

        // Find the LIKE condition (skip the base SQL conditions)
        $likeCondition = null;
        foreach ($conditions as $condition) {
            if (is_array($condition) && count($condition) >= 3 &&
                strtoupper($condition[1]) === 'LIKE' &&
                strpos($condition[2], '%test%') !== false) {
                $likeCondition = $condition;
                break;
            }
        }

        $this->assertNotNull($likeCondition);
        // The asterisk gets replaced with %, so test* becomes %test%
        $this->assertStringContainsString('%test%', $likeCondition[2]);
    }

    /**
     * Test NOT conditions with special characters
     */
    public function testNotConditionsWithSpecialCharacters()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $keyw = [
            0 => '-avoid(this)',
            'AND' => [],
            'OR' => [],
            'NOT' => ['avoid(this)']
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        $conditions = $this->mockDb->getWhereConditions();

        // Find the NOT REGEXP condition
        $notRegexCondition = null;
        foreach ($conditions as $condition) {
            if (is_array($condition) && count($condition) >= 3 &&
                strtoupper($condition[1]) === 'NOT REGEXP') {
                $notRegexCondition = $condition;
                break;
            }
        }

        $this->assertNotNull($notRegexCondition);
        $this->assertStringContainsString('[[:<:]]avoid\\(this\\)[[:>:]]', $notRegexCondition[2]);
    }

    /**
     * Test OR conditions with special characters
     */
    public function testOrConditionsWithSpecialCharacters()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $keyw = [
            0 => 'option(1) option[2]',
            'AND' => [],
            'OR' => ['option(1)', 'option[2]'],
            'NOT' => []
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        $conditions = $this->mockDb->getWhereConditions();

        // Find the OR condition
        $orCondition = null;
        foreach ($conditions as $condition) {
            if (is_array($condition) && count($condition) >= 2 &&
                $condition[0] === 'OR') {
                $orCondition = $condition;
                break;
            }
        }

        $this->assertNotNull($orCondition);
        $this->assertIsArray($orCondition[2]);

        // Check that the OR conditions contain escaped regex patterns
        $orConditions = $orCondition[2];
        $foundEscapedParens = false;
        $foundEscapedBrackets = false;

        foreach ($orConditions as $cond) {
            if (is_array($cond) && count($cond) >= 3) {
                if (strpos($cond[2], 'option\\(1\\)') !== false) {
                    $foundEscapedParens = true;
                }
                if (strpos($cond[2], 'option\\[2\\]') !== false) {
                    $foundEscapedBrackets = true;
                }
            }
        }

        $this->assertTrue($foundEscapedParens, 'Parentheses should be escaped in OR conditions');
        $this->assertTrue($foundEscapedBrackets, 'Square brackets should be escaped in OR conditions');
    }

    /**
     * Test complex search with multiple special characters
     */
    public function testComplexSearchWithMultipleSpecialCharacters()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $keyw = [
            0 => 'test(1) AND data[set] option+1 choice?2 -exclude(this)',
            'AND' => ['test(1)', 'data[set]'],
            'OR' => ['option+1', 'choice?2'],
            'NOT' => ['exclude(this)']
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        $conditions = $this->mockDb->getWhereConditions();

        // Should have multiple conditions (base SQL + search conditions)
        $this->assertGreaterThan(7, count($conditions));

        // Get all regex conditions and check they contain escaped characters
        $regexConditions = $this->mockDb->getAllRegexConditions();
        $this->assertGreaterThan(0, count($regexConditions));

        // Also check OR conditions which are stored differently
        $orConditions = [];
        foreach ($conditions as $condition) {
            if (is_array($condition) && count($condition) >= 3 && $condition[0] === 'OR') {
                $orConditions = $condition[2];
                break;
            }
        }

        // Check that special characters are properly escaped in regex patterns
        $foundEscapedParens = false;
        $foundEscapedBrackets = false;
        $foundEscapedPlus = false;
        $foundEscapedQuestion = false;

        // Check regular regex conditions
        foreach ($regexConditions as $condition) {
            $pattern = $condition[2];
            if (strpos($pattern, 'test\\(1\\)') !== false) $foundEscapedParens = true;
            if (strpos($pattern, 'data\\[set\\]') !== false) $foundEscapedBrackets = true;
        }

        // Check OR conditions
        foreach ($orConditions as $orCondition) {
            if (is_array($orCondition) && count($orCondition) >= 3) {
                $pattern = $orCondition[2];
                if (strpos($pattern, 'option\\+1') !== false) $foundEscapedPlus = true;
                if (strpos($pattern, 'choice\\?2') !== false) $foundEscapedQuestion = true;
            }
        }

        $this->assertTrue($foundEscapedParens, 'Parentheses should be escaped');
        $this->assertTrue($foundEscapedBrackets, 'Square brackets should be escaped');
        $this->assertTrue($foundEscapedPlus, 'Plus signs should be escaped');
        $this->assertTrue($foundEscapedQuestion, 'Question marks should be escaped');
    }

    /**
     * Test empty keyword array
     */
    public function testEmptyKeywordArray()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $keyw = [
            0 => '',
            'AND' => [],
            'OR' => [],
            'NOT' => []
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        // Should return the original SQL without adding regex conditions
        $this->assertSame($sql, $result);
    }

    /**
     * Test null keyword parameter
     */
    public function testNullKeywordParameter()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();

        $result = $this->searchController->getKeywSQL($sql, null);

        // Should return the original SQL
        $this->assertSame($sql, $result);
    }

    /**
     * Test POSIX character class patterns fall back to LIKE
     */
    public function testPosixCharacterClassesFallBackToLike()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $keyw = [
            0 => '[:alpha:]',
            'AND' => ['[:alpha:]'],
            'OR' => [],
            'NOT' => []
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        $conditions = $this->mockDb->getWhereConditions();

        // Should use LIKE instead of REGEXP for POSIX character classes
        $likeCondition = null;
        foreach ($conditions as $condition) {
            if (is_array($condition) && count($condition) >= 3 &&
                strtoupper($condition[1]) === 'LIKE' &&
                strpos($condition[2], '[:alpha:]') !== false) {
                $likeCondition = $condition;
                break;
            }
        }

        $this->assertNotNull($likeCondition, 'POSIX character classes should use LIKE');
        $this->assertStringContainsString('%[:alpha:]%', $likeCondition[2]);
    }

    /**
     * Test very long keywords fall back to LIKE
     */
    public function testVeryLongKeywordsFallBackToLike()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $longKeyword = str_repeat('a', 150); // 150 characters
        $keyw = [
            0 => $longKeyword,
            'AND' => [$longKeyword],
            'OR' => [],
            'NOT' => []
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        $conditions = $this->mockDb->getWhereConditions();

        // Should use LIKE instead of REGEXP for very long keywords
        $likeCondition = null;
        foreach ($conditions as $condition) {
            if (is_array($condition) && count($condition) >= 3 &&
                strtoupper($condition[1]) === 'LIKE' &&
                strpos($condition[2], $longKeyword) !== false) {
                $likeCondition = $condition;
                break;
            }
        }

        $this->assertNotNull($likeCondition, 'Very long keywords should use LIKE');
    }

    /**
     * Test keywords with only special characters fall back to LIKE
     */
    public function testSpecialCharacterOnlyKeywordsFallBackToLike()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $specialChars = '[]{}()*+?.\\^$|';
        $keyw = [
            0 => $specialChars,
            'AND' => [$specialChars],
            'OR' => [],
            'NOT' => []
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        $conditions = $this->mockDb->getWhereConditions();

        // Should use LIKE instead of REGEXP for special character only keywords
        $likeCondition = null;
        foreach ($conditions as $condition) {
            if (is_array($condition) && count($condition) >= 3 &&
                strtoupper($condition[1]) === 'LIKE' &&
                strpos($condition[2], '%[]{}()%') !== false) { // Look for part of the special chars
                $likeCondition = $condition;
                break;
            }
        }

        $this->assertNotNull($likeCondition, 'Special character only keywords should use LIKE');
    }

    /**
     * Test keywords with excessive repetition fall back to LIKE
     */
    public function testExcessiveRepetitionKeywordsFallBackToLike()
    {
        $this->mockDb->clearConditions();

        $sql = $this->searchController->baseSQL();
        $repetitiveKeyword = 'a' . str_repeat('a', 25); // 26 repeated 'a's
        $keyw = [
            0 => $repetitiveKeyword,
            'AND' => [$repetitiveKeyword],
            'OR' => [],
            'NOT' => []
        ];

        $result = $this->searchController->getKeywSQL($sql, $keyw);

        $conditions = $this->mockDb->getWhereConditions();

        // Should use LIKE instead of REGEXP for repetitive keywords
        $likeCondition = null;
        foreach ($conditions as $condition) {
            if (is_array($condition) && count($condition) >= 3 &&
                strtoupper($condition[1]) === 'LIKE' &&
                strpos($condition[2], $repetitiveKeyword) !== false) {
                $likeCondition = $condition;
                break;
            }
        }

        $this->assertNotNull($likeCondition, 'Repetitive keywords should use LIKE');
    }
}
