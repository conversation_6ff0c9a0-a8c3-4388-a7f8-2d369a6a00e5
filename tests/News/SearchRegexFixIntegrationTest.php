<?php
namespace Tests\News;

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\Attributes\CoversNothing;

// Include the Data class for testing
require_once __DIR__ . '/../../vendor/topnew/src/Data.php';

/**
 * Integration test to verify the regex escaping fix works end-to-end
 * This test simulates the actual search flow that was causing production errors
 */
#[CoversNothing]
class SearchRegexFixIntegrationTest extends TestCase
{
    /**
     * Test that problematic search terms that caused production errors now work
     */
    public function testProblematicSearchTermsNowWork()
    {
        // These are examples of search terms that would have caused production errors
        $problematicSearchTerms = [
            'test(ing)',                                    // Original "missing closing parenthesis" error
            'data[analysis]',                               // Square brackets
            'cost+benefit',                                 // Plus signs
            'question?mark',                                // Question marks
            'complex(test)[with]{multiple}+special?characters', // Multiple special chars
            'carbon(dioxide)',                              // Parentheses
            'data[set]',                                    // Square brackets
            'option(1)',                                    // Parentheses
            'choice[2]',                                    // Square brackets
            '[:alpha:]',                                    // POSIX character class (new error)
            '[:digit:]',                                    // POSIX character class
            '[:space:]',                                    // POSIX character class
            '[]{}()*+?.\\^$|',                             // Special characters only
            str_repeat('a', 150),                          // Very long keyword
            'a' . str_repeat('a', 25)                      // Excessive repetition
        ];
        
        foreach ($problematicSearchTerms as $searchTerm) {
            // Step 1: Clean the keyword using Data::cleanKeyw (this is what happens in SearchController)
            $cleanedKeywords = \Topnew\Data::cleanKeyw($searchTerm);
            
            // Step 2: Verify the structure is correct
            $this->assertIsArray($cleanedKeywords, "cleanKeyw should return array for term: $searchTerm");
            $this->assertArrayHasKey('AND', $cleanedKeywords);
            $this->assertArrayHasKey('OR', $cleanedKeywords);
            $this->assertArrayHasKey('NOT', $cleanedKeywords);
            $this->assertArrayHasKey(0, $cleanedKeywords);
            
            // Step 3: Test that our getKeywSQL method can handle these keywords without error
            $mockDb = new MockDBForIntegration();
            $searchController = new SearchControllerForIntegration($mockDb);
            
            $sql = $searchController->baseSQL();
            
            // This should not throw an exception (the original bug would cause a PDO exception)
            try {
                $result = $searchController->getKeywSQL($sql, $cleanedKeywords);
                $this->assertTrue(true, "getKeywSQL should handle term '$searchTerm' without exception");
            } catch (\Exception $e) {
                $this->fail("getKeywSQL threw exception for term '$searchTerm': " . $e->getMessage());
            }
            
            // Step 4: Verify that special characters are properly escaped in the generated regex
            $regexConditions = $mockDb->getAllRegexConditions();
            
            if (!empty($regexConditions)) {
                foreach ($regexConditions as $condition) {
                    $pattern = $condition[2];
                    
                    // The pattern should contain escaped special characters
                    if (strpos($searchTerm, '(') !== false) {
                        $this->assertStringContainsString('\\(', $pattern, "Parentheses should be escaped in pattern for term: $searchTerm");
                    }
                    if (strpos($searchTerm, '[') !== false) {
                        $this->assertStringContainsString('\\[', $pattern, "Square brackets should be escaped in pattern for term: $searchTerm");
                    }
                    if (strpos($searchTerm, '+') !== false) {
                        $this->assertStringContainsString('\\+', $pattern, "Plus signs should be escaped in pattern for term: $searchTerm");
                    }
                    if (strpos($searchTerm, '?') !== false) {
                        $this->assertStringContainsString('\\?', $pattern, "Question marks should be escaped in pattern for term: $searchTerm");
                    }
                }
            }
            
            $mockDb->clearConditions();
        }
    }
    
    /**
     * Test that the fix doesn't break normal search terms
     */
    public function testNormalSearchTermsStillWork()
    {
        $normalSearchTerms = [
            'climate',
            'environment',
            'sustainability'
        ];

        foreach ($normalSearchTerms as $searchTerm) {
            $cleanedKeywords = \Topnew\Data::cleanKeyw($searchTerm);

            $mockDb = new MockDBForIntegration();
            $searchController = new SearchControllerForIntegration($mockDb);

            $sql = $searchController->baseSQL();

            // Should work without exception
            try {
                $result = $searchController->getKeywSQL($sql, $cleanedKeywords);
                $this->assertTrue(true, "Normal search term '$searchTerm' should work");
            } catch (\Exception $e) {
                $this->fail("Normal search term '$searchTerm' threw exception: " . $e->getMessage());
            }

            $mockDb->clearConditions();
        }
    }
}

/**
 * Simplified mock DB for integration testing
 */
class MockDBForIntegration
{
    private $whereConditions = [];
    
    public function from($table) { return $this; }
    public function where($field, $operator = null, $value1 = null, $value2 = null) { 
        if ($operator === null) {
            $this->whereConditions[] = $field;
        } elseif ($value1 === null) {
            $this->whereConditions[] = [$field, '=', $operator];
        } elseif ($value2 === null) {
            $this->whereConditions[] = [$field, $operator, $value1];
        } else {
            $this->whereConditions[] = [$field, $operator, $value1, $value2];
        }
        return $this; 
    }
    
    public function getAllRegexConditions() {
        $regexConditions = [];
        foreach ($this->whereConditions as $condition) {
            if (is_array($condition) && count($condition) >= 3) {
                $operator = strtoupper($condition[1]);
                if ($operator === 'REGEXP' || $operator === 'NOT REGEXP') {
                    $regexConditions[] = $condition;
                }
            }
        }
        return $regexConditions;
    }
    
    public function clearConditions() {
        $this->whereConditions = [];
    }
}

/**
 * Simplified search controller for integration testing
 */
class SearchControllerForIntegration
{
    private $db;
    
    public function __construct($db) {
        $this->db = $db;
    }
    
    public function baseSQL() {
        return $this->db->from(['n' => 'blog_topic'])
            ->where('n.cat', 'LIKE', '21%');
    }
    
    public function getKeywSQL($sql, $keyw) {
        if (!$keyw || !is_array($keyw) || !isset($keyw[0])) {
            return $sql;
        }
        $col = "concat(n.headline, ' ', n.summary, ' ', n.txt)";
        foreach ($keyw['AND'] as $k) {
            if (strpos($k, '*') !== false) {
                $sql = $sql->where($col, 'LIKE', '%' . str_replace('*', '%', $k) . '%');
            } else {
                $sql = $this->addSafeRegexCondition($sql, $col, 'REGEXP', $k);
            }
        }
        foreach ($keyw['NOT'] as $k) {
            if (strpos($k, '*') !== false) {
                $sql = $sql->where($col, 'NOT LIKE', '%' . str_replace('*', '%', $k) . '%');
            } else {
                $sql = $this->addSafeRegexCondition($sql, $col, 'NOT REGEXP', $k);
            }
        }
        if ($keyw['OR']) {
            foreach ($keyw['OR'] as $k => $v) {
                if (strpos($k, '*') !== false) {
                    $keyw['OR'][$k] = [$col, 'LIKE', '%' . str_replace('*', '%', $v) . '%'];
                } else {
                    $keyw['OR'][$k] = $this->getSafeRegexCondition($col, 'REGEXP', $v);
                }
            }
            $sql = $sql->where('OR', $keyw['OR']);
        }
        return $sql;
    }

    private function addSafeRegexCondition($sql, $col, $operator, $keyword) {
        if ($this->isRegexSafe($keyword)) {
            $escapedK = preg_quote($keyword, '/');
            return $sql->where($col, $operator, '[[:<:]]' . $escapedK . '[[:>:]]');
        } else {
            $likeOperator = $operator === 'NOT REGEXP' ? 'NOT LIKE' : 'LIKE';
            return $sql->where($col, $likeOperator, '%' . $keyword . '%');
        }
    }

    private function getSafeRegexCondition($col, $operator, $keyword) {
        if ($this->isRegexSafe($keyword)) {
            $escapedK = preg_quote($keyword, '/');
            return [$col, $operator, '[[:<:]]' . $escapedK . '[[:>:]]'];
        } else {
            $likeOperator = $operator === 'NOT REGEXP' ? 'NOT LIKE' : 'LIKE';
            return [$col, $likeOperator, '%' . $keyword . '%'];
        }
    }

    private function isRegexSafe($keyword) {
        if (preg_match('/\[:[a-z]+:\]/', $keyword)) {
            return false;
        }
        if (strlen($keyword) > 100) {
            return false;
        }
        if (preg_match('/^[\[\]{}()*+?.\\\\^$|]+$/', $keyword)) {
            return false;
        }
        if (preg_match('/(.)\1{20,}/', $keyword)) {
            return false;
        }
        return true;
    }
}
