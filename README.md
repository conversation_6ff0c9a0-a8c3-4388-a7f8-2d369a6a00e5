# WebApp2021

A bespoke CMS / news publishing system that provides current, concise and independently researched news and analysis to paid subscribers via email (instant, daily and weekly).

## Blog Site IDs

 * 	**#6 :** Footprint - [footprintnews.com.au](https://www.footprintnews.com.au/)
 * 	**#7 :** HR Daily - [hrdaily.com.au](https://www.hrdaily.com.au/)
 * 	**#2 :** OHS Alert - [ohsalert.com.au](https://www.ohsalert.com.au/)
 * 	**#3 :** Shortlist - [shortlist.net.au](https://www.shortlist.net.au/)
 * 	**#4 :** Workplace Express - [workplaceexpress.com.au](https://www.workplaceexpress.com.au/)

## Dependencies (Composer)

### Local (included in this repo)
 * 	/vendor/payway (Westpac Qvalent PayWay Cards API for PHP, excludes certificates)
 * 	/vendor/topnew (Topnew CMS Framework)

### 3rd party


 * dompdf/dompdf: v2.0.3 - https://github.com/dompdf/dompdf/releases 
   DOMPDF is a CSS 2.1 compliant HTML to PDF converter
    - masterminds/html5: v2.8.0 - https://github.com/Masterminds/html5-php - An HTML5 parser and serializer.
    - phenx/php-font-lib: v0.5.4 - A library to read, parse, export and make subsets of different types of font files.
    - phenx/php-svg-lib: v0.5.0 - A library to read, parse and export to PDF SVG files.
    - sabberworm/php-css-parser: v8.4.0 - Parser for CSS Files written in PHP

 * michelf/php-markdown: v2.0.0 - https://github.com/michelf/php-markdown
   PHP Markdown

 * phpmailer/phpmailer: v6.8.0 - https://github.com/PHPMailer/PHPMailer/blob/master/changelog.md
   PHPMailer is a full-featured email creation and transfer class for PHP

 * phpspec/php-diff: v1.1.3 - (is this still required?)
   A comprehensive library for generating differences between two hashable objects (strings or arrays).

 * twig/twig: v3.6.0 - https://github.com/twigphp/Twig/blob/3.x/CHANGELOG
   Twig, the flexible, fast, and secure template language for PHP
    - symfony/polyfill-ctype: v1.27.0 - Symfony polyfill for ctype functions
    - symfony/polyfill-mbstring v1.27.0 - Symfony polyfill for the Mbstring extension

 * twig/markdown-extra: v3.6.0 - https://github.com/twigphp/markdown-extra
   A Twig extension for Markdown

## Site Specific Configuration / Templates

 * Front end templates (assets span multiple directories)
   - /public_html/ (js, css, img, .htaccess)
   - /View
 * Config files
   - /public_html/config.php
   - /app/News/Cli/Base.php
 * Crontab: Email sending / reports etc
