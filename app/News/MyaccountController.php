<?php
namespace App\News;

use App\News\HtmlHelper;
use Topnew\Data;

class MyaccountController extends Base
{
    use TraitInv;

    public function home($uid = 0) {
        if (!$this->uid) {
            header('location:/login');
            exit;
        }

        $this->view = '/' . $this->blog['site']['template'] . '/view/Myaccount/home.tpl';
        $this->set('page.title', 'My Account');
        $this->set('page.desc', '');
        $uid = ceil($uid);
        if ($uid < 1 || $this->blog['user_acs'] != 9) {
            $uid = $this->uid; // admin can check other users
        }

        $this->saveEmailAlert($uid);

        $cust = $this->db->from('blog_user')->where('uid', $uid)->row();
        if (!$cust) {
            return;
        }
        $company = $this->db->select('*')
        ->from('inv_purchaser')->where('id', $cust['purchaser'])->row();
        $this->set('cust', $cust);
        $this->set('company', $company);
        $this->setBookmark($uid);
        $this->setHistory($uid);
        $this->set('msg_exp', $this->getMsg($cust));
        if ($cust['is_admin']) {
            $this->setInvoice($cust['purchaser']);
        }
        $this->set('cust.state_options', HtmlHelper::addrState($cust['state']));
    }

    public function bookmark($cmd = 'add', $tid = 0) {
        $this->view = false;
        $tid = ceil($tid);
        if ($tid < 1 || $this->uid < 1) {
            return;
        }
        $lid = $this->db->select('lid')->from('blog_log')
            ->where('tid', $tid)
            ->where('uid', $this->uid)
            ->where('log_type', 7)
            ->val();
        if ($cmd == 'del' && $lid) {
            $this->db->where('lid', $lid)->delete('blog_log');
            $this->db->where('tid', $tid)
                ->where('cat', '!=', $this->blog['cat_event'])
                ->update('blog_topic', 'rank = rank - 1');
        } elseif ($cmd == 'add' && !$lid) {
            $this->db->insert('blog_log', ['uid' => $this->uid, 'tid' => $tid, 'log_type' => 7]);
            $this->db->where('tid', $tid)
                ->where('cat', '!=', $this->blog['cat_event'])
                ->update('blog_topic', 'rank = rank + 1');
        }
    }

    public function edit($uid = 0) {
        $uid = ceil($uid);
        if ($uid < 1 || $this->blog['user_acs'] != 9) {
            $uid = $this->uid; // admin can check other users
        }
        $data = [
            'lname', 'phone', 'mobile', 'company', 'position',
            'addr', 'city', 'state', 'zip',
        ];
        $data = Data::clean($data);
        $cust = $this->db->from('blog_user')->where('uid', $uid)->row();
        $spam_list = explode(',', $this->blog['spam_list']);
        foreach ($data as $k => $v) {
            if ($cust[$k] == $v) {
                unset($data[$k]);
            } else {
                foreach ($spam_list as $s) {
                    if (stripos($v, $s) !== false) {
                        unset($data[$k]);
                    }
                }
            }
        }
        if ($data) {
            $this->db->where('uid', $uid)->update('blog_user', $data);
            $ip = Data::ip();
            foreach ($data as $k => $v) {
                $this->db->insert('inv_log', [
                    'txt' => '<b>' . $k . '</b> updated from <b>' . $cust[$k] . '</b> to <b>' . $v . '</b>',
                    'uid' => $cust['uid'],
                    'by_uid' => $this->uid,
                    'status' => 'Sys',
                    'purchaser' => $cust['purchaser'],
                    'ip' => $ip,
                ]);
            }
        }
        header('location:/myaccount/' . ($this->blog['user_acs'] == 9 ? $uid : ''));
        exit;
    }

    public function pdf($inv_id = 0) {
        if (!$inv_id || !$this->get('user.is_admin')) {
            exit;
        }
        $inv = $this->db->from('inv_invoice')->where('inv_id', $inv_id)->row();
        if (!$inv || $inv['purchaser'] != $this->get('user.purchaser') || $inv['purchaser'] < 10) {
            exit;
        }
        $this->getPdf($inv);
    }

    private function getMsg($cust) {
        if ($this->isStaff()) {
            return '';
        } elseif ($cust['acs'] == 2) {
            $trial = max($cust['trial1'], $cust['trial2'], $cust['trial3']);
            if ($trial && $trial != '0000-00-00') {
                $trial = strtotime($trial . ' +28 day');
                if ($trial < strtotime('today')) {
                    return '<p class="alert alert-danger">Your freetrial is expired on<br><b>'
                        . date('d/m/Y', $trial)
                        . '</b><br><a href="/subscribe">Please click here to subscribe</a></p>';
                } else {
                    return '<p class="alert alert-info">Your freetrial will expire on<br><b>'
                        . date('d/m/Y', $trial) . '</b></p>';
                }
            }
            return;
        } elseif ($cust['acs'] != 3) {
            return;
        }
        // the following is subscribers
        $last_inv = $this->db->from('inv_invoice')
            ->where('purchaser', $cust['purchaser'])
            ->where('status', ['Paid', 'Draft', 'Unpaid'])
            ->order('inv_date DESC')
            ->limit(1)
            ->row();

        if($last_inv) {
            $pay = '/pay/' . $last_inv['inv_id'] . '/' . $this->payCode($last_inv);
            $date_fm = date('d/m/Y', strtotime($last_inv['date_fm']));
            if ($last_inv['status'] == 'Paid') {
                return '<p class="alert alert-success">Your subscription is from <b>' . $date_fm . '</b><br>and '
                    . ($last_inv['date_to'] > date('Y-m-d') ? 'will expire' : 'is expired')
                    . ' on <b>' . date('d/m/Y', strtotime($last_inv['date_to'])) . '</b></p>';
            } elseif (in_array($last_inv['status'], ['Draft', 'Unpaid'])) {
                if (substr($last_inv['inv_date'], 0, 10) == $last_inv['date_due']) {
                    return '<p class="alert alert-danger">Your subscription has not been paid yet.'
                        . '<br>Due on <b>' . date('d/m/Y', strtotime($last_inv['date_due'])) . '</b>'
                        . '<br>Please <a href="' . $pay . '">click here to pay</a></p>';
                }
                return '<p class="alert alert-warning">Your subscription '
                    . ($last_inv['date_fm'] > date('Y-m-d') ? 'will expire' : 'is expired')
                    . ' on<br><b>' . $date_fm . '</b>'
                    . '<br>Please <a href="' . $pay . '">click here to pay renewal fee</a></p>';
            }
        }
    }

    private function saveEmailAlert($uid) {
        if (!isset($_GET['email'])) {
            return;
        }
        $email = trim($_GET['email']);
        if (!in_array($email, ['Instant', 'Daily', 'Weekly'])) {
            return;
        }
        $this->db->where('uid', $uid)->update('blog_user', ['email_repeat' => $email]);
        $this->db->insert('inv_log', [
            'uid' => $uid,
            'by_uid' => $this->uid,
            'status' => 'Sys',
            'created' => date('Y-m-d H:i:s'),
            'txt' => '<b>Email Repeat</b> changed to <b>' . $email . '</b>',
            'ip' => Data::ip(),
        ]);
    }

    private function setBookmark($uid) {
        $this->set('bookmark', $this->baseSQL()->select('n.tid, n.headline txt, n.created ts, l.created')
            ->join(['l' => 'blog_log'], 'l.tid = n.tid')
            ->where('l.log_type', 7) // bookmark
            ->where('l.uid', $uid)
            ->where('l.uid > 0')
            ->order('l.created DESC')
            ->limit(100)
            ->arr()
        );
    }

    private function setHistory($uid) {
        $this->set('history', $this->baseSQL()->select('n.tid, n.headline txt, max(l.created) created, n.created ts')
            ->join(['l' => 'blog_log'], 'l.tid = n.tid')
            ->where('l.log_type', 'like', '1%')
            ->where('l.uid', $uid)
            ->where('l.uid > 0')
            ->group(1)
            ->order('3 DESC')
            ->limit(50)
            ->arr()
        );
    }

    private function setInvoice($purchaser) {
        if ($purchaser < 10) {
            return;
        }

        $this->set('inv', $this->db
            ->select('inv_id, amt, status, qty, date_fm, date_to, date_due')
            ->from('inv_invoice')
            ->where('purchaser', $purchaser)
            ->where('status', ['Paid', 'Unpaid'])
            ->order('inv_date DESC')
            ->limit(10)
            ->rows()
        );
    }
}
