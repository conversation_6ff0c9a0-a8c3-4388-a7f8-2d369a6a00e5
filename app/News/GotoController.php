<?php
namespace App\News;

use Topnew\Auth;

class GotoController extends Base
{
    public function home($url = '') {
        $url = Auth::sdec($url);
        header('location:' . ($url ?: '/'));
        exit;
    }

    public function cli($tracker = '') {
        //goto/cli?q=blog_email.id-camp_id-uid-url
        //or after 2019-12-12 as next:
        //goto/cli/blog_email.id-camp_id-uid?q=url
        if ($tracker) {
            $arr = explode('-', $tracker, 3);
            $arr[3] = isset($_GET['q']) ? $_GET['q'] : '';
        } else {
            // this method sent out via email between 2019-11-11 to 2019-12-11
            // can be deprecated after 6 months
            $arr = explode('-', isset($_GET['q']) ? $_GET['q'] : '', 4);
        }
        $url = isset($arr[3]) ? trim($arr[3]) : '';
        if (!$url) {
            exit;
        }
        $uid = isset($arr[2]) && is_numeric($arr[2]) ? ceil($arr[2]) : 0;
        $camp_id = isset($arr[1]) && is_numeric($arr[1]) ? ceil($arr[1]) : 0;
        $id = $uid && $camp_id && isset($arr[0]) && is_numeric($arr[0]) ? ceil($arr[0]) : 0;
        if ($id) {
            $email = $this->db->select('camp_id,uid')->from('blog_email')->where('id', $id)->row();
            if ($email && $email['camp_id'] == $camp_id && $email['uid'] == $uid) {
                $this->db->where('id', $id)->update('blog_email', 'clicked = clicked + 1');
            }
        }
        $url = Auth::sdec($url);
        if (!$url) {
            exit;
        }
        if (substr($url, 0, 7) != 'http://' && substr($url, 0, 8) != 'https://' && $url[0] != '/') {
            $url = '/' . $url;
        }
        header('location:' . $url);
        exit;
    }

    public function img($tracker = '') {
        //goto/img/blog_email.id-camp_id-uid
        $arr = explode('-', $tracker, 3);
        $uid = isset($arr[2]) ? ceil($arr[2]) : 0;
        $camp_id = isset($arr[1]) ? ceil($arr[1]) : 0;
        $id = $uid && $camp_id ? ceil($arr[0]) : 0;
        if ($id) {
            $email = $this->db->select('camp_id,uid')->from('blog_email')->where('id', $id)->row();
            if ($email && $email['camp_id'] == $camp_id && $email['uid'] == $uid) {
                $this->db->where('id', $id)->update('blog_email', 'opened = opened + 1');
            }
        }

        // draw a dot on screen 1px tracker
        header('Content-type: image/png');
        $img = imagecreate(1, 1);
        $bg = imagecolorallocatealpha($img, 0, 0, 0, 127); // transparent
        imagepng($img);
        imagedestroy($img);
        exit;
    }
}
