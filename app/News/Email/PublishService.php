<?php
namespace App\News\Email;

use App\News\Base As NewsBase;

class PublishService extends NewsBase
{
    private $log = [];
    
    public function publishScheduled() {
        // Fetch topic ids that are scheduled in the past but haven't been published yet
        $scheduled_topics = $this->db->select('tid')
            ->from('blog_topic')
            ->where('created <=', date('Y-m-d H:i:s'))
            ->where('acs <=', 6) // Not draft or unpublished
            ->where('NOT EXISTS (
                SELECT 1 FROM blog_email_camp 
                WHERE txt LIKE CONCAT("%/news/", blog_topic.tid, "/%")
            )')
            ->enum();
        
        $count = 0;
        // For each topic, publish it
        foreach ($scheduled_topics as $tid) {
            if ($this->publishTopic($tid)) {
                $count++;
            }
        }
        
        return [
            'count' => $count,
            'message' => "$count scheduled news published",
            'log' => $this->log
        ];
    }
    
    public function publishTopic($tid) {
        if (!$tid) {
            $this->log[] = "Error: Invalid topic ID";
            return false;
        }

        // Get topic data
        $topic = $this->db->select('*')
            ->from('blog_topic')
            ->where('tid', $tid)
            ->row();

        if (!$topic) {
            $this->log[] = "Error: Topic #$tid not found";
            return false;
        }

        // Check if it's a draft
        if ($topic['acs'] > 6) {
            $this->log[] = "Topic #$tid is a draft and cannot be published";
            return false;
        }

        // Check if already published
        if ($this->hasPublished($tid)) {
            $this->log[] = "Topic #$tid has already been published";
            return false;
        }

        // Check if email is enabled for this topic
        if (!$topic['mail']) {
            $this->log[] = "Topic #$tid: Email not enabled, skipping campaign creation";
            return true; // Not an error, just no email needed
        }
        
        // Get email template
        $temp = $this->db
            ->from('blog_email_temp')
            ->where('temp_type', 'Instant')
            ->where('active', 1)
            ->row();

        if (!$temp) {
            $this->log[] = "System Error: No active instant email template found";
            return false;
        }

        // Create campaign immediately (regardless of timing)
        $camp = new Camp();
        $camp_id = $camp->makeCamp($temp, $tid);

        if (!$camp_id) {
            $this->log[] = "Failed to create email campaign for topic #$tid";
            return false;
        }

        // Create email queue with proper timing
        $publishTime = $topic['created']; // Use article's scheduled publish time
        $isScheduled = strtotime($publishTime) > time();

        if ($isScheduled) {
            // Future scheduled: create queue but don't send yet
            $camp->makeQueue($camp_id, $temp, 0, $publishTime);
            $this->log[] = "Campaign #$camp_id created for topic #$tid, scheduled for $publishTime";
        } else {
            // Immediate: create queue and allow sending
            $camp->makeQueue($camp_id, $temp, 0);
            $this->log[] = "Campaign #$camp_id created for topic #$tid, ready for immediate sending";
        }

        return true;
    }
    
    private function hasPublished($tid) {
        // Check if the topic has been published
        $old_tid = $this->blog['site']['id'] == 7 ? 10000 : 80000; // last tid from old system 7=hrd
        return $tid < $old_tid || $this->db->from('blog_email_camp')
            ->where('txt', 'like', '%/news/' . $tid . '/%')->count();
    }
    
    public function getLog() {
        return $this->log;
    }
}
