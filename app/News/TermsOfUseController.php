<?php
namespace App\News;

class TermsOfUseController extends Base
{
    public function home() {
        $this->view = '/' . $this->blog['site']['template'] . '/view/Pages/terms-of-use.tpl';
        $site = $this->blog['site']['id'];
        $title = $desc = '';
        if ($site == 6) {
            $title = 'Footprint terms of use';
            $desc  = 'Terms of use for Footprint subscriptions.';
        }
        $this->set('page.title', $title);
        $this->set('page.desc',  $desc);
    }
}
