<?php
namespace App\News\Admin;

use App\News\HtmlHelper;
use Topnew\Data;
use App\News\Email\Camp;
use App\News\Email\Send;

class EmailTempController extends Base
{
    private $table = 'blog_email_temp';

    public function home($a = '', $b = '') {
        return $this->crud($a, $b);
    }

    public function addCamp($id = 0) {
        $temp = $this->db->from('blog_email_temp')->where('id', $id)->row();
        $err = '';
        if ($temp) {
            $camp = new Camp();
            $camp->makeCamp($temp);
            $log = $camp->getLog();
            $err = '?err=' . urlencode($log);
        }

        header('location:/admin/email-temp/' . $id . $err);
        exit;
    }

    public function testEmail($cid = 0) {
        $camp = $this->db->from('blog_email_camp')->where('id', $cid)->row();
        $err = '';
        if (!$camp) {
            $err = 'Campaign not found';
        } elseif (! $this->db->from('blog_email')->where('camp_id', $cid)->count()) {
            $err = 'Please queue email first';
        } else {
            $email = Data::clean('email')['email'];
            if (!$email) {
                $err = 'Please input email';
            } elseif (! Data::validEmail($email)) {
                $err = 'Email is invalid';
            } else {
                $user = $this->db->select('uid, uid AS euid, fname, lname, email, acs')
                    ->from('blog_user')
                    ->where('email', $email)
                    ->where('acs > 6')
                    ->row();
                if (!$user) {
                    $err = $email . ' is not a staff account';
                }
            }
        }
        if (!$err) {
            $user['eid'] = $this->db->insert('blog_email', [
                'camp_id' => $cid,
                'uid' => $user['uid'],
                'queued_ts' => date('Y-m-d H:i:s'),
            ]) ?: 0;
            $send = new Send();
            $camp['email'] = $this->blog['email'];
            $send->sendMailUser($user, $camp);
            $err = 'Test email has been sent to: ' . $email;
        }
        header('location:/admin/email-temp/' . ($camp ? $camp['temp_id'] : 0) . '?err=' . urlencode($err));
        exit;
    }

    public function delCamp($cid = 0) {
        $cid  = ceil($cid);
        $camp = $this->db->from('blog_email_camp')->where('id', $cid)->row();
        $sent = $this->db->from('blog_email')->where('camp_id', $cid)->where('sent > 0')
            ->where('uid not in (select uid from blog_user where acs>6)')->count();
        if ($sent) {
            $err = 'Sorry, you can not delete this campaign. Because ' . $sent . ' emails already sent';
        } else {
            $this->db->where('camp_id', $cid)->delete('blog_email');
            // double check if still sending
            $sent = $this->db->from('blog_email')->where('camp_id', $cid)->where('sent > 0')->count();
            if ($sent) {
                $err = 'Sorry, you can not delete this campaign. Because ' . $sent . ' emails already sent';
            } else {
                $this->db->where('id', $cid)->delete('blog_email_camp');
                $err = 'Email campaign # ' . $cid . ' has been deleted';
            }
        }
        header('location:/admin/email-temp/' . ($camp ? $camp['temp_id'] : 0) . '?err=' . urlencode($err));
        exit;
    }

    public function copy($id = 0) {
        $id = ceil($id);
        $temp = $this->db->from('blog_email_temp')->where('id', $id)->row();
        if ($temp) {
            unset($temp['id']);
            unset($temp['created']);
            $id = $this->db->insert('blog_email_temp', $temp);
        }
        header('location:/admin/email-temp/' . $id);
        exit;
    }

    public function edit($id = 0) {
        $id = ceil($id);
        $temp = $this->db->from('blog_email_temp')->where('id', $id)->row();
        $data = ['cmd'];
        $cmd = Data::clean($data)['cmd'];

        $err = '';
        if ($temp) {
            if ($cmd == 'save_temp') {
                $err = $this->saveTemp($id);
            } elseif ($cmd == 'save_temp_txt') {
                $data = ['subject', 'news', 'view_tpl', 'txt', 'preview_txt', 'list1', 'list2', 'list3'];
                $data = Data::clean($data);
                $this->db->where('id', $id)->update('blog_email_temp', $data);
            } elseif ($cmd == 'Save Camp') {
                $this->saveCamp($id);
            } elseif ($cmd == 'Queue Email') {
                $this->saveCampEmail($temp);
            } elseif ($cmd == 'Send Email' || $cmd == 'Stop Send') {
                $this->saveCampSend($id, $cmd);
            }
        }

        header('location:/admin/email-temp/' . $id . ($err ? '?err=' . $err : ''));
        exit;
    }

    public function index($page = 1) {
        $temps = $this->db->from($this->table)->order('id desc');
        $temps = $this->pgno($temps, $page);
        $temps = $this->getLastCamp($temps);
        $this->set('temps', $temps);
        $this->set('pgno_url', '/admin/email-temp/page/');
        $this->set('page.title', 'Email Campaign CMS');
    }

    public function info($id) {
        $this->view = 'info.tpl';
        $temp = $this->db->from('blog_email_temp')->where('id', $id)->row();
        if ($temp['repeat_at']) {
            $repeat = [];
            if ($temp['temp_type'] == 'Daily') { // hh:ii
                $repeat['h'] = ceil(substr($temp['repeat_at'], 0, 2));
                $repeat['i'] = ceil(substr($temp['repeat_at'], 3));
            } elseif ($temp['temp_type'] == 'Weekly') { // Mon 11:33
                $repeat['w'] = substr($temp['repeat_at'], 0, 3);
                $repeat['h'] = ceil(substr($temp['repeat_at'], 4, 2));
                $repeat['i'] = ceil(substr($temp['repeat_at'], 7));
            } else {
                $ymd = strtotime($temp['repeat_at']);
                $repeat['y'] = date('Y', $ymd);
                $repeat['m'] = date('m', $ymd);
                $repeat['d'] = date('d', $ymd);
                $repeat['h'] = date('H', $ymd);
                $repeat['i'] = date('i', $ymd);
            }
            $temp['repeat'] = $repeat;
        }
        $this->set('temp', $temp);

        $this->setCamp($id);
        $this->set('type_options', HtmlHelper::emailRepeat($temp['temp_type'], [
            'Instant' => 'Instant',
            'Daily'   => 'Daily',
            'Weekly'  => 'Weekly',
            'Manual'  => 'Manual',
        ]));
        $this->set('user_options', HtmlHelper::userGroup($temp['users']));
        $this->set('file_options', HtmlHelper::tempFile($temp['view_tpl'], $this->config->get('view_root'), $this->blog['site']['template']));
        $this->set('state_options',HtmlHelper::addrState($temp['user_state']));
        $this->set('tag_options',  HtmlHelper::userTag($temp['user_tag'], '', 'SELECT'));
        $this->set('page.title', 'Email Campaign CMS');
        if (isset($_GET['err'])) {
            $this->set('err', nl2br(trim(urldecode($_GET['err']))));
        }

        $arr = ['ALL CUSTOMER', 'ALL CUSTOMER SET', 'ALL STAFF', 'ALL BASIC', 'ALL FREETRIAL', 'ALL PREMIUM', 'INACTIVE FREETRIAL'];
        if (!in_array($temp['users'], $arr)) {
            $this->set('temp.emails', $temp['users']);
        }
    }

    private function getLastCamp($temps) {
        $ids = [];
        foreach ($temps as $t) {
            $ids[] = $t['id'];
        }
        if (!$ids) {
            return $temps;
        }

        $camps = $this->db->select('temp_id, max(id) as max_id')
            ->from('blog_email_camp')
            ->where('temp_id', $ids)
            ->group(1)
            ->arr();
        if (!$camps) {
            return $temps;
        }

        // Get the camp IDs from the max_id values
        $camp_ids = array_values($camps);
        
        $emails = $this->db->select('c.temp_id, c.id as camp_id, c.queued_ts, c.subject, count(e.id) as num')
            ->from('blog_email_camp AS c')
            ->leftJoin(['e' => 'blog_email'], 'e.camp_id = c.id')
            ->where('c.id', $camp_ids)
            ->group('c.temp_id, c.id') // Include all non-aggregated columns
            ->arr();
        if (!$emails) {
            return $temps;
        }

        foreach ($temps as $k => $temp) {
            if (isset($emails[$temp['id']])) {
                $temps[$k]['email'] = $emails[$temp['id']];
            }
        }
        return $temps;
    }

    private function saveCamp($temp_id) {
        $data = ['subject', 'txt', 'camp_id' => 'int'];
        $data = Data::clean($data);
        $camp_id = $data['camp_id'];
        if ($camp_id < 1) {
            return;
        }

        if ($this->db->from('blog_email_camp')
            ->where('id', $camp_id)
            ->where('temp_id', $temp_id)
            ->count()
        ) {
            $send = $this->db->from('blog_email')
                ->where('camp_id', $camp_id)
                ->where('sent > 0')
                ->count();
            if (!$send) {
                unset($data['camp_id']);
                $this->db->where('id', $camp_id)->update('blog_email_camp', $data);
            }
        }
    }

    private function saveCampEmail($temp) {
        $data = ['camp_id' => 'int'];
        $camp_id = Data::clean($data)['camp_id'];
        if ($camp_id < 1) {
            return;
        }
        $camp_id = $this->db->select('id')
            ->from('blog_email_camp')
            ->where('id', $camp_id)
            ->where('temp_id', $temp['id'])
            ->val();
        if (!$camp_id) {
            return;
        }
        if ($this->db->from('blog_email')->where('camp_id', $camp_id)->count()) {
            return; // already queued
        }

        $camp = new Camp();
        $camp->makeQueue($camp_id, $temp, 4); // 4 do not send yet
        $log = $camp->getLog();
        $err = '?err=' . urlencode($log);

        header('location:/admin/email-temp/' . $temp['id'] . $err);
        exit;
    }

    private function saveCampSend($temp_id, $cmd = '') {
        $data = ['camp_id' => 'int'];
        $camp_id = Data::clean($data)['camp_id'];
        if ($camp_id) {
            $camp_id = $this->db->select('id')
                ->from('blog_email_camp')
                ->where('id', $camp_id)->where('temp_id', $temp_id)
                ->val();
        }
        if ($camp_id) {
            $sql = $this->db->where('camp_id', $camp_id)->where('sent = 0');
            if ($cmd == 'Send Email') {
                $sql->where('err', -1)->update('blog_email', 'err = 0');
            } else { // stop
                $sql->where('err', 0)->update('blog_email', 'err = -1');
            }
        }
    }

    private function saveTemp($id) {
        $data = [
            'active' => 'int',
            'name', 'temp_type', 'users', 'emails', 'user_tag', 'user_state',
            'repeat',
        ];
        $data = Data::clean($data);
        $data['active'] = $data['active'] && in_array($data['temp_type'], ['Daily', 'Weekly']) ? 1: 0;
        $err = [];
        if ($data['temp_type'] == 'Manual' && $data['emails']) {
            $arr = explode("\n", str_replace(',', "\n", $data['emails']));
            $emails = [];
            foreach ($arr as $line) {
                $line = trim($line);
                if ($line) {
                    if (is_numeric($line) && ceil($line) == $line) {
                        $uid = $this->db->select('uid')->from('blog_user')->where('uid', $line)->val();
                        if ($uid) {
                            $emails[] = $uid;
                        } else {
                            $err[] = 'ID not exists: ' . $line;
                        }
                    } elseif (!Data::validEmail($line)) {
                        $err[] = 'Invalid email skipped: ' . $line;
                    } else {
                        $uid = $this->db->select('uid')->from('blog_user')->where('email', $line)->val();
                        if ($uid) {
                            $emails[] = $uid;
                        } else {
                            $err[] = 'Email not exists: ' . $line;
                        }
                    }
                }
            }
            sort($emails);
            $data['users'] = implode(',', array_unique($emails));
        }
        unset($data['emails']);

        $repeat = $data['repeat'];
        $h = max(min(ceil($repeat['h']), 23), 0);
        $i = max(min(ceil($repeat['i']), 59), 0);
        $hi =  !$h && !$i ? '' : ($h>9 ? $h : '0'.$h) . ':' . ($i>9 ? $i : '0'.$i);
        if ($data['temp_type'] == 'Instant') {
            $data['repeat_at'] = '';
        } elseif ($data['temp_type'] == 'Daily') {
            $data['repeat_at'] = $hi;
        } elseif ($data['temp_type'] == 'Weekly') {
            $data['repeat_at'] = ($repeat['w'] ?: 'Mon') . ($hi ? ' ' . $hi : '');
        } else {
            $data['repeat_at'] = date('Y-m-d H:i', mktime($h, $i, 0, $repeat['m'], $repeat['d'], $repeat['y']));
        }
        unset($data['repeat']);

        $this->db->where('id', $id)->update('blog_email_temp', $data);
        return implode('<br>', $err);
    }

    private function setCamp($id) {
        $camp = $this->db->select('
            c.id, c.subject, c.queued_ts ts, c.txt, ifnull(count(e.id),0) num,
            ifnull(sum(e.sent>0),0) sent, sum(e.err>0) err, sum(e.err<0) stop, sum(e.bounced>0) bounced,
            sum(e.opened>0) opened, sum(e.clicked>0) clicked, sum(e.unsub>0) unsub,
            min(e.queued_ts) ts_send')
            ->from('blog_email_camp c')
            ->leftJoin(['e' => 'blog_email'], 'c.id = e.camp_id')
            ->where('c.temp_id', $id)
            ->group(1)
            ->order('1 DESC')
            ->limit(50)
            ->rows();
        $ids = array_column($camp, 'id');
        if (!$ids) {
            $this->set('camp', $camp);
            return;
        }

        $stat = $this->db
            ->select('e.camp_id, u.acs, u.status, count(*) num')
            ->from('blog_email AS e')
            ->join(['u' => 'blog_user'], 'e.uid = u.uid')
            ->where('e.camp_id', $ids)
            ->group(1,2,3)
            ->arr3();
        $acs = ['Open', 'Basic', 'Freetrial', 'Premium', 'NA4', 'NA5', 'Staff', 'Support', 'Editor', 'Admin'];
        foreach ($camp as $c => $r) {
            $cid = $r['id'];
            if (isset($stat[$cid])) {
                $txt = '';
                foreach ($stat[$cid] as $a => $r2) {
                    foreach ($r2 as $s => $num) {
                        $txt .= isset($acs[$a]) ? $acs[$a] : $a;
                        $txt .= '_' . $s . '_' . $num . '; ';
                    }
                }
                $camp[$c]['stat'] = $txt;
            }
        }

        $this->set('camp', $camp);

        $camp_cur = Data::clean(['cid' => 'int'])['cid'];
        if ($camp_cur && in_array($camp_cur, $ids)) {
            $this->set('camp_cur', $camp_cur);
        }
    }
}
