<?php
namespace App\News\Admin;

use Topnew\Chart;
use Topnew\Data;

class StatController extends Base
{
    public function home() {
        if ($this->blog['user_acs'] != 9) {
            header('location:/admin');
            exit;
        }

        $this->daily();
        $this->hourly();
        $this->monthly();
        $this->hourlyPop();

        $this->topTen('geo', 'Geo');

        $os = [
            '', 'NA_OS', 'windows', 'iPad', 'iPod', 'iPhone', 'mac', 'android', 'linux', 'Nokia',
            'BlackBerry', 'FreeBSD', 'OpenBSD', 'NetBSD', 'OpenSolaris', 'SunOS', 'OS/2', 'BeOS', 'win'
        ];
        $this->topTen('os', 'OS', $os);

        $br = [
            100 => 'webtv', 101 => 'IE', 102 => 'MSN', 103 => 'PocketIE',
            110 => 'OperaMini', 111 => 'Opera', 115 => 'galeon',
            120 => 'Netscape',
            130 => 'FF',
            150 => 'Safari', 155 => 'NetPositive', 158 => 'Firebirde',
            160 => 'Konqueror', 165 => 'icab', 168 => 'Phoenix',
            170 => 'Amaya', 172 => 'Lynx', 175 => 'Iceweasel', 176 => 'Shirotoko', 177 => 'IceCat',
            180 => 'AOL',
            200 => 'googlebot',
            210 => 'msnbot',
            220 => 'Slurp',
            230 => 'W3C-checklink', 231 => 'W3C_Validator',
            250 => 'Moz',
            300 => 'Chrome',
            310 => 'omniweb',
            320 => 'Android',
            322 => 'iPhone', 323 => 'iPad', 324 => 'iPod',
            340 => 'BlackBerry',
            350 => 'Norkia'
        ];
        $this->topTen('br', 'Browser', $br);

        $this->topNews7Day();
        //$this->topNews('all');
        $this->topNews('trend');

        $this->userOnline();
        $this->userTop();

        // most traffic via email camp
        // most traffic via email parts
        // most popular keyw this month
        // most active readers
        // currently online
        // list of spam email / domain -- last seen / hits most
        // list of ip blocked -- last check / most
        // top referrer external -- last 2 months
        // top referrer internal from story/cat/tag to story

        $this->set('page.title', 'Admin Stats Dashboard');
    }

    public function tag($tag = '') {
        $this->set('page.title', 'Page hits report');
        $data = Data::clean([
            'y1' => 'int', 'm1' => 'int', 'd1' => 'int', 'h1' => 'int', 'i1' => 'int',
            'y2' => 'int', 'm2' => 'int', 'd2' => 'int', 'h2' => 'int', 'i2' => 'int',
            'fm' => 'date','to' => 'date',
            'num'=> 'int', 'uid'=> 'int', 'tid'=> 'int',
            'tag', 'cmd',
        ]);
        if (!$data['cmd']) {
            $fm = date('Y-m-d H:i', strtotime('-7 day'));
            $data['y1'] = substr($fm, 0, 4);
            $data['m1'] = substr($fm, 5, 2);
            $data['d1'] = substr($fm, 8, 2);
            $data['h1'] = 0;
            $data['i1'] = 0;
            $fm = date('Y-m-d H:i');
            $data['y2'] = substr($fm, 0, 4);
            $data['m2'] = substr($fm, 5, 2);
            $data['d2'] = substr($fm, 8, 2);
            $data['h2'] = substr($fm,11, 2);
            $data['i2'] = substr($fm,14, 2);
        }
        if ($data['fm']) {
            $fm = $data['fm'] . ' 00:00:00';
            $data['y1'] = substr($fm, 0, 4);
            $data['m1'] = substr($fm, 5, 2);
            $data['d1'] = substr($fm, 8, 2);
        } else {
            // deprecated warnings errors
            $hour = $data['h1'] ? $data['h1'] : 0;  // Default to 0 if $hour is null
            $minute = $data['i1'] ? $data['i1'] : 0;  // Default to 0 if $minute is null
            $month = $data['m1'] ? $data['m1'] : 0;  // Default to 0 if $month is null
            $day = $data['d1'] ? $data['d1'] : 0;  // Default to 0 if $day is null
            $year = $data['y1'] ? $data['y1'] : 0;  // Default to 0 if $year is null
            $fm = date('Y-m-d H:i:s', mktime($hour, $minute, 0, $month, $day, $year));
        }
        if ($fm < '2000-01-01 00:00:00') {
            $fm = '';
        }
        if ($data['to']) {
            $to = $data['to'] . ' 23:59:59';
            $data['y2'] = substr($to, 0, 4);
            $data['m2'] = substr($to, 5, 2);
            $data['d2'] = substr($to, 8, 2);
        } else {
            // deprecated warnings error
            $hour2 = $data['h2'] ? $data['h2'] : 0;  // Default to 0 if $hour is null
            $minute2 = $data['i2'] ? $data['i2'] : 0;  // Default to 0 if $minute is null
            $month2 = $data['m2'] ? $data['m2'] : 0;  // Default to 0 if $month is null
            $day2 = $data['d2'] ? $data['d2'] : 0;  // Default to 0 if $day is null
            $year2 = $data['y2'] ? $data['y2'] : 0;  // Default to 0 if $year is null
            $to = date('Y-m-d H:i:s', mktime($hour2, $minute2, 59, $month2, $day2, $year2));
        }
        if ($to < '2000-01-01 00:00:00') {
            $to = '';
        }
        if ($to && $fm > $to) {
            Data::swap($fm, $to);
        }
        $tag = $tag ?: $data['tag'];
        $tag = $tag ? $this->db->select('cat')->from('blog_cat')->where('name', $tag)->val() : '';
        $tid = $data['tid'];
        $uid = $data['uid'];

        $log_ymd = 0;
        if ($fm) {
            if ($fm < date('Y-m-d 00:00:00', strtotime('-12 month'))) {
                $log_ymd = 1;
            } else {
                /*
                $site = $this->blog['site']['name']; // each site has different release date
                if ($site == 'Shortlist' && $fm < '2020-11-27 00:00:00') {
                    $log_ymd = 1;
                }
                */
            }
        }

        if ($log_ymd) {
            $this->copyYmd();
            $col = ',sum(hit) hit,max(created) created FROM blog_log_ymd';
        } else {
            $col = ',count(*) hit,max(created) created FROM blog_log';
        }

        $log = 'SELECT ' . ($data['cmd'] == 'User' ? 'uid' : 'tid') . $col . ' WHERE 1';
        $log .= $tid ? ' AND tid=' . $tid : '';
        $log .= $uid ? ' AND uid=' . $uid : '';
        $log .= $fm  ? " AND created >= '" . $fm . "'" : '';
        $log .= $to  ? " AND created <= '" . $to . "'" : '';
        if ($tag) {
            $tids = $this->db->select('tid')->from('blog_tag')->where('tag', $tag)->enum();
            $log .= $tids ? ' AND tid IN (' . implode(',', $tids) . ')' : ' AND 0';
        }
        $log .= ' GROUP BY 1 ORDER BY 2 DESC LIMIT ' . ($data['num'] > 0 ? $data['num'] : 50);

        if ($data['cmd'] == 'User') {
            $sql = 'SELECT u.uid,u.email,l.hit,u.fname,u.lname,u.acs,l.created FROM blog_user AS u'
                . ' JOIN (' . $log . ') as l ON u.uid = l.uid ORDER BY l.hit DESC';
        } else {
            $sql = 'SELECT t.tid,t.headline,l.hit,t.created FROM blog_topic AS t'
                . ' JOIN (' . $log . ') as l ON t.tid = l.tid ORDER BY l.hit DESC';
        }
        $hits = $this->db->all($sql);
        $this->set('hits', $hits);
        $data['fm'] = $fm;
        $data['to'] = $to;
        $this->set('form', $data);

        $tags = $this->db->select('cat,name,info')->from('blog_cat')->where('cat', 'start', 21)->order(1)->arr();
        $tags = [0 => ['name' => '', 'info' => 'All tags']] + $tags;
        $this->set('tags', $tags);

        if ($data['cmd'] == 'User') {
            $this->tagUid($uid, $hits);
        } else {
            $this->tagTid($tid);
        }
    }

    private function copyYmd() {
        $ymd = $this->db->select('max(created)')->from('blog_log_ymd')->val();
        if ($ymd >= date('Y-m-d')) {
            return;
        }
        $this->db->delete('blog_log_tmp');
        $sql = 'INSERT INTO blog_log_tmp SELECT date(created),uid,tid,count(*) FROM blog_log';
        if ($ymd) {
            $sql .= " WHERE created >='" . $ymd . " 00:00:00'";
        }
        $res = $this->db->run($sql . ' GROUP BY 1,2,3');
        $num = $this->db->from('blog_log_tmp')->count();
        if (!$num) {
            return;
        }
        if ($ymd) {
            $this->db->where('created', $ymd)->delete('blog_log_ymd');
        }
        $this->db->run('INSERT INTO blog_log_ymd SELECT * FROM blog_log_tmp');
    }

    private function tagTid($tid) {
        if (!$tid) {
            return;
        }

        $users = $this->db->from('blog_log')
            ->where('tid', $tid)
            ->where('uid > 0')
            ->order('created DESC')
            ->limit(50)
            ->all();
        $this->set('users', $users);

        $logs = $this->db->from('log' . date('_Y_m'))
            ->where("(
                url LIKE '/news/%" . $tid . "' OR
                url = '/news/" . $tid . "' OR
                url LIKE '/news/" . $tid . "/%'
            )")
            ->order('ts DESC')
            ->limit(50)
            ->all();
        $this->set('logs', $logs);

        $uids = array_unique(array_merge(array_column($logs, 'uid'), array_column($users, 'uid')));
        if (!$uids) {
            return;
        }
        $names = $this->db->select('uid,fname,lname,email')->from('blog_user')
            ->where('uid', $uids)->arr();
        $this->set('names', $names);
    }

    private function tagUid($uid, $hits) {
        if (!$uid) {
            return;
        }

        $news = $this->db->select('l.*,t.headline')
            ->from(['t' => 'blog_topic'])
            ->join(['l' => 'blog_log'], 't.tid = l.tid')
            ->where('l.uid', $uid)
            ->order('l.created DESC')
            ->limit(50)
            ->all();
        $this->set('news', $news);

        $logs = $this->db->from('log' . date('_Y_m'))
            ->where('uid', $uid)
            ->order('ts DESC')
            ->limit(50)
            ->all();
        $this->set('logs', $logs);

        $this->set('names', [$uid => $hits ? $hits[0] : []]);
    }

    private function daily() {
        $data = $this->dataGet('substr(ts, 1, 10)');
        $data = array_slice($data, -32);

        $init = [
            'css' => 1,
            'w' => 1000,
            'h' => 200,
            'valShow' => 1,
            'title' => 'Web Visitors Daily Traffic',
            'colorDel' => '0,1,2,3',
            'xFormat' => 'substr|8',
        ];

        $this->set('stat.daily', Chart::svg($data, $init));
    }

    private function dataGet($col) {
        $data = $this->db->select($col . ', count(*)')
            ->from('log_' . date('Y_m', strtotime('-1 month')))
            ->group(1)->order(1)->arr(); // last month
        $data2 = $this->db->select($col . ',count(*)')
            ->from('log_' . date('Y_m'))
            ->group(1)->order(1)->arr(); // this month
        return $this->dataMerge($data, $data2);
    }

    private function dataMerge($data, $data2) {
        foreach ($data2 as $k => $v) {
            if (isset($data[$k])) {
                $data[$k] += $v;
            } else {
                $data[$k] = $v;
            }
        }
        ksort($data);
        return $data;
    }

    private function hourly() {
        $data = $this->dataGet('substr(ts, 1, 13)');
        ob_start();
        $init = [
            'w' => 1000,
            'h' => 200,
            'valShow' => 1,
            'xKey' => 'hour',
            'xMin' => date('Y-m-d H', strtotime('-48 hour')),
            'xMax' => date('Y-m-d H'),
            'title' => 'Web Visitors Hourly Traffic',
            'colorDel' => '0,1,2,3',
            'xFormat' => 'substr|-2',
        ];
        $this->set('stat.hourly', Chart::svg($data, $init));
    }

    private function hourlyPop() {
        $data = $this->dataGet('substr(ts, 12, 2)');
        $init = [
            'w' => 1000,
            'h' => 200,
            'valShow' => 1,
            'title' => 'Web Visitors Hourly Traffic Sum over last 2 Months',
            'colorDel' => '0,1,2,3',
        ];
        $this->set('stat.hourlyPop', Chart::svg($data, $init));
    }

    private function monthly() {
        $fm = max('2019-01-01', date('Y-m-d', strtotime('-1 year')));
        $to = date('Y-m-d');
        $data = [];
        while ($fm <= $to) {
            $tab = 'log_' . date('Y_m', strtotime($fm));
            $data2 = $this->db->select('substr(ts, 1, 7),count(*)')
                ->from($tab)->group(1)->arr();
            $data = $this->dataMerge($data, $data2);
            $fm = date('Y-m-d', strtotime('+1 month', strtotime($fm)));
        }

        ksort($data);
        $init = [
            'w' => 1000,
            'h' => 200,
            'valShow' => 1,
            'title' => 'Web Visitors Monthly Traffic',
            'colorDel' => '0,1,2,3',
        ];
        $this->set('stat.monthly', Chart::svg($data, $init));
    }

    private function userOnline() {
        $users = $this->db->rows('
            SELECT u.uid,u.acs,u.fname,u.lname,l.num,l.ts,u.status
            FROM blog_user AS u
            JOIN (
                SELECT uid,count(*) AS num,max(ts) AS ts
                FROM log_' . date('Y_m') . '
                WHERE now() - ts < 50000
                GROUP BY 1
            ) AS l ON u.uid = l.uid
            ORDER BY l.ts DESC
        '); // last 5 hours
        $this->set('users_online', $users);
    }

    private function userTop() {
        $users = $this->dataGet('uid');
        if (!$users) {
            return;
        }
        arsort($users);
        $users = array_slice($users, 0, 50, 1);
        $names = $this->db->select('uid, acs, fname, lname, visited AS ts, status')
            ->from('blog_user')
            ->where('uid', array_keys($users))
            ->arr();
        $res = [];
        foreach ($users as $uid => $num) {
            if ($uid && isset($names[$uid])) {
                $u = $names[$uid];
                $u['uid'] = $uid;
                $u['num'] = $num;
                $res[] = $u;
            }
        }
        $this->set('users_top', $res);
    }

    private function topNews($part = '') {
        $order = $part == 'all' ? 'hit' : 'abs(hit/datediff(now(),created)) DESC, hit';
        $rows = $this->db->from('blog_topic')
            ->select('tid,acs,created,hit,url,headline,rank')
            ->order($order . ' DESC')
            ->limit(20)
            ->all();
        foreach ($rows as $k => $v) {
            $rows[$k]['url'] = $this->getTopicUrl($v['tid'], $v['url']);
        }
        $this->set('top.news.' . $part, $rows);
    }

    private function topNews7Day() {
        $sql = "select tid,count(*) num from blog_log where created >= '"
            . date('Y-m-d H:i:s', strtotime('-7 day'))
            . "' group by 1 order by 2 desc limit 20";
        $rows = $this->db->select('n.tid,n.acs,n.created,n.hit,n.url,n.headline,n.rank,a.num')
            ->from(['n' => 'blog_topic'])
            ->join(['a' => $sql], 'a.tid = n.tid')
            ->order('a.num DESC')
            ->all();
        foreach ($rows as $k => $v) {
            $rows[$k]['url'] = $this->getTopicUrl($v['tid'], $v['url']);
        }
        $this->set('top.news.all7', $rows);
    }

    private function topTen($col, $title, $arr = []) {
        $data = $this->dataGet($col);
        arsort($data);
        if ($col == 'os') {
            unset($data[1]);
        }
        $data = array_slice($data, 0, 10, 1);
        if ($arr) {
            $res = [];
            foreach ($data as $k => $v) {
                $k = isset($arr[$k]) ? $arr[$k] : 'NA';
                $res[$k] = $v;
            }
        } else {
            $res = $data;
        }
        $init = [
            'chart' => 'pie',
            'w' => 300,
            'h' => 300,
            'gapR' => -50,
            'pieArc' => -90,
            'pieStripe' => 1,
            'pieDonut' => 1,
            'title' => 'Top 10 ' . $title . ' of Web Visitors',
            //'piePct' => 1,
            //'valShow' => 1,
        ];
        $this->set('stat.' . $col, Chart::svg($res, $init));
    }
}
