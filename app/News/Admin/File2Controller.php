<?php
namespace App\News\Admin;

use Topnew\Page\File;

class File2Controller extends Base
{
    public function home() {
        $this->set('page.title', 'Admin - File Manager');
        if (! $this->inGrp('Director')) {
            // to be updated to proper grp on feedback from jo or drew
            $this->set('msg', 'Sorry, you do not have access to this page.');
            $this->view = '/Admin/File/err.tpl';
            return;
        }

        /*$admin_c = $this->config->get('blog.site.name') == 'Shortlist'
            ? $this->config->get('view_root') . '/AdminContent/'
            : '';*/

        $admin_c = $this->config->get('view_root') . '/' . $this->blog['site']['template'] . '/view/AdminContent/';
        $file = new File([
            'root' => $admin_c ?: WEB . 'files/',
            'web'  => $admin_c ? '*NONE*' : '/files',
        ]);
        if ($file->get('ajax')) {
            echo $file->get('txt');
            exit;
        }
        $this->set('web', $file->get('web'));
        $this->set('nav', $file->get('nav'));
        $this->set('dir', $file->get('dir'));
        $this->set('msg', $file->get('msg'));
        $this->set('sort',$file->get('sort'));
        if ($file->get('is_dir')) {
            $this->view = '/Admin/File/home.tpl';
            $this->set('files',$file->get('files'));
            $this->set('cols', $file->get('cols'));
        } else {
            $this->view = '/Admin/File/file.tpl';
            $this->set('txt',  $file->get('txt'));
        }
    }
}
