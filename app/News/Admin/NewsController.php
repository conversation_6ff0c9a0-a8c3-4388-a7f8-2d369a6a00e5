<?php
namespace App\News\Admin;

use App\News\Email\Camp;
use Topnew\Data;
use Topnew\Form;

class NewsController extends Base
{
    public function del($tid = 0) {
        $tid = ceil($tid);
        if ($tid > 0) {
            $this->db->where('tid', $tid)->delete('blog_topic');
            $this->db->where('tid1=' . $tid . ' OR tid2=' . $tid)->delete('blog_related');
            $this->db->where('tid', $tid)->delete('blog_tag');
        }
        $this->edit();
        $this->set('msg', '<p class="rc b-gold">Story deleted</p>');
    }

    public function edit($tid = 0) {
        if (! $this->canEdit()) {
            exit('you can not edit post news');
        }

        $data = Data::Clean([
            'headline', 'summary', 'summary_short', 'txt', 'tags', 'url', 'related', 'cmd', 'meta',
            'acs'  => 'int',
            'acs_only' => 'int',
            'uid'  => 'int',
            'dt_y' => 'int', 'dt_m' => 'int', 'dt_d' => 'int', 'dt_h' => 'int', 'dt_i' => 'int',
            'cats' => 'int',
            'mail' => 'int',
            'uid_by' => 'int',
        ]);
        
        $data['acs_only'] = $data['acs_only'] ?: 0;
        $data['mail'] = $data['mail'] ?: 0;
        
        $this->data = array_merge($data, $this->data);
        $this->view = '/Admin/News/edit';
        $this->set('msg', isset($_GET['saved']) ? '<p class="b-green rc"><a href="/news/' . $tid . '" target="news">' . $_GET['saved'] . ' saved</a></p>' : '');
        $cmd = $this->get('cmd');
        if (!$cmd) {
            $this->initData($tid);
        } elseif ($cmd == 'diff') {
            return $this->diff($tid);
        } else {
            $this->saveData($tid);
            // Trigger email campaign creation when publishing
            if ($cmd == 'Publish') {
                $this->publish($tid);
            }
        }
        $this->set('summary_short_hide', $this->get('summary_short') ? '' : 'hide');
        $this->coutForm($tid);
    }

    public function post($tid = 0) {
        $topic = $tid ? $this->db->select('headline,cat')->from('blog_topic')->where('tid', $tid)->row() : '';
        if (!$topic) {
            return $this->edit();
        }
        $item = $topic['cat'] == $this->blog['cat_event'] ? 'Event' : 'News';
        header('location:/admin/news/edit/' . $tid . '?saved=' . $item);
        exit;

        $this->set('tid', $tid);
        $this->set('headline', $topic['headline']);
        $this->set('page.title', 'Post ' . $item . ' - Success');
        $this->set('page.desc', '');
        $this->set('item', $item);
        $this->view = '/Admin/News/post';
    }

    private function cleanUrl($url) {
        // seems img . will be removed and not allow 123.png ?
        // url only need A-Za-z0-9
        $url = preg_replace('/[^\da-z]/i', ' ', strtolower($url));
        // remove multiple space
        $url = preg_replace('/\s+/', ' ', trim($url));
        $url = str_replace(' ', '-', $url);
        return is_numeric(str_replace('-', '', $url)) ? '' : $url;
    }

    private function coutForm($tid) {
        $form = new Form();
        $this->set('action', '/admin/news/' . ($tid ? 'edit/' . $tid : 'post'));
        $this->set('edit_link', $tid ? '<a href="/news/' . $tid . '" class="btn btn-outline-primary" target="news"># ' . $tid . '</a>' : '');
        $item = 'News';
        if ($tid && $this->get('cat') == $this->blog['cat_event']) {
            $item = 'Event';
        } else {
            $cats = $this->get('cats');
            if ($cats && in_array($this->blog['cat_event'], $cats)) {
                $item = 'Event';
            }
        }
        $this->set('head', ($tid ? 'Edit ' : 'Create ') . $item);
        $this->set('page.title', $this->get('head'));
        $this->set('page.desc', '');
        $this->set('author', $this->getFormAuthor($form));
        $this->set('acs', $form->select('acs', $this->get('acs'), [
            'list' => ['Open', 'Basic', 'Freetrial', 'Premium', 7 => 'Unpublished', 'Draft'],
            'label' => -1,
            'class' => 'custom-select d-block w-100 placeholder',
            'defa' => 'Access Minimum',
        ]));
        $this->set('acs_only', $form->select('acs_only', $this->get('acs_only'), [
            'list' => [1 => 'Basic Only', 'Freetrial Only', 'Premium Only'],
            'label' => -1,
            'class' => 'custom-select d-block w-100 placeholder',
            'defa' => 'Visible to Anyone',
        ]));
        $related = $this->getTopicRelated($tid) ?: [[],[],[],[]];
        $related[] = [];
        $related[] = [];
        $this->set('related', $related);
        $this->set('cats_options', $this->getFormCats($cats));
        $this->set('cats_selected', $this->getFormCatSelected($tid, $cats));

        $meta = $this->get('meta');
        $meta = $meta ? json_decode($meta, 1) : [];
        if ($this->get('cat') == $this->blog['cat_event']) {
            $defa = [
                'img' => '', 'body_img' => '', 'body_img_caption' => '',
                'url' => '',
                'event_start' => date('Y-m-d', strtotime($this->get('rank') ?: 'today')),
                'event_end' => $this->get('updated') ? substr($this->get('updated'), 0, 10) : date('Y-m-d', strtotime($this->get('rank') ?: 'today')),
                'Location' => '',
                'Registration' => '',
                'Cost' => '',
                'Email' => '',
                'Phone' => '',
                'Event Type' => '',
                'When' => '',
                'Featured RHS' => '',
                'Layout' => '',
            ];
        } else {
            $defa = ['img' => '', 'footer' => '', 'Lazy loading' => '', 'body_img' => '', 'body_img_caption' => ''];
        }
        $meta = $meta ? array_merge($defa, $meta) : $defa;
        //uksort($meta, 'strcasecmp');
        $this->set('meta', $meta);

        $this->set('meta_date', ['event_start', 'event_end']);
        $this->set('published', $this->hasPublished($tid));

        $comm_tags = $this->getCommTags();
        $this->set('comm_tags', $comm_tags);

        $sel_tags = $this->getSelTags($tid);
        $this->set('sel_tags', $sel_tags);

        $arr = scandir($this->config->get('view_root') . '/' . $this->blog['site']['template'] . '/view/News');
        $arr2= $arr3 = [];
        foreach ($arr as $k) {
            if (substr($k, -4) == '.tpl' && substr($k, 0, 7) == 'footer.') {
                $arr2[] = substr($k, 7, -4);
            }
        }
        $this->set('footer_options', $arr2);

        $dir = $this->config->get('view_root') . '/' . $this->blog['site']['template'] . '/view/NewsEvent';
        if (file_exists($dir)) {
            $arr = scandir($dir);
            foreach ($arr as $k) {
                if (substr($k, -4) == '.tpl' && substr($k, 0, 7) == 'layout.') {
                    $arr3[] = substr($k, 7, -4);
                }
            }
        }
        $this->set('layout_options', $arr3);
    }

    private function getSelTags($tid = 0) {
        $rows = $this->db->select('c.cat, c.name, c.info')
            ->from('blog_cat c, blog_tag t')
            ->where('c.cat = t.tag')
            ->where('t.tid', $tid)
            ->where('c.cat', 'like', '2%')
            ->order('t.sort')
            ->rows();
        $sel_tags = [];
        foreach ($rows as $r) {
            $sel_tags[$r['cat']] = $this->getCatName($r['name'], $r['info']);
        }
        return $sel_tags;
    }

    private function getCommTags() {
        $rows = $this->db->select('cat, name, info')
            ->from('blog_cat')
            ->where('cat', 'like', '21__%')
            ->order(in_array($this->blog['site']['id'], [2,4]) ? 'cat' : "if (info='', name, info)")
            ->rows();//2:OHS
        $comm_tags = [];
        foreach ($rows as $r) {
            $comm_tags[$r['cat']] = $this->getCatName($r['name'], $r['info']);
        }
        return $comm_tags;
    }

    private function hasPublished($tid) {
        // plugin date range as well later eg any news older than 1 year always published ?
        $old_tid = $this->blog['site']['id'] == 7 ? 10000 : 80000; // last tid from old system 7=hrd
        return $tid < $old_tid || $this->db->from('blog_email_camp')
            ->where('txt', 'like', '%/news/' . $tid . '/%')->count();
    }

    private function diff($tid) {
        if (!$tid || !isset($_POST['txt'])) {
            exit;
        }
        $old = $this->baseSQL()->select('txt')->where('n.tid', $tid)->val();
        $old = explode("\n", $old);
        $new = explode("\n", $_POST['txt']);
        foreach ($old as $k=>$v) {
            $old[$k] = rtrim($v);
        }
        foreach ($new as $k=>$v) {
            $new[$k] = rtrim($v);
        }

        $options = [
            //'context' => 3,
            //'ignoreWhitespace' => true,
            //'ignoreCase' => true,
        ];
        // Initialize the diff class
        $diff = new \Diff($old, $new, $options);
        $renderer = new \Diff_Renderer_Html_Inline;
        echo $diff->render($renderer) ?: 'Nothing changed';
        echo '<link rel="stylesheet" href="/css/diff.css">';
        exit;
    }

    private function getFormAuthor($form) {
        $staff = $this->db->select("uid, concat(if(acs > 7, '', '* '), fname, ' ', lname)")
            ->from('blog_user')
            ->where('or', ['acs > 6', 'uid' => [$this->data['uid'], $this->data['uid_by']]])
            ->order('fname')
            ->arr();
        return $form->select('uid', $this->data['uid'], [
            'list'  => $staff,
            'label' => -1,
            'class' => 'custom-select d-inline-block w-auto mr-2 placeholder',
            'defa'  => 'Published by',
            'title' => 'Published / Edited by',
        ]) . $form->select('uid_by', $this->data['uid_by'], [
            'list'  => $staff,
            'label' => -1,
            'class' => 'custom-select d-inline-block w-auto placeholder',
            'defa'  => 'Journalist',
            'title' => 'Journalist',
        ]);
    }

    private function getFormCats(&$cats = []) {
        $cats = $this->db->select('cat, name, info')
            ->from('blog_cat')
            ->where('cat', 'like', $this->blog['cat_base'] . '%')
            ->where('cat', '!=', $this->blog['cat_base'])
            ->order("concat('a', cat)")
            ->arr();
        $html = '<option value="">Please select a Category</option>';
        foreach ($cats as $k => $cat) {
            $html .= '<option value="' . $k . '">';
            $html .= str_repeat('-', strlen($k) - strlen($this->blog['cat_base']) - 2);
            $html .= ' ' . $this->getCatName($cat['name'], $cat['info']) . '</option>';
        }
        return $html;
    }

    private function getFormCatSelected($tid, $cats) {
        if ($tid) {
            $cats_selected = $this->db->select('tag')
                ->from('blog_tag')
                ->where('tid', $tid)
                ->where('tag', 'like', '18%')
                ->order('sort')
                ->enum();
        } else {
            $cats_selected = $this->get('cats');
        }
        if (!$cats_selected) {
            return;
        }
        $html = '';
        foreach ($cats_selected as $cat) {
            $html .= '<div><input type="hidden" value="' . $cat . '" name="cats[]">';
            $html .= isset($cats[$cat]) ? $this->getCatName($cats[$cat]['name'], $cats[$cat]['info']) : '';
            $html .= '</div>';
        }
        return $html;
    }

    private function initData(&$tid = 0) {
        $tid = max(ceil($tid), 0);
        $topic = $tid ? $this->baseSQL(1)->where('n.tid', $tid)->row() : '';
        $dt = $topic ? $topic['created'] : date('Y-m-d H:i');
        $this->set('dt_y', substr($dt, 0, 4));
        $this->set('dt_m', substr($dt, 5, 2));
        $this->set('dt_d', substr($dt, 8, 2));
        $this->set('dt_h', substr($dt,11, 2));
        $this->set('dt_i', substr($dt,14, 2));
        if (!$topic) {
            $tid = 0;
            $this->set('uid', $this->blog['sess_uid']);
            //$this->set('uid_by', $this->blog['sess_uid']);
            $this->set('meta', '');
            $this->set('acs', 8); // draft
            $this->set('mail', 1);
            if ($this->get('cats.0') == $this->blog['cat_event']) {
                $this->set('mail', 0);
                $this->set('cat', $this->blog['cat_event']);
            }
            return;
        }

        $this->data = array_merge($this->data, $topic);
        $meta = json_decode($topic['meta'], 1);
        $this->data['summary_short'] = isset($meta['summary_short']) ? $meta['summary_short'] : '';
        $this->data['uid_by'] = isset($meta['uid_by']) ? $meta['uid_by'] : '';

        /*$this->data['tags'] = implode(', ', $this->db->select('c.name')
            ->from(['c' => 'blog_cat'])
            ->join(['t' => 'blog_tag'], 'c.cat = t.tag')
            ->where('t.tid', $tid)
            ->where('c.cat', 'like', '2%')
            ->order('t.sort')
            ->enum()
        );*/
    }

    public function publishScheduled() {
        $publishService = new \App\News\Email\PublishService();
        $result = $publishService->publishScheduled();
        
        return $result['message'];
    }

    private function publish($tid) {
        if (!$tid) {
            error_log("[NewsController::publish] No TID provided to publish function");
            return; // should not be here, but just safety double check
        }
        
        error_log("[NewsController::publish] Starting publish process for TID: " . $tid);
        
        try {
            $publishService = new \App\News\Email\PublishService();
            error_log("[NewsController::publish] PublishService instantiated successfully");
            
            $result = $publishService->publishTopic($tid);
            error_log("[NewsController::publish] publishTopic returned: " . ($result ? 'true' : 'false'));
            
            if (!$result) {
                error_log("[NewsController::publish] Publishing failed, showing error messages");
                foreach ($publishService->getLog() as $logMessage) {
                    error_log("[NewsController::publish] Error: " . $logMessage);
                    $this->data['msg'] .= '<p class="alert alert-danger">' . $logMessage . '</p>';
                }
            } else {
                error_log("[NewsController::publish] Publishing succeeded, showing success messages");
                foreach ($publishService->getLog() as $logMessage) {
                    error_log("[NewsController::publish] Success: " . $logMessage);
                    $this->data['msg'] .= '<p class="alert alert-success">' . $logMessage . '</p>';
                }
            }
        } catch (\Exception $e) {
            error_log("[NewsController::publish] Exception caught: " . $e->getMessage());
            error_log("[NewsController::publish] Exception trace: " . $e->getTraceAsString());
            $this->data['msg'] .= '<p class="alert alert-danger">Error occurred during publishing. Check server logs.</p>';
        }
        
        error_log("[NewsController::publish] Publish function completed");
    }

    private function saveData($tid) {
        $this->saveDataTxt();
        $topic = $tid ? $this->baseSQL(1)->where('n.tid', $tid)->row() : '';
        $this->data['cats'] = $this->data['cats'] ?: [];
        array_unshift($this->data['cats'], $this->blog['cat_base']); // defa cat
        // remove parent cat if kid cat exists, also remove duplicates
        foreach ($this->data['cats'] as $k => $v) {
            foreach ($this->data['cats'] as $k2 => $v_kid) {
                if ($k != $k2 && (
                    $v == $v_kid || // duplicate
                    ($v < $v_kid && substr($v_kid, 0, strlen($v)) == $v) // keep kid cat
                )) {
                    unset($this->data['cats'][$k]);
                }
            }
        }

        $this->data['cat'] = reset($this->data['cats']);
        $this->data['created'] = date('Y-m-d H:i:s', mktime(
            $this->data['dt_h'], $this->data['dt_i'], 0, $this->data['dt_m'], $this->data['dt_d'], $this->data['dt_y']
        ));
        $this->data['headline'] = $this->data['headline'] ?: 'NA';
        $this->data['url'] = $this->cleanUrl($this->data['url'] ?: $this->data['headline']);
        $this->data['ip'] = Data::ip();

        $arr = ['cat', 'acs', 'acs_only', 'uid', 'ip', 'created', 'url', 'headline', 'summary', 'txt', 'mail'];
        foreach ($arr as $k) {
            $data[$k] = $this->data[$k];
        }
        $this->saveDataMeta($data);

        if ($topic) {
            $this->db->where('tid', $tid)->update('blog_topic', $data);
            $item = $data['cat'] == $this->blog['cat_event'] ? 'Event' : 'News';
            $msg = '<p class="rc b-green">' . $item . ' saved: <a href="/news/' . $tid . '" target="news">' . $this->data['headline'] . '</a></p>';
            $meta = json_decode($this->get('meta'), 1);
            if (isset($meta['event_start']) && isset($meta['event_end'])) {
                if ($meta['event_start'] > $meta['event_end']) {
                    $msg .= '<p class="rc b-red">Event start date must not be later than event end date</p>';
                }
                if (substr($this->data['created'], 0, 10) > $meta['event_end']) {
                    $msg .= '<p class="rc b-red">(Display) Date must not be later than event end date</p>';
                }
            }
        } else {
            $msg = '';
            $tid = $this->db->insert('blog_topic', $data);
        }

        $this->db->insert('blog_topic_log', array_merge($data, [
            'by_uid' => $this->blog['sess_uid'],
            'tid' => $tid,
        ])); // log all of them

        $tags = $this->saveDataTags($this->data['tags']);
        $this->saveDataCats($tid, $this->data['cats'], $tags);
        $msg .= $this->saveDataImg($data['created']);
        $this->set('msg', $msg);
        $this->saveDataRelated($tid, $this->data['related']);
        $this->set('tid', $tid);

        if (!$topic || (
            $topic['cat'] != $this->blog['cat_event'] && $this->data['cat'] == $this->blog['cat_event']
        )) {
            // new post or change from news to event need redirect
            header('location:/admin/news/post/' . $tid);
            exit;
        }
    }

    private function saveDataCats($tid, $cats, $tags) {
        $cats = array_merge($cats, $tags);
        $new = [];
        foreach ($cats as $k => $v) {
            if (!in_array($v, $new)) {
                $new[] = $v;
            }
        }

        $old = $this->db->select('tag')->from('blog_tag')->where('tid', $tid)->order('sort')->enum();
        if ($new == $old) {
            return;
        }
        $this->db->where('tid', $tid)->delete('blog_tag');

        foreach ($new as $k => $v) {
            $sql[] = "($tid, $v, $k)";
        }
        $this->db->run('INSERT INTO blog_tag (tid, tag, sort) VALUES ' . implode(',', $sql));
    }

    private function saveDataImg($year) {
        if (!isset($_FILES['file'])) {
            return;
        }

        $year = substr($year, 0, 4);
        $dir  = WEB . 'files/' . ($year < 2000 ? date('Y') : $year);
        $file = $_FILES['file'];
        $allowed = ['image/jpg', 'image/jpeg', 'image/gif', 'image/png', 'application/pdf'];
        $msg = '';
        foreach ($file['name'] as $k => $name) {
            if (!$file['error'][$k] && $file['size'][$k] && in_array($file['type'][$k], $allowed)) {
                if (! file_exists($dir)) {
                    mkdir($dir, 0777, 1);
                }
                $arr = explode('.', $name);
                $ext = array_pop($arr);
                $name = $this->cleanUrl(implode('.', $arr)) . '.' . $ext;
                if (!file_exists($dir . '/' . $name)) {
                    $tmp_name = $file['tmp_name'][$k];
                    move_uploaded_file($tmp_name, $dir . '/' . $name);
                    $msg .= 'File uploaded:<br>' . $name . '<br>';
                } else {
                    $msg .= $name . ' already exists.<br>';
                }
            }
        }
        return $msg ? '<p class="alert alert-warning">' . $msg . '</p>' : '';
    }

    private function saveDataMeta(&$data) {
        $meta = $this->data['summary_short'] ? ['summary_short' => $this->data['summary_short']] : [];
        if ($this->data['uid_by']) {
            $meta['uid_by'] = $this->data['uid_by'];
        }
        foreach ($this->get('meta') as $v) {
            if ($v[0] && $v[1]) {
                $meta[$v[0]] = $v[1];
            }
        }
        $this->set('meta', json_encode($meta));

        if (isset($meta['event_start'])) {
            $data['rank'] = str_replace('-', '', $meta['event_start']);
            unset($meta['event_start']);
        }
        if (isset($meta['event_end'])) {
            $data['updated'] = $meta['event_end'];
            unset($meta['event_end']);
        } else {
            $data['updated'] = 'now()';
        }
        $data['meta'] = $meta ? json_encode($meta) : '';
    }

    private function saveDataRelated($tid, $related) {
        $sort = 0;
        $data = [];
        foreach ($related as $arr) {
            if (!$arr['fmt'] || $arr['fmt'] == 11) {
                $tid2 = $arr['tid'] ? ceil($arr['tid']) : '';
                if ($tid2 > 0) {
                    $info = $this->db->select('headline')->from('blog_topic')->where('tid', $tid2)->val();
                    if ($info) { // when tid2 found
                        $data[] = ['tid1' => $tid, 'tid2' => $tid2, 'sort' => $sort++, 'fmt' => $arr['fmt'], 'info' => $arr['info'] ?: $info, 'url' => ''];
                    }
                }
            } elseif ($arr['info'] && $arr['url']) {
                $data[] = ['tid1' => $tid, 'tid2' => 0, 'sort' => $sort++, 'fmt' => $arr['fmt'], 'info' => $arr['info'], 'url' => $arr['url']];
            }
        }

        $old = $this->db->from('blog_related')->where('tid1', $tid)->order('sort')->all();
        if ($data != $old) {
            $this->db->where('tid1', $tid)->delete('blog_related');
            foreach ($data as $arr) {
                $this->db->insert('blog_related', $arr);
            }
        }
    }

    private function saveDataTags($tags) {
        if (!$tags) {
            return [];
        }
        $arr = explode(',', $tags);
        $tags = [];
        foreach ($arr as $v) {
            $v = trim($v);
            if ($v) {
                $tags[strtolower(str_replace(' ', '-', $v))] = $v;
            }
        }
        $tag_ids = $this->db->select('name, cat')->from('blog_cat')
            ->where('cat', 'like', '2%')->where('name', array_keys($tags))->arr();
        $new_tags = [];
        foreach ($tags as $tag => $info) {
            if (isset($tag_ids[$tag])) {
                $new_tags[] = $tag_ids[$tag];
            } else {
                // Check if a tag with this slug already exists (double-check)
                $existingTag = $this->db->select('cat')
                    ->from('blog_cat')
                    ->where('name', $tag)
                    ->val();
                
                if ($existingTag) {
                    // Tag already exists, use it
                    $new_tags[] = $existingTag;
                    continue;
                }
                
                // Get all existing top-level tag IDs
                $existingTags = $this->db->select('cat')
                    ->from('blog_cat')
                    ->where("cat like '21%'")
                    ->where("LENGTH(cat) = 4")  // Ensure 4-digit IDs
                    ->enum();
                
                // Find the first available gap
                $tag_id = null;
                for ($i = 1; $i <= 99; $i++) {
                    $candidateId = '21' . str_pad($i, 2, '0', STR_PAD_LEFT);
                    if (!in_array($candidateId, $existingTags)) {
                        $tag_id = $candidateId;
                        break;
                    }
                }
                
                // If no gaps found, check if we can add a new ID at the end
                if (!$tag_id) {
                    // Get the maximum tag ID
                    $maxTopLevelId = $this->db->select('max(cat)')
                        ->from('blog_cat')
                        ->where("cat like '21%'")
                        ->where("LENGTH(cat) = 4")
                        ->val();
                    
                    // If no top-level tags exist yet, start with 2101
                    if (!$maxTopLevelId) {
                        $tag_id = 2101;
                    } else {
                        // Extract the last two digits and increment
                        $lastTwoDigits = intval(substr($maxTopLevelId, -2));
                        $newLastTwoDigits = $lastTwoDigits + 1;
                        
                        // Check if we've reached the maximum (99)
                        if ($newLastTwoDigits > 99) {
                            // Log the error but continue - we'll skip creating this tag
                            error_log("Cannot create tag: Maximum number of top-level tags reached (limit 99) and no gaps available");
                            continue; // Skip this tag
                        }
                        
                        // Format with leading zero if needed
                        $formattedLastTwoDigits = str_pad($newLastTwoDigits, 2, '0', STR_PAD_LEFT);
                        
                        // Create the new tag ID with prefix 21
                        $tag_id = '21' . $formattedLastTwoDigits;
                    }
                }
                
                $info = $this->getCatName($tag) == $info || $info == strtolower($info) ? '' : $info;
                $this->db->insert('blog_cat', ['name' => $tag, 'cat' => $tag_id, 'info' => $info]);
                $new_tags[] = $tag_id;
            }
        }
        return $new_tags;
    }

    private function saveDataTxt() {
        $txt = $this->data['txt'];
        $txt = $this->saveDataTxtCut($txt, '<a href="', '"');
        $txt = $this->saveDataTxtCut($txt, "<a href='", "'");
        $txt = $this->saveDataTxtCut($txt, '(', ')');
        $this->data['txt'] = $txt;
    }

    private function saveDataTxtCut($txt, $cut1 = '', $cut2 = '') {
        $arr = explode($cut1, $txt); // a href="##123"
        foreach ($arr as $k => $v) {
            if ($k && substr($v, 0, 2) == '##') {
                $arr2 = explode($cut2, substr($v, 2), 2);
                if (isset($arr2[1]) && ceil($arr2[0]) == $arr2[0] && $arr2[0] > 0 && is_numeric($arr2[0])) {
                    $arr2[0] = '/news/' . $arr2[0];
                    $arr[$k] = implode($cut2, $arr2);
                }
            }
        }
        return implode($cut1, $arr);
    }
}
