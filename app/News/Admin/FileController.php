<?php
namespace App\News\Admin;

use Topnew\Page\File;

class FileController extends Base
{
    public function home() {
        $file = new File([
            'root' => WEB . 'files/',//WEB.'file/'
            'web'  => '/files', // '/file'
        ]);
        if ($file->get('ajax')) {
            echo $file->get('txt');
            exit;
        }
        $this->set('web', $file->get('web'));
        $this->set('nav', $file->get('nav'));
        $this->set('dir', $file->get('dir'));
        $this->set('msg', $file->get('msg'));
        $this->set('sort',$file->get('sort'));
        $this->set('page.title', 'Admin - File Manager');
        if ($file->get('is_dir')) {
            $this->view = '/Admin/File/home.tpl';
            $this->set('files',$file->get('files'));
            $this->set('cols', $file->get('cols'));
        } else {
            $this->view = '/Admin/File/file.tpl';
            $this->set('txt',  $file->get('txt'));
        }
    }
}
