<?php
namespace App\News\Admin;

use App\News\HtmlHelper;
use App\News\TraitInv;
use Topnew\Data;

class CompanyController extends Base
{
    use TraitInv;

    public function home($a = '', $b = '') {
        return $this->crud($a, $b);
    }

    public function del($id = 0) {
        $id = ceil($id);
        $comp = $id ? $this->db->from('inv_purchaser')->where('id', $id)->row() : [];
        if ($id > 0 && $id < 100) {
            $this->set('msg', 'You can not delete any purchaser which ID less than 100, which might be a system company. Please contact DBA to delete.');
        } elseif ($comp) {
            $num_user = $this->db->from('blog_user')->where('purchaser', $id)->count();
            $num_inv  = $this->db->from('inv_invoice')->where('purchaser', $id)->count();
            if ($num_user) {
                $this->set('msg', 'There are ' . $num_user . ' users under this purchaser, you need to delete them first');
            } elseif ($num_inv) {
                $this->set('msg', 'There are ' . $num_inv . ' invoices under this purchaser, you need to delete them first');
            } else {
                $this->db->where('id', $id)->delete('inv_purchaser');
                $msg = 'Purchaser deleted: ' . $comp['company'] . '(' . $comp['email'] . ')';
                $this->saveLog($id, $msg);
                $this->set('msg', $msg);
                $this->index();
                return;
            }
        }
        $this->info($id);
    }

    public function edit($id = 0) {
        $id = ceil($id);
        $company = $this->db->from('inv_purchaser')->where('id', $id)->row();
        $data = ['cmd'];
        $cmd = Data::clean($data)['cmd'];
        $err = '';

        if ($company) {
            if (in_array($cmd, ['active', 'cancelled'])) {
                $this->saveData($company, ['status' => $cmd]);
                if ($cmd == 'cancelled') {
                    $this->cancelUser($company['id']);
                }
            } elseif ($cmd == 'save_info') {
                $data = [
                    'company', 'abn', 'organisation_limit', 'valid_domain', 'enable_subscriber_management', 'fname', 'lname', 'position', 'email',
                    'phone', 'mobile', 'addr', 'city', 'state', 'country', 'zip',
                ];
                $data = Data::clean($data);
                if ($data['email'] && !Data::validEmail($data['email'])) {
                    $err = '?err=Email+**' . $data['email'] . '**+is+invalid';
                    unset($data['email']);
                }
                $this->saveData($company, $data);
            }
        }

        header('location:/admin/company/' . $id . $err);
        exit;
    }

    public function index($page = 1) {
        header('Cache-control: private'); //browser back button keep form data
        $this->set('page.title', 'Admin - Lookup Organisation');
        $data = $this->searchForm();
        if ($data['is_organisation']) {
        $purchaser_id = $this->db->from('blog_user')->where('is_organisation', 1)->arr();
        $purchaser_ids = array_column($purchaser_id, 'purchaser');
        }
        $this->set('pgno_url', '/admin/company/page/');
        $rows = $this->db->select('p.*')->from('inv_purchaser AS p')->order('p.company,p.id DESC');
    
        if ($data['id']) {
            $rows = $rows->where('p.id', 'like', $data['id']);
            $this->set('keyw', 'ID = ' . $data['id']);
            $this->set('pgno_param', 'id=' . $data['id']);
            $this->set('company', $this->pgno($rows, max($page, $data['pgno'])));
            return;
        }

        $this->set('is_organ_searched', 0);
        if ($data['is_organisation']) {
            $this->set('is_organ_searched', 1);
             $rows = $rows->where('p.id', $purchaser_ids);
            }
            
        if ($data['status']) {
            $rows = $rows->where('p.status', ($data['status'] == 'cancelled' ? '=' : '!='), 'cancelled');
        }

        $arr = ['name', 'email', 'state', 'company'];
        foreach ($arr as $k) {
            if ($data[$k]) {
                $col = ($k == 'name') ? "concat(p.fname, ' ', p.lname)" : 'p.' . $k;
                $rows = $rows->where($col, 'like', '%' . $data[$k] . '%');
            }
        }
     
        if ($data['no_user']) {
            $rows = $rows->where('NOT EXISTS', "
                SELECT 1 FROM blog_user WHERE purchaser = p.id
                AND status NOT in ('closed', 'pending', 'expired')
            ");
        }
        if ($data['no_inv']) {
            $rows = $rows->where('NOT EXISTS', 'SELECT 1 FROM inv_invoice WHERE purchaser = p.id');
        }

        /*$sql_inv = "
            SELECT aa.inv_id,aa.qty,aa.date_fm,aa.date_to,aa.amt,aa.gst,aa.status,aa.purchaser,aa.item
            FROM inv_invoice AS aa
            JOIN (
              SELECT purchaser, max(concat(date_to,inv_id)) AS exp
              FROM inv_invoice
              WHERE ifnull(date_to, '') > ''
              GROUP BY 1
            ) AS pp ON aa.inv_id = substr(pp.exp, 11)
        ";*/
        $sql_inv = "
            SELECT aa.inv_id,aa.qty,aa.date_fm,aa.date_to,aa.amt,aa.gst,aa.status,aa.purchaser,aa.item
            FROM inv_invoice AS aa
            JOIN (
              SELECT purchaser, concat(max(date_to),min(inv_id)) AS exp
              FROM inv_invoice
              WHERE ifnull(date_to, '') >= '" . date('Y-m-d') . "'
              GROUP BY 1
            ) AS pp ON aa.inv_id = substr(pp.exp, 11)
        ";
        if ($data['inv_status']) {
            $rows = $rows->join(['i' => $sql_inv], 'i.purchaser = p.id')
                ->where('i.status', $data['inv_status']);
        }

        $can_download_csv = $this->inGrp('Director');
        $this->set('can_download_csv', $can_download_csv);
        if ($data['cmd'] == 'csv' && $can_download_csv) {
            $this->csv($rows, $sql_inv, $data);
            exit;
        }

        $company = $this->pgno($rows, max($page, $data['pgno']));
        $this->set('company', $company);

        $ids = array_column($company, 'id');
        $num_users = $ids
            ? $this->db->select('purchaser, count(*)')->from('blog_user')
                ->where('purchaser', $ids)
                ->where('status', '!=', ['closed', 'pending', 'expired'])
                ->group(1)->arr()
            : [];
        $this->set('num_users', $num_users);

        $arr = ['status', 'name', 'email', 'state', 'company', 'no_user', 'no_inv', 'inv_status', 'is_organisation'];
        $keyw = $param = '';
        foreach ($arr as $k) {
            if ($data[$k]) {
                $v = $k == 'status' && $data[$k] == 'new' ? 'Pending' : $data[$k];
                $keyw .= ucwords(str_replace('_', ' ', $k)) . ' = ' . $v . '; ';
                $param .= '&' . $k . '=' . $data[$k];
            }
        }
        $this->set('keyw', $keyw);
        $this->set('pgno_param', substr($param, 1));
    }

       public function info($id) {
        $company = $this->db->from('inv_purchaser')->where('id', $id)->row();
        $users_count = $this->db->from('blog_user')->where('purchaser', $id)->count();
        if (!$company) {
            return $this->home();
        }
        $id = $company['id'];

        $data = Data::clean(['cmd', 'note', 'err']);
        if ($data['cmd'] == 'note_add' && $data['note']) {
            $this->saveLog($id, $data['note'], 'Note');
            header('location:/admin/company/' . $id);
            exit;
        }
        $this->setNotes($data['note'], 'purchaser = ' . $id);
        $this->set('err', $data['err']);
        $this->set('users_count', $users_count);
        $this->set('company', $company);
        $this->set('page.title', 'Admin - Organisation Profile');
        $this->view = 'info';
        $this->searchForm();

        $num = $this->db->from('blog_user')->where('purchaser', $id)
            ->select("sum(if (status = 'active',1,0)) user_active,sum(if (status in ('closed', 'pending', 'expired'),0,1)) user_num")
            ->row();
        $this->set('user_num', $num['user_num']);
        $this->set('user_active', $num['user_active']);

        $pgno = Data::clean('pgno')['pgno'];
        $this->set('users', $this->db->from('blog_user')
            ->select('uid, acs, status, fname, lname, email, is_admin, visited')
            ->where('purchaser', $id)
            ->order("is_admin DESC, acs DESC, status in ('closed', 'pending', 'expired'), status, fname, lname")
            ->limit(25)
            ->pgno($pgno, $ttl, $max)
        );
        $this->set('pgno_max', $max);
        $this->set('pgno_ttl', $ttl);
        $this->set('pgno_url', '/admin/company/' . $id . '?pgno=');
        $this->set('pgno', $pgno ?: 1);

        $this->set('url_edit', '/admin/company/edit/' . $id . '?cmd=');
        $this->set('company.state_options', HtmlHelper::addrState($company['state']));
        $this->set('company.country_options', HtmlHelper::addrCountry($company['country']));

        // next line might be no longer needed
        $this->set('last_inv', $this->db->select('max(inv_id)')->from('inv_invoice')
            ->where('purchaser', $id)->val());

        $this->set('unpaid_inv', $this->db->from('inv_invoice')->where('status', 'Unpaid')
            ->where('purchaser', $id)->count());
    }

    public function add() {
        $id = $this->db->insert('inv_purchaser', ['company' => 'New Company']);
        header('location:/admin/company/' . $id);
        exit;
    }

    private function cancelUser($id) {
        $users = $this->db->select('uid,status')->from('blog_user')
            ->where('purchaser', $id)->where('status', '!=', 'closed')->arr();
        $ip = Data::ip();
        foreach ($users as $uid => $status) {
            $this->db->insert('inv_log', [
                'txt' => '<b>status</b> updated from <b>' . $status . '</b> to <b>closed</b>',
                'by_uid' => $this->uid,
                'status' => 'Sys',
                'purchaser' => $id,
                'uid' => $uid,
                'ip' => $ip,
            ]);
        }
        $this->db->where('purchaser', $id)->update('blog_user', ['status' => 'closed']);
    }

    private function csv($rows, $sql_inv, $data) {

        $rows = $rows->select('p.id AS Org_ID, p.company AS Organisation,
            p.fname AS First_Name, p.lname AS Last_Name, p.email, p.phone, p.mobile,
            i.date_fm AS Start_date, i.date_to AS Exp_date, p.status,
            /*i.inv_id,*/ o.qty, o.qty_all AS `Qty $`, i.qty AS Qty_next, i.item AS Next_Product,
            p.position, p.addr AS Address,
            p.city, p.state, p.zip AS Postcode, i.status AS Inv_Status, SUBSTRING(group_concat(n.txt), 1, 30000) AS Notes') // Limited the "Notes" column length to 30000 characters due to limitation issue in excel
            ->leftJoin(['n' => 'inv_log'], "p.id = n.purchaser AND n.status = 'Note'")
            ->group('i.purchaser')
            ->order(1);
        if (!$data['inv_status']) {
            $rows = $rows->leftJoin(['i' => $sql_inv], 'i.purchaser = p.id');
        }
        

        /*$sql_order = "
            SELECT oc.purchaser,sum(oo.qty) AS qty_all,sum(if(oo.vid>100,oo.qty,0)) AS qty
            FROM inv_order oo
            JOIN (
              SELECT oa.purchaser,min(concat(oa.date_fm,oa.inv_id)) dfm
              FROM inv_invoice oa
              JOIN (
                SELECT oi.purchaser,max(oi.date_to) AS dto
                FROM inv_invoice oi
                WHERE oi.status != 'cancelled'
                  AND oi.item != 'ADD'
                  AND oi.date_fm <= '" . date('Y-m-d') . "'
                  AND ifnull(oi.date_fm,'') !=''
                  AND ifnull(oi.date_to,'') !=''
                  AND EXISTS (SELECT 1 FROM inv_order oj,inv_price op WHERE oj.inv_id=oi.inv_id AND oj.vid=op.vid and op.qty_fm=1 LIMIT 1)
                GROUP BY 1
              ) AS ob ON oa.purchaser = ob.purchaser AND oa.date_to = ob.dto
              GROUP BY 1
            ) AS oc on oo.inv_id = substr(oc.dfm,11)
            GROUP BY 1";*/
        $sql_order = "
            SELECT oc.purchaser,sum(oo.qty) AS qty_all,sum(if(oo.vid>100,oo.qty,0)) AS qty
            FROM inv_order oo
            JOIN (
              SELECT oa.purchaser,concat(max(oa.date_to),min(oa.inv_id)) dfm
              FROM inv_invoice oa
              JOIN (
                SELECT oi.purchaser,max(oi.date_to) AS dto
                FROM inv_invoice oi
                WHERE oi.status != 'cancelled'
                  AND oi.item != 'ADD'
                  AND oi.date_fm <= '" . date('Y-m-d') . "'
                  AND ifnull(oi.date_fm,'') !=''
                  AND ifnull(oi.date_to,'') !=''
                  AND EXISTS (SELECT 1 FROM inv_order oj,inv_price op WHERE oj.inv_id=oi.inv_id AND oj.vid=op.vid and op.qty_fm=1 LIMIT 1)
                GROUP BY 1
              ) AS ob ON oa.purchaser = ob.purchaser AND oa.date_to = ob.dto
              GROUP BY 1
            ) AS oc on oo.inv_id = substr(oc.dfm,11)
            GROUP BY 1";    
     
        $rows = $rows->leftJoin(['o' => $sql_order], 'o.purchaser = p.id')->all();

        $users = $this->db->from('blog_user')->where('is_organisation', 1)->arr();
        $purchaserIds = array_column($users, 'purchaser');
        
        foreach ($rows as $key => $value) {
            if(in_array($value['Org_ID'], $purchaserIds)){
                $rows[$key]['Manage_Subscribers'] = "Yes";
               }else{
                $rows[$key]['Manage_Subscribers'] = "No";
               }
        }
        
  
        $file = 'Company_'
            . ($data['status'] ? $data['status'] . '_' : '')
            . ($data['inv_status'] ? 'inv_' . $data['inv_status'] . '_' : '')
            . date('Ymd_His');
        $this->downloadCsvCompany($rows, $file);
    }

    private function saveData($company, $data) {
        foreach ($data as $k => $v) {
            if ($company[$k] == $v) {
                unset($data[$k]);
            }
        }
        if (!$data) {
            return;
        }
        $this->db->where('id', $company['id'])->update('inv_purchaser', $data);
        foreach ($data as $k => $v) {
            $this->saveLog($company['id'], '<b>' . $k . '</b> updated from <b>' . $company[$k] . '</b> to <b>' . $v . '</b>');
        }
    }

    private function saveLog($purchaser, $note, $typ = 'Sys') {
        $this->db->insert('inv_log', [
            'txt' => $note,
            'by_uid' => $this->uid,
            'purchaser' => $purchaser,
            'ip' => Data::ip(),
            'status' => $typ,
        ]);
    }

    private function searchForm() {
        $data = [
            'id'  => 'int',
            'pgno'=> 'int',
            'no_user' => 'int',
            'no_inv' => 'int',
            'is_organisation' => 'int',
            'status', 'email', 'company', 'name',
            'state', 'inv_status', 'cmd',
        ];
        $data = Data::clean($data);
        if ($data['id'] < 1) {
            $data['id'] = '';
        }
        //$this->set('form', $data);

        //$this->set('status_options', HtmlHelper::companyStatus(null, 'Status')); //$data['status']
        $this->set('state_options', HtmlHelper::addrState());//$data['state']
        $this->set('inv_status_options', HtmlHelper::invStatus());//$data['inv_status']
        return $data;
    }
}
