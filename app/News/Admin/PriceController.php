<?php
namespace App\News\Admin;

use Topnew\Data;

class PriceController extends Base
{
    public function home($a = '', $b = '') {
        return $this->crud($a, $b);
    }

    public function index($page = 1) {
        $data = Data::clean([
            'sort',
        ]);
        $sort = $data['sort'] ?: 'item_abbr';
        $this->set('page.title', 'Admin - Price setup');
        $rows = $this->db->from('inv_price')->order($sort . ',qty_fm,qty_to')->all();
        $sold = $this->db->select('vid,sum(qty) qty,sum(qty*price/100) amt')
            ->from('inv_order')->group(1)->arr();
        $id = $this->get('vid');
        foreach ($rows as $k => $v) {
            $vid = $v['vid'];
            $rows[$k]['sold'] = isset($sold[$vid]) ? $sold[$vid] : ['amt'=>0, 'qty'=>0];
            if ($id && $id == $vid) {
                $v['price'] /= 100;
                $this->set('item', $v);
            }
        }
        $this->set('price', $rows);
        $this->set('sort', $sort);
    }

    public function info($vid = 0) {
        $this->set('vid', $vid);
        $this->saveData($vid);
        $this->index();
    }

    private function saveData($vid = 0) {
        $data = Data::clean([
            'item_abbr', 'ver_name', 'ver_name_web',
            'qty_fm' => 'int', 'qty_to' => 'int',
            'price',
            'exp_month' => 'int', 'subs'=> 'int',
            'cmd',
        ]);
        if (!$data['cmd']) {
            return;
        }
        unset($data['cmd']);
        if($data['price'] && $data['qty_fm'] && $data['qty_to'] && $data['exp_month'] && ($data['subs'] || $data['subs'] == 0)) {
            $data['price'] *= 100;
        
            if ($data['qty_fm'] && $data['qty_to'] && $data['qty_fm'] > $data['qty_to']) {
                Data::swap($data['qty_fm'], $data['qty_to']);
            }
            $id = $this->db->where('vid', $vid)->save('inv_price', $data);
            if ($id && $id != $vid) {
                $this->set('vid', $id);
            }
            $this->set('msg', 'Price saved');
        } else {
            $this->set('msg', 'Qty, Price, Duration, and Web order is required.');
        }
    }

    public function del($vid = 0) {
        if ($vid) {
            $num = $this->db->select('sum(qty)')->from('inv_order')->where('vid', $vid)->val();
            if ($vid < 100) {
                $this->set('msg', 'You can not delete system price');
            } elseif ($num > 0) {
                $this->set('msg', 'You can not delete this price, it has sold ' . $num);
            } else {
                $this->db->where('vid', $vid)->delete('inv_price');
                $this->set('msg', 'Price has been deleted');
            }
        }
        $this->index();
    }
}
