<?php
namespace App\News\Admin;

use Topnew\Data;

class SessionController extends Base
{
    public function home() {
        $this->set('page.title', 'Admin - User sessions management');

        $day30 = date('Y-m-d H:i:s', strtotime('-30 day'));
        $this->db->where('ts', '<', $day30)->delete('log_login');

        // also remove those already expired
        $now = date('Y-m-d H:i:s');
        $this->db->run("
            DELETE FROM log_login
            WHERE (exp < '" . $now . "')
            OR (auto > 0 AND datediff('" . $now . "', ts) > auto)
            or (auto = 0 AND datediff('" . $now . "', ts) > 2)
        ");

        $data = Data::clean(['uid' => 'int', 'pgno' => 'int', 'del' => 'int']);
        if ($data['del']) {
            $this->db->where('id', $data['del'])->delete('log_login');
        }
        $rows = $this->db->from('log_login');
        if ($data['uid']) {
            $rows = $rows->where('uid', $data['uid']);
        }
        $rows = $rows->order('ts DESC,id DESC')->pgno($data['pgno'], $ttl, $max);
        $this->set('stat', $rows);
        $this->set('pgno', $data['pgno']);
        $this->set('pgno_ttl', $ttl);
        $this->set('pgno_max', $max);
        $this->set('pgno_uid', $data['uid']);
        $this->set('pgno_url', '/admin/session?uid=' . $data['uid'] . '&pgno=');
        if ($rows) {
            $this->set('names', $this->db->select('uid,fname,lname,email')->from('blog_user')
                ->where('uid', array_column($rows, 'uid'))->arr());
        }
    }
}
