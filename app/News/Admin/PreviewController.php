<?php
namespace App\News\Admin;

use App\News\Base As NewsBase;
use Topnew\Auth;

class PreviewController extends NewsBase
{
    public $view;

    public function __construct()
    {
        // If NewsBase has its own constructor, call it.
        parent::__construct();

        // Set the page header
        $this->set('page.head', '/Admin/head.tpl');

        if (isset($_GET['tpl'])) {
            // Allow letters, numbers, underscores, dashes, and forward slashes
            $tpl = preg_replace('/[^a-zA-Z0-9_\/-]/', '', $_GET['tpl']);

            // Prevent directory traversal by disallowing ".."
            if (strpos($tpl, '..') !== false) {
                throw new Exception('Invalid template path.');
            }

            // Build the view template path
            $this->view = $tpl . '.tpl';
        } else {
            $this->view = false;
        }

        // Set the page footer
        $this->set('page.foot', '/Admin/foot.tpl');
    }
}

