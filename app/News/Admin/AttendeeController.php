<?php
namespace App\News\Admin;

use Topnew\Data;
use App\News\HtmlHelper;

class AttendeeController extends Base
{
    public function home($id = 0) {
        $this->set('page.title', 'HR Daily webinars');
        $can_del = $this->inGrp('Webinar Delete');
        if (!$this->inGrp('Webinar Regos') && !$can_del) {
            $this->view = 'err';
            return;
        }
        $this->set('can_del', $can_del);

        $arr = explode('-', $id);
        $tid = isset($arr[0]) && is_numeric($arr[0]) ? ceil($arr[0]) : 0;
        $seq = isset($arr[1]) && is_numeric($arr[1]) ? ceil($arr[1]) : 0;
        $reg = $this->db->from('reg_event')->where('tid', $tid)->where('seq', $seq)->row();
        if ($reg) {
            return $this->info($reg);
        }
        $data = $this->searchForm($tid);
        $this->set('form', $data);
        if ($data['tid']) {
            return $this->event($data);
        }
        $data['status'] = '';

        $rows = $this->searchSQL($data)
            ->rightJoin(['n' => 'blog_topic'], 'n.tid = e.tid')
            ->select("n.tid,n.rank AS start,n.meta,
                sum(if(e.status != 'Fail',1,0)) reg, sum(if(e.status='Paid',1,0)) paid")
            ->where('n.cat = ' . $this->blog['cat_event'])
            ->where('n.meta', 'NOT LIKE', '%"Event Type":"Webcast"%')
            ->order('n.rank DESC, n.tid DESC')
            ->group('n.tid') // event start date
            ->limit($data['pgsize'])->pgno($data['pgno'], $pgno_ttl, $pgno_max);
        foreach ($rows as $k => $v) {
            $meta = json_decode($v['meta'], 1);
            $rows[$k]['exp'] = $v['start'] < date('Ymd') ? 1 : 0;
            $rows[$k]['start'] = substr($v['start'], 0, 4) . '-' .substr($v['start'], 4, 2) . '-' . substr($v['start'], 6, 2);
            $rows[$k]['Cost'] = isset($meta['Cost']) && $meta['Cost'] > 0 ? '' : 'P';
            $rows[$k]['title'] = isset($meta['summary_short']) ? $meta['summary_short'] : 'NA';
            unset($rows[$k]['meta']);
            if (!$v['reg']) {
                $rows[$k]['reg'] = '';
            }
            if (!$v['paid']) {
                $rows[$k]['paid'] = '';
            }
        }

        $this->set('pgno', $data['pgno']);
        $this->set('pgno_url', '/admin/attendee' . $data['url']);
        $this->set('pgno_max', $pgno_max);
        $this->set('data', $rows);
    }

    private function event($data) {
        $this->view = 'event';
        $meta = $this->db->select('meta')->from('blog_topic')->where('tid', $data['tid'])->val();
        $meta = json_decode($meta, 1);
        $title = isset($meta['summary_short']) ? $meta['summary_short'] : 'NA';
        $this->set('title', $title ?: 'NA');

        if ($data['cmd'] == 'del-webinar') {
            $this->db->where('tid', $data['tid'])->delete('reg_event');
            $this->set('msg', 'Webinar deleted!');
            return;
        }

        $rows = $this->searchSQL($data)->order('e.pay_date DESC');
        if ($data['cmd'] == 'Download CSV') {
            $rows = $rows->select("tid Webinar,seq,uid,status,fname,lname,email,phone,position,company,
                addr Postal_Address,city Suburb,state,zip Postcode,country,consent,pay_ip,pay_date,
                pay_method as Card_type,
                if (status='Paid',pay_amt / 100,0) Amount,
                pay_ref,pay_response Bank_notes,q1,q2,q3,notes")->all();
            $this->downloadCsv($rows, 'webinar-reg-' . $data['tid']);
            return;
        }

        $rows = $rows->limit($data['pgsize'])->pgno($data['pgno'], $pgno_ttl, $pgno_max);
        foreach ($rows as $k => $v) {
            $rows[$k]['seq'] = str_pad($v['seq'], 4, 0, STR_PAD_LEFT);
        }
        $this->set('data', $rows);
        $this->set('pgno', $data['pgno']);
        $this->set('pgno_url', '/admin/attendee/' . $data['tid'] . $data['url']);
        $this->set('pgno_max', $pgno_max);
    }

    private function info($reg) {
        $reg['seq'] = str_pad($reg['seq'], 4, 0, STR_PAD_LEFT);
        $reg = $this->saveData($reg);
        $this->view = 'info';
        $this->set('form', $reg);

        $meta = $this->db->select('meta')->from('blog_topic')->where('tid', $reg['tid'])->val();
        $meta = json_decode($meta, 1);
        $title = isset($meta['summary_short']) ? $meta['summary_short'] : 'NA';
        $this->set('title', $title ?: 'NA');
    }

    private function saveData($reg) {
        $data = [
            'fname', 'lname', 'email', 'phone', 'position', 'company', 'addr', 'city', 'state', 'country', 'zip', 'notes', 'cmd'
        ];
        $data = Data::clean($data);
        if (!$data['cmd']) {
            return $reg;
        }

        if ($data['cmd'] == 'Delete') {
            $this->db->where('tid', $reg['tid'])->where('seq', $reg['seq'])->delete('reg_event');
            $this->set('msg', 'Registration #' . $reg['tid'] . '-' . $reg['seq'] . ' has been deleted');
            return $reg;
        }

        // the following is save
        unset($data['cmd']);
        foreach ($data as $k => $v) {
            if ($reg[$k] != $v) {
                $reg[$k] = $v;
            } else {
                unset($data[$k]);
            }
        }
        if ($data) {
            $this->db->where('tid', $reg['tid'])->where('seq', $reg['seq'])->update('reg_event', $data);
        }
        return $reg;
    }

    private function searchSQL($data) {
        $rows = $this->db->from('reg_event AS e');
        $arr = ['uid', 'tid', 'pay_method', 'state'];
        foreach ($arr as $k) {
            if ($data[$k]) {
                $rows = $rows->where('e.' . $k, $data[$k]);
            }
        }
        $arr = ['email', 'phone', 'company', 'city', 'pay_ref'];
        foreach ($arr as $k) {
            if ($data[$k]) {
                $rows = $rows->where('e.' . $k, 'like', '%' . $data[$k] . '%');
            }
        }
        if ($data['name']) {
            $rows = $rows->where('OR', [
                ['e.fname', 'like', '%' . $data['name'] . '%'],
                ['e.lname', 'like', '%' . $data['name'] . '%'],
            ]);
        }
        if ($data['year']) {
            $rows = $rows->where('n.rank', 'start', $data['year']);
        }
        if ($data['status'] == 'Premium + Paid') {
            $rows = $rows->where('e.status', '!=', 'Fail');
        } elseif ($data['status']) {
            $rows = $rows->where('e.status', $data['status']);
        }
        return $rows;
    }

    private function searchForm($tid) {
        $data = [
            'uid' => 'int',
            'tid' => 'int',
            'pgno'=> 'int',
            'pgsize' => 'int',
            'pay_method' => 'int',
            'year' => 'int',
            'status', 'name', 'email', 'phone', 'company', 'city', 'state', 'pay_ref',
            'cmd',
        ];
        $data = Data::clean($data);
        $int = ['uid', 'tid', 'pay_method', 'pgno'];
        foreach ($int as $k) {
            if ($data[$k] < 1) {
                $data[$k] = '';
            }
        }
        $tid = ceil($tid);
        if ($tid > 0) {
            $data['tid'] = $tid;
        }
        $data['pgno'] = max(1, $data['pgno']); //min 1
        if ($data['pgsize'] < 1 || $data['pgsize'] > 50) {
            $data['pgsize'] = $tid ? 50 : 10;
        }

        /*$this->set('method_options', HtmlHelper::getHtml([
            '' => 'Card', 24 => 'Visa', 25 => 'Mastercard', 23 => 'Amex',
        ], ''));
        $this->set('status_options', HtmlHelper::getHtml([
            '' => 'Status', 'Premium' => 'Premium', 'Paid' => 'Paid', 'Fail' => 'Fail',
        ], ''));
        $this->set('state_options', HtmlHelper::addrState());*/
        $url = '';
        foreach ($data as $k => $v) {
            if ($v && $k != 'pgno' && $k != 'tid' && $k != 'cmd') {
                if ($k != 'pgsize' || $v != 10) {
                    $url .= '&' . $k . '=' . $v;
                }
            }
        }
        $data['url'] = '?' . substr($url . '&pgno=', 1);
        return $data;
    }
}
