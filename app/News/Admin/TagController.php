<?php
namespace App\News\Admin;

use Topnew\Data;
use App\Utils\ComponentLogger;

class TagController extends Base {

    // MAX ROOT DEPTH for ANY BRANCH limitation
    private $MAX_TAG_ID_LENGTH = 10;
    // MAX SIBLING IMPL LIMITATION
    private $MAX_SIBLINGS_PER_PARENT = 100;

    private $logger;

    protected $table = 'tag';
    protected $view_dir = 'Admin/Tag';
    protected $require_login = true;

    public function __construct() {
        parent::__construct();
        $this->logger = new ComponentLogger('tag_controller');
    }

    public function home() {
        // Add cache control headers
        header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
        header("Cache-Control: post-check=0, pre-check=0", false);
        header("Pragma: no-cache");

        $this->view = '/Admin/Tag/tag.tpl';

        // Get common tags (already working)
        $comm_tags = $this->getCommTags();
        $this->set('comm_tags', $comm_tags);

        // Get selected tags (already working)
        $sel_tags = $this->getSelTags();
        $this->set('sel_tags', $sel_tags);

        // Set page title for browser
        $this->set('page.title', 'Tag Manager');

        // $this->data['grouped_tags'] = $grouped_tags;
        $this->data['title'] = 'Tag Manager';
        $this->set('title', 'Tag Manager');
    }
    
    public function save() {
        $id = $this->post('id');
        $name = $this->post('name');
        
        if ($id && $name) {
            $this->db->update($this->table, ['name' => $name], ['id' => $id]);
            $this->data['msg'] = 'Tag updated successfully';
        }
        
        $this->redirect('/admin/tag');
    }

    private function getSelTags($tid = 0) {
        $rows = $this->db->select('c.cat, c.name, c.info')
            ->from('blog_cat c, blog_tag t')
            ->where('c.cat = t.tag')
            ->where('t.tid', $tid)
            ->where('c.cat', 'like', '2%')
            ->order('t.sort')
            ->rows();
        $sel_tags = [];
        foreach ($rows as $r) {
            $sel_tags[$r['cat']] = $this->getCatName($r['name'], $r['info']);
        }
        return $sel_tags;
    }

    private function getCommTags() {
        $rows = $this->db->select('cat, name, info')
            ->from('blog_cat')
            ->where('cat', 'like', '21__%')
            ->order(in_array($this->blog['site']['id'], [2,4]) ? 'cat' : "if (info='', name, info)")
            ->rows();//2:OHS
        $comm_tags = [];
        
        // Check if show_ids parameter is set to true
        $showIds = isset($_GET['dev_mode']) && $_GET['dev_mode'] === 'true';
        
        foreach ($rows as $r) {
            if ($showIds) {
                // Show tag ID with the name when show_ids=true
                $comm_tags[$r['cat']] = $r['cat'] . " " . $this->getCatName($r['name'], $r['info']);
            } else {
                // Normal display without IDs
                $comm_tags[$r['cat']] = $this->getCatName($r['name'], $r['info']);
            }
        }

        $this->logger->debug('tags: ' . print_r($comm_tags, true));
        return $comm_tags;
    }

    /**
     * Update the tag hierarchy based on drag and drop operations
     */
    public function updateHierarchy() {
        try {
            // Get the parameters from the request
            $movedTagId = isset($_POST['movedTagId']) ? $_POST['movedTagId'] : null;
            $targetParentId = isset($_POST['targetParentId']) ? $_POST['targetParentId'] : '21';
            $targetPosition = isset($_POST['targetPosition']) ? intval($_POST['targetPosition']) : null;
            
            $this->logger->info("updateHierarchy called with: movedTagId=$movedTagId, targetParentId=$targetParentId, targetPosition=$targetPosition");

            if (!$movedTagId || $targetPosition === null) {
                $this->logger->error('Missing required parameters');
                $this->jsonResponse(['success' => false, 'message' => 'Missing required parameters']);
                return;
            }

            // Get the current parent ID of the moved tag
            $currentParentId = $this->getTagParentId($movedTagId);
            $this->logger->info("Current parent ID of $movedTagId: $currentParentId");

            // Log the articles that will be affected by this move
            $affectedArticles = $this->getArticlesLinkedToTag($movedTagId);
            $this->logger->info("TAG MOVE OPERATION: Tag $movedTagId affects " . count($affectedArticles) . " articles: " . implode(', ', $affectedArticles));

            // Check if the tag has children (don't allow moving tags with children)
            if ($this->tagHasChildren($movedTagId)) {
                $this->logger->warning("Tag $movedTagId has children, cannot move");
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Moving tags with children is not supported'
                ]);
                return;
            }

            // If moving within the same parent, use the simpler swap approach
            if ($currentParentId == $targetParentId) {
                $this->logger->info("SAME PARENT REORDERING: Tag $movedTagId within parent $currentParentId from position to position $targetPosition");
                $success = $this->handleSameParentReordering($movedTagId, $targetParentId, $targetPosition);
            } else {
                // Moving to a different parent - more complex operation
                $this->logger->info("PARENT CHANGE OPERATION: Tag $movedTagId moving from parent $currentParentId to parent $targetParentId at position $targetPosition");
                $success = $this->handleParentChange($movedTagId, $targetParentId, $targetPosition);
            }
            
            if ($success) {
                $this->jsonResponse(['success' => true, 'message' => 'Tag position updated successfully']);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to update tag position']);
            }
        } catch (Exception $e) {
            $this->logger->error('Exception in updateHierarchy: ' . $e->getMessage());
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Handle reordering of tags within the same parent
     * 
     * @param string $movedTagId The ID of the tag being moved
     * @param string $parentId The parent ID
     * @param int $targetPosition The target position (0-based)
     * @return bool Success status
     */
    private function handleSameParentReordering($movedTagId, $parentId, $targetPosition) {
        try {
            // Get all tags with this parent, in order
            $tags = $this->getTagsAtLevel($parentId);
            
            // Find the current position of the moved tag
            $currentPosition = array_search($movedTagId, $tags);
            if ($currentPosition === false) {
                $this->logger->error("Tag $movedTagId not found in parent $parentId");
                return false;
            }

            $this->logger->info("Current position: $currentPosition, Target position: $targetPosition");

            // If the position hasn't changed, do nothing
            if ($currentPosition == $targetPosition) {
                $this->logger->info("Position unchanged, no action needed");
                return true;
            }
            
            // Start a transaction
            $this->db->beginTransaction();
            
            // Simple swap approach - just swap the IDs of the moved tag and the target tag
            if ($targetPosition < count($tags)) {
                $targetTagId = $tags[$targetPosition];
                $this->logger->info("Will swap IDs between $movedTagId and $targetTagId");

                // Create a temporary negative ID to avoid unique constraint violations
                $tempId = -1 * time();

                // First update moved tag to temp ID
                $result = $this->db->update('blog_cat',
                    ['cat' => $tempId],
                    ['cat' => $movedTagId]
                );

                if (!$result) {
                    $this->logger->error("Failed to update moved tag to temp ID: " . $this->db->err(1));
                    $this->db->rollback();
                    return false;
                }
                
                // Update target tag to moved tag's ID
                $result = $this->db->update('blog_cat',
                    ['cat' => $movedTagId],
                    ['cat' => $targetTagId]
                );

                if (!$result) {
                    $this->logger->error("Failed to update target tag: " . $this->db->err(1));
                    $this->db->rollback();
                    return false;
                }

                // Update temp ID to target tag's ID
                $result = $this->db->update('blog_cat',
                    ['cat' => $targetTagId],
                    ['cat' => $tempId]
                );

                if (!$result) {
                    $this->logger->error("Failed to update temp ID to target ID: " . $this->db->err(1));
                    $this->db->rollback();
                    return false;
                }
                
                // Now update references in blog_tag
                // First update moved tag references to temp ID
                $result = $this->db->update('blog_tag',
                    ['tag' => $tempId],
                    ['tag' => $movedTagId]
                );
                
                // Update target tag references to moved tag's ID
                $result = $this->db->update('blog_tag',
                    ['tag' => $movedTagId],
                    ['tag' => $targetTagId]
                );
                
                // Update temp ID references to target tag's ID
                $result = $this->db->update('blog_tag',
                    ['tag' => $targetTagId],
                    ['tag' => $tempId]
                );
                
                $this->logger->info("Swapped IDs between $movedTagId and $targetTagId");
            } else {
                // If target position is beyond the end, we need a different approach
                $this->logger->info("Target position is beyond the end, using alternative approach");
                // Find the last tag and swap with it
                $lastTagId = end($tags);
                $lastPosition = count($tags) - 1;
                
                if ($currentPosition != $lastPosition) {
                    // Swap with the last tag
                    $tempId = -1 * time();
                    
                    // First update moved tag to temp ID
                    $this->db->update('blog_cat',
                        ['cat' => $tempId],
                        ['cat' => $movedTagId]
                    );
                    
                    // Update last tag to moved tag's ID
                    $this->db->update('blog_cat',
                        ['cat' => $movedTagId],
                        ['cat' => $lastTagId]
                    );
                    
                    // Update temp ID to last tag's ID
                    $this->db->update('blog_cat',
                        ['cat' => $lastTagId],
                        ['cat' => $tempId]
                    );
                    
                    // Update references in blog_tag
                    $this->db->update('blog_tag',
                        ['tag' => $tempId],
                        ['tag' => $movedTagId]
                    );
                    
                    $this->db->update('blog_tag',
                        ['tag' => $movedTagId],
                        ['tag' => $lastTagId]
                    );
                    
                    $this->db->update('blog_tag',
                        ['tag' => $lastTagId],
                        ['tag' => $tempId]
                    );
                    
                    $this->logger->info("Swapped IDs between $movedTagId and $lastTagId");
                }
            }

            // Commit the transaction
            $this->db->commit();
            $this->logger->info("Same parent reordering completed successfully");
            return true;
        } catch (Exception $e) {
            // Rollback on error
            $this->db->rollback();
            $this->logger->error("Error in handleSameParentReordering: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle moving a tag to a different parent
     * 
     * @param string $movedTagId The ID of the tag being moved
     * @param string $targetParentId The target parent ID
     * @param int $targetPosition The target position (0-based)
     * @return bool Success status
     */
    private function handleParentChange($movedTagId, $targetParentId, $targetPosition) {
        try {
            // Start a transaction
            $this->db->beginTransaction();
            
            // Get the tag info
            $tagInfo = $this->db->select('name, info')
                ->from('blog_cat')
                ->where('cat', $movedTagId)
                ->row();
            
            if (!$tagInfo) {
                $this->logger->error("Tag $movedTagId not found");
                return false;
            }

            // Generate a new ID for the tag in the target parent
            $newTagId = $this->generateTagId($targetParentId, $targetPosition);
            if (!$newTagId) {
                $this->logger->error("Failed to generate new tag ID");
                return false;
            }

            $this->logger->info("Generated new tag ID: $newTagId for moved tag $movedTagId");

            // Get affected articles before making changes
            $affectedArticles = $this->getArticlesLinkedToTag($movedTagId);
            $this->logger->info("PARENT CHANGE: Tag $movedTagId -> $newTagId affects articles: " . implode(', ', $affectedArticles));

            // Update the tag ID in blog_cat
            $this->db->update('blog_cat',
                ['cat' => $newTagId],
                ['cat' => $movedTagId]
            );
            $this->logger->info("PARENT CHANGE: Updated blog_cat table - changed tag ID from $movedTagId to $newTagId");

            // Update references in blog_tag
            $this->db->update('blog_tag',
                ['tag' => $newTagId],
                ['tag' => $movedTagId]
            );
            $this->logger->info("PARENT CHANGE: Updated blog_tag table - changed tag references from $movedTagId to $newTagId for " . count($affectedArticles) . " articles");
            
            // Update the sort order of tags in the target parent
            $this->updateSortOrderAfterMove($targetParentId, $newTagId, $targetPosition);
            
            // Commit the transaction
            $this->db->commit();
            $this->logger->info("Parent change completed successfully");
            return true;
        } catch (Exception $e) {
            // Rollback on error
            $this->db->rollback();
            $this->logger->error("Error in handleParentChange: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get the parent ID of a tag
     * 
     * @param string $tagId The tag ID
     * @return string The parent ID
     */
    private function getTagParentId($tagId) {
        $tagLength = strlen($tagId);
        
        // If it's a top-level tag (4 characters), parent is root (21)
        if ($tagLength <= 4) {
            return '21';
        }
        
        // Otherwise, parent is the tag ID without the last 2 characters
        return substr($tagId, 0, $tagLength - 2);
    }

    /**
     * Generate a new tag ID for a specific parent and position
     * 
     * @param string $parentId The parent ID
     * @param int $position The target position (0-based)
     * @return string|false The new tag ID or false if generation failed
     */
    private function generateTagId($parentId, $position) {
        // Get existing tags at this level
        $tags = $this->getTagsAtLevel($parentId);
        
        // Check if we've reached the maximum number of siblings
        if (count($tags) >= $this->MAX_SIBLINGS_PER_PARENT) {
            $this->logger->warning("Maximum number of siblings reached for parent $parentId: " . count($tags));
            return false;
        }
        
        // If position is beyond the end, add at the end
        if ($position >= count($tags)) {
            // Find the next available suffix
            $maxSuffix = 0;
            foreach ($tags as $tag) {
                $suffix = intval(substr($tag, strlen($parentId)));
                if ($suffix > $maxSuffix) {
                    $maxSuffix = $suffix;
                }
            }
            
            // Create new ID with the next suffix
            $newSuffix = str_pad($maxSuffix + 1, 2, '0', STR_PAD_LEFT);
            return $parentId . $newSuffix;
        }
        
        // If inserting at a specific position, try to find a gap
        if ($position > 0 && isset($tags[$position-1]) && isset($tags[$position])) {
            $prevTag = $tags[$position-1];
            $nextTag = $tags[$position];
            
            $prevSuffix = intval(substr($prevTag, strlen($parentId)));
            $nextSuffix = intval(substr($nextTag, strlen($parentId)));
            
            // If there's a gap, use the middle value
            if ($nextSuffix - $prevSuffix > 1) {
                $newSuffix = str_pad($prevSuffix + 1, 2, '0', STR_PAD_LEFT);
                return $parentId . $newSuffix;
            }
        }
        
        // If inserting at position 0, check if there's space before the first tag
        if ($position == 0 && !empty($tags)) {
            $firstTag = $tags[0];
            $firstSuffix = intval(substr($firstTag, strlen($parentId)));
            
            if ($firstSuffix > 1) {
                // There's space before the first tag
                return $parentId . '01';
            }
        }
        
        // If we can't find a gap, add at the end
        $maxSuffix = 0;
        foreach ($tags as $tag) {
            $suffix = intval(substr($tag, strlen($parentId)));
            if ($suffix > $maxSuffix) {
                $maxSuffix = $suffix;
            }
        }
        
        // Create new ID with the next suffix
        $newSuffix = str_pad($maxSuffix + 1, 2, '0', STR_PAD_LEFT);
        return $parentId . $newSuffix;
    }

    /**
     * Update the sort order after moving a tag
     * 
     * @param string $parentId The parent ID
     * @param string $newTagId The new tag ID
     * @param int $position The target position (0-based)
     */
    private function updateSortOrderAfterMove($parentId, $newTagId, $position) {
        // Get all tags with this parent
        $tags = $this->getTagsAtLevel($parentId);
        
        // Insert the new tag at the specified position
        array_splice($tags, $position, 0, [$newTagId]);
        
        // No need to update sort order as we're using the cat ID for sorting
        $this->logger->info("Tag order updated, using cat IDs for sorting");
    }

    /**
     * Get all tags at a specific parent level, ordered by sort
     * 
     * @param string $parentId The parent ID
     * @return array Array of tag IDs
     */
    private function getTagsAtLevel($parentId) {
        // Get all tags with this parent, ordered by sort
        $rows = $this->db->select('cat')
            ->from('blog_cat')
            ->where('cat', 'like', $parentId . '%')
            ->where('LENGTH(cat)', strlen($parentId) + 2)  // Only direct children
            ->rows();
        
        $tags = [];
        foreach ($rows as $row) {
            $tags[] = $row['cat'];
        }
        
        $this->logger->debug("Tags at parent $parentId: " . json_encode($tags));
        return $tags;
    }

    /**
     * Send a JSON response and exit
     * 
     * @param array $data The data to encode as JSON
     */
    private function jsonResponse($data) {
        // Clear any output buffers to prevent HTML from being sent before JSON
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        // Set the content type header
        header('Content-Type: application/json');
        
        // Disable error reporting for this response
        error_reporting(0);
        
        // Output the JSON data
        echo json_encode($data);
        exit;
    }

    /**
     * Ajax API update tag
     */
    public function update() {
        // Enable detailed error logging
        $this->logger->info('TagController::update() method called at ' . date('Y-m-d H:i:s'));
        $this->logger->debug('POST data: ' . print_r($_POST, true));

        try {
            $id = isset($_POST['id']) ? $_POST['id'] : null;
            $name = isset($_POST['name']) ? $_POST['name'] : null;

            $this->logger->info('Received id: ' . $id . ', name: ' . $name);
            
            if ($id && $name) {
                // Get the old tag name for logging
                $oldTagInfo = $this->db->select('name, info')
                    ->from('blog_cat')
                    ->where('cat', $id)
                    ->row();
                $oldName = $oldTagInfo ? ($oldTagInfo['info'] ?: $oldTagInfo['name']) : 'Unknown';

                // Get affected articles for logging
                $affectedArticles = $this->getArticlesLinkedToTag($id);

                // Log the SQL query we're about to execute
                $query = "UPDATE blog_cat SET info = '{$name}' WHERE cat = {$id}";
                $this->logger->debug('About to execute query: ' . $query);

                // Update the info field in blog_cat table instead of name
                $result = $this->db->update('blog_cat', ['info' => $name], ['cat' => $id]);

                // Log the result with comprehensive details
                if ($result) {
                    $this->logger->info("TAG UPDATE SUCCESS: Tag $id renamed from '$oldName' to '$name' - affects " . count($affectedArticles) . " articles: " . implode(', ', $affectedArticles));
                } else {
                    $this->logger->error("TAG UPDATE FAILED: Failed to rename tag $id from '$oldName' to '$name' - DB error: " . $this->db->err(1));
                }

                // Return JSON response
                $this->jsonResponse(['success' => ($result !== false), 'message' => $this->db->err(1) ?: 'Tag updated successfully']);
            } else {
                $this->logger->error('Missing required fields: id=' . $id . ', name=' . $name);
                $this->jsonResponse(['success' => false, 'message' => 'Missing required fields']);
            }
        } catch (\Exception $e) {
            $this->logger->error('Exception in TagController::update(): ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Ajax API to delete a tag
     */
    public function delete() {
        try {
            // Start output buffering to catch any unexpected output
            ob_start();
            
            // Get the tag ID from the request
            $id = isset($_POST['id']) ? $_POST['id'] : null;
            $forceDelete = isset($_POST['forceDelete']) && $_POST['forceDelete'] === 'true';
            $getArticles = isset($_POST['getArticles']) && $_POST['getArticles'] === 'true';
            $checkOnly = isset($_POST['checkOnly']) && $_POST['checkOnly'] === 'true';
            
            // Validate the tag ID
            if (!$id) {
                $this->jsonResponse(['success' => false, 'message' => 'Tag ID is required']);
                return;
            }
            
            // Log the request
            $this->logger->info("Delete tag request: ID={$id}, forceDelete={$forceDelete}, getArticles={$getArticles}, checkOnly={$checkOnly}");

            // Check if the tag has children
            $children = $this->db->select('cat')
                ->from('blog_cat')
                ->where('cat', 'like', $id . '%')
                ->where('cat', '!=', $id)
                ->rows();

            if (!empty($children)) {
                $this->logger->warning('Cannot delete tag with children: ' . $id . ', children count: ' . count($children));
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Cannot delete this tag because it has child tags. Please delete the children first.',
                    'hasChildren' => true
                ]);
                return;
            }
            
            // Check if the tag is used in any articles
            $usages = $this->db->select('COUNT(*) as count')
                ->from('blog_tag')
                ->where('tag', $id)
                ->row();
            
            // Store the count of affected articles
            $affectedArticles = $usages ? $usages['count'] : 0;
            $this->logger->info("Tag {$id} is used in {$affectedArticles} articles");
            
            // If checkOnly is true, just return whether the tag has articles
            if ($checkOnly) {
                $this->jsonResponse([
                    'success' => true,
                    'hasArticles' => ($affectedArticles > 0),
                    'articleCount' => $affectedArticles
                ]);
                return;
            }
            
            // If getArticles is true, return the list of articles using this tag
            if ($getArticles && $affectedArticles > 0) {
                try {
                    // Determine which table to use for articles
                    $articleTable = 'blog_topic'; // Default to blog_topic
                    $articleIdField = 'tid';      // Default to tid
                    
                    // Get a sample article to determine the correct field names
                    $sampleArticle = $this->db->select('*')
                        ->from($articleTable)
                        ->limit(1)
                        ->row();
                    
                    // Build the query using the fluent interface
                    $articles = $this->db->select(
                            "t.tid as id, " . 
                            (isset($sampleArticle['headline']) ? "a.headline" : "a.title") . " as title, " .
                            (isset($sampleArticle['created']) ? "a.created" : "a.date_pub") . " as date_pub, " .
                            "a.acs as acs, " .
                            "CASE " .
                            "  WHEN a.acs = 0 THEN 'published' " .
                            "  WHEN a.acs = 7 THEN 'unpublished' " .
                            "  WHEN a.acs = 8 THEN 'draft' " .
                            "  WHEN a.acs = 9 THEN 'deleted' " .
                            "  ELSE 'published' " .
                            "END as status"
                        )
                        ->from('blog_tag t')
                        ->leftJoin([$articleTable . ' a'], "t.tid = a." . $articleIdField)
                        ->where('t.tag', $id)
                        ->order(isset($sampleArticle['created']) ? "a.created DESC" : "a.date_pub DESC")
                        ->rows();
                    
                    $this->logger->info('Found ' . count($articles) . ' articles using tag ' . $id);
                    
                    $this->jsonResponse([
                        'success' => true,
                        'articles' => $articles,
                        'totalCount' => $usages['count'],
                        'tagId' => $id
                    ]);
                    return;
                } catch (\Exception $e) {
                    $this->logger->error("Error getting articles: " . $e->getMessage());
                    $this->jsonResponse([
                        'success' => false,
                        'message' => 'Error retrieving articles: ' . $e->getMessage()
                    ]);
                    return;
                }
            }
            
            // If the tag is used and forceDelete is false, return requireConfirmation
            if ($affectedArticles > 0 && !$forceDelete) {
                $this->jsonResponse([
                    'success' => false,
                    'requireConfirmation' => true,
                    'usageCount' => $affectedArticles,
                    'message' => 'This tag is used in ' . $affectedArticles . ' articles. Deleting this tag will remove it from all these articles.'
                ]);
                return;
            }
            
            // Get the tag name and affected articles before deleting
            $tagInfo = $this->db->select('name, info')
                ->from('blog_cat')
                ->where('cat', $id)
                ->row();
            $tagName = $tagInfo ? ($tagInfo['info'] ?: $tagInfo['name']) : 'Unknown';

            // Get list of affected articles for comprehensive logging
            $affectedArticlesList = [];
            if ($affectedArticles > 0) {
                $affectedArticlesList = $this->getArticlesLinkedToTag($id);
            }

            // If forceDelete is true or there are no references, remove all references to this tag
            if ($affectedArticles > 0) {
                $this->logger->info("TAG DELETE: About to delete $affectedArticles tag references for tag $id ($tagName) from articles: " . implode(', ', $affectedArticlesList));

                $this->db->where('tag', $id);
                $deleteRefs = $this->db->delete('blog_tag');

                if ($deleteRefs) {
                    $this->logger->info("TAG DELETE SUCCESS: Deleted $affectedArticles tag references for tag $id ($tagName) from blog_tag table");
                } else {
                    $this->logger->error("TAG DELETE ERROR: Failed to delete tag references for tag $id ($tagName) - DB error: " . $this->db->err(1));
                }
            }

            // Delete the tag
            $this->db->where('cat', $id);
            $result = $this->db->delete('blog_cat');

            // Log the comprehensive result
            if ($result) {
                $this->logger->info("TAG DELETE COMPLETE: Successfully deleted tag $id ($tagName) and removed from " . count($affectedArticlesList) . " articles: " . implode(', ', $affectedArticlesList));
            } else {
                $this->logger->error("TAG DELETE FAILED: Failed to delete tag $id ($tagName) from blog_cat table - DB error: " . $this->db->err(1));
            }

            // Return JSON response
            $this->jsonResponse([
                'success' => ($result !== false), 
                'message' => $result ? ($tagName . ' tag removed from ' . $affectedArticles . ' articles') : ('Failed to delete tag: ' . $this->db->err(1)),
                'tagName' => $tagName,
                'affectedArticles' => $affectedArticles
            ]);
        } catch (\Exception $e) {
            // Catch any unexpected exceptions
            $this->logger->error("Unexpected error in delete tag: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->jsonResponse([
                'success' => false,
                'message' => 'An unexpected error occurred: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Ajax API create tag
     */
    public function create() {
        $this->logger->info('TagController::create() method called at ' . date('Y-m-d H:i:s'));
        $this->logger->debug('POST data: ' . print_r($_POST, true));
        
        try {
            $name = isset($_POST['name']) ? $_POST['name'] : null;
            
            if ($name) {
                // Create a slug from the name
                $slug = strtolower(str_replace(' ', '-', $name));
                $this->logger->debug("Created slug: $slug for name: $name");

                // Check if a tag with this slug already exists
                $existingTag = $this->db->select('cat, name, info')
                    ->from('blog_cat')
                    ->where('name', $slug)
                    ->row();

                if ($existingTag) {
                    $this->logger->warning("Tag with slug '$slug' already exists with ID: " . $existingTag['cat']);
                    $this->jsonResponse([
                        'success' => false,
                        'message' => "A tag with the name '$name' already exists. Please use a different name."
                    ]);
                    return;
                }
                
                // Get all existing top-level tag IDs
                $existingTags = $this->db->select('cat')
                    ->from('blog_cat')
                    ->where("cat like '21%'")
                    ->where("LENGTH(cat) = 4")  // Ensure 4-digit IDs
                    ->enum();
                
                $this->logger->debug("Found " . count($existingTags) . " existing top-level tags");

                // Find the first available gap
                $tagId = null;
                for ($i = 1; $i <= 99; $i++) {
                    $candidateId = '21' . str_pad($i, 2, '0', STR_PAD_LEFT);
                    if (!in_array($candidateId, $existingTags)) {
                        $tagId = $candidateId;
                        $this->logger->debug("Found available gap at ID: $tagId");
                        break;
                    }
                }
                
                // If no gaps found, check if we can add a new ID at the end
                if (!$tagId) {
                    // Get the maximum tag ID
                    $maxTopLevelId = $this->db->select('max(cat)')
                        ->from('blog_cat')
                        ->where("cat like '21%'")
                        ->where("LENGTH(cat) = 4")
                        ->val();
                    
                    $this->logger->debug("No gaps found. Current max top-level ID: " . ($maxTopLevelId ?: 'none'));

                    // If no top-level tags exist yet, start with 2101
                    if (!$maxTopLevelId) {
                        $tagId = 2101;
                    } else {
                        // Extract the last two digits and increment
                        $lastTwoDigits = intval(substr($maxTopLevelId, -2));
                        $newLastTwoDigits = $lastTwoDigits + 1;

                        // Check if we've reached the maximum (99)
                        if ($newLastTwoDigits > 99) {
                            $this->logger->error("Cannot create tag: Maximum number of top-level tags reached (limit 99) and no gaps available");
                            $this->jsonResponse([
                                'success' => false,
                                'message' => 'Maximum number of top-level tags reached (limit 99) and no gaps available'
                            ]);
                            return;
                        }
                        
                        // Format with leading zero if needed
                        $formattedLastTwoDigits = str_pad($newLastTwoDigits, 2, '0', STR_PAD_LEFT);
                        
                        // Create the new tag ID with prefix 21
                        $tagId = '21' . $formattedLastTwoDigits;
                    }
                }
                
                $this->logger->info("Generated new tag ID: $tagId");

                // Insert the new tag
                $this->logger->debug("Attempting to insert new tag with ID: $tagId, name: $slug, info: $name");
                $result = $this->db->insert('blog_cat', [
                    'cat' => $tagId,
                    'name' => $slug,
                    'info' => $name
                ]);

                // Log comprehensive creation result
                if ($result) {
                    $this->logger->info("TAG CREATE SUCCESS: Created new top-level tag $tagId with name '$name' (slug: '$slug')");
                } else {
                    $this->logger->error("TAG CREATE FAILED: Failed to create tag $tagId with name '$name' - DB error: " . $this->db->err(1));
                }
                
                // Return JSON response
                $response = [
                    'success' => ($result !== false), 
                    'tagId' => $tagId,
                    'message' => $result ? 'Tag created successfully' : ('Failed to create tag: ' . $this->db->err(1))
                ];
                $this->logger->debug("Sending response: " . json_encode($response));
                $this->jsonResponse($response);
            } else {
                $this->logger->error("Missing required field: name");
                $this->jsonResponse(['success' => false, 'message' => 'Missing required field: name']);
            }
        } catch (\Exception $e) {
            $this->logger->error('Exception in TagController::create(): ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Ajax API create child tag
     */
    public function createChild() {
        $this->logger->info('TagController::createChild() method called at ' . date('Y-m-d H:i:s'));
        $this->logger->debug('POST data: ' . print_r($_POST, true));

        try {
            $parentId = isset($_POST['parentId']) ? $_POST['parentId'] : null;
            $name = isset($_POST['name']) ? $_POST['name'] : null;

            $this->logger->info("Attempting to create child tag with name: $name under parent ID: $parentId");

            if (!$parentId || !$name) {
                $this->logger->error("Missing required fields: parentId=$parentId, name=$name");
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Missing required fields: parentId and name are required'
                ]);
                return;
            }

            if (strlen($parentId) >= $this->MAX_TAG_ID_LENGTH) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Unable to create child tag - Hierarchy limit exceeded for the current tag hierarchy implementation.'
                ]);
                return;
            }
            
            // Create a slug from the name
            $slug = strtolower(str_replace(' ', '-', $name));
            
            // Check if a tag with this slug already exists
            $existingTag = $this->db->select('cat, name, info')
                ->from('blog_cat')
                ->where('name', $slug)
                ->row();
                
            if ($existingTag) {
                $this->logger->warning("Tag with slug '$slug' already exists with ID: " . $existingTag['cat']);
                $this->jsonResponse([
                    'success' => false,
                    'message' => "A tag with the name '$name' already exists. Please use a different name."
                ]);
                return;
            }

            // Verify parent exists
            $parentExists = $this->db->select('cat')
                ->from('blog_cat')
                ->where('cat', $parentId)
                ->row();

            if (!$parentExists) {
                $this->logger->error("Parent tag not found: $parentId");
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Parent tag not found'
                ]);
                return;
            }
            
            // Check if we've reached the maximum number of siblings
            $siblings = $this->getTagsAtLevel($parentId);
            if (count($siblings) >= $this->MAX_SIBLINGS_PER_PARENT) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Maximum number of siblings reached for this parent (' . $this->MAX_SIBLINGS_PER_PARENT . ')'
                ]);
                return;
            }
            
            // Find the next available child ID
            $parentIdLength = strlen($parentId);

            // Get all existing child tag IDs for this parent
            $existingChildTags = $this->db->select('cat')
                ->from('blog_cat')
                ->where('cat', 'LIKE', $parentId . '%')
                ->where('cat', '!=', $parentId)
                ->where('LENGTH(cat)', '=', $parentIdLength + 2)
                ->enum();

            $this->logger->debug("Found " . count($existingChildTags) . " existing child tags for parent $parentId");

            // Find the first available gap
            $tagId = null;
            for ($i = 1; $i <= 99; $i++) {
                $candidateId = $parentId . str_pad($i, 2, '0', STR_PAD_LEFT);
                if (!in_array($candidateId, $existingChildTags)) {
                    $tagId = $candidateId;
                    $this->logger->debug("Found available gap at ID: $tagId");
                    break;
                }
            }

            // If no gaps found, check if we can add a new ID at the end
            if (!$tagId) {
                $this->logger->warning("No gaps found for child tags of parent $parentId");
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Cannot create more child tags under this parent. Maximum limit of 99 children has been reached.'
                ]);
                return;
            }

            $this->logger->info("Generated new tag ID: $tagId");

            // Insert the new tag
            $result = $this->db->insert('blog_cat', [
                'cat' => $tagId,
                'name' => $slug,
                'info' => $name
            ]);

            // Log comprehensive creation result
            if ($result) {
                $this->logger->info("TAG CREATE CHILD SUCCESS: Created new child tag $tagId under parent $parentId with name '$name' (slug: '$slug')");
            } else {
                $this->logger->error("TAG CREATE CHILD FAILED: Failed to create child tag $tagId under parent $parentId with name '$name' - DB error: " . $this->db->err(1));
            }

            // Return JSON response
            $this->jsonResponse([
                'success' => ($result !== false),
                'tagId' => $tagId,
                'name' => $name,
                'message' => $result ? 'Child tag created successfully' : ('Failed to create child tag: ' . $this->db->err(1))
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Exception in TagController::createChild(): ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->jsonResponse(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    /**
     * Check if any tag in the hierarchy has children
     * 
     * @param array $items The hierarchy items
     * @return bool True if any tag has children
     */
    private function hasTagsWithChildren($items) {
        foreach ($items as $item) {
            $tagId = $item['id'];
            $tagLength = strlen($tagId);
            
            // Check if this tag has children in the database
            // A child tag must have longer length and start with the parent tag ID
            $children = $this->db->select('cat')
                ->from('blog_cat')
                ->where('cat', 'like', $tagId . '%')
                ->where('LENGTH(cat) > ' . $tagLength)  // Fix: Add the comparison operator inside the string
                ->rows();
            
            if (!empty($children)) {
                $this->logger->debug('Tag ' . $tagId . ' has ' . count($children) . ' children');
                return true;
            }
            
            // Recursively check children in the hierarchy
            if (isset($item['children']) && !empty($item['children'])) {
                if ($this->hasTagsWithChildren($item['children'])) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Get all article IDs linked to a specific tag
     *
     * @param string $tagId The tag ID
     * @return array Array of article IDs
     */
    private function getArticlesLinkedToTag($tagId) {
        $articles = $this->db->select('tid')
            ->from('blog_tag')
            ->where('tag', $tagId)
            ->rows();

        return array_column($articles, 'tid');
    }

    /**
     * Check if a specific tag has children
     *
     * @param string $tagId The tag ID to check
     * @return bool True if the tag has children
     */
    private function tagHasChildren($tagId) {
        $tagLength = strlen($tagId);

        $this->logger->debug('Checking if tag ' . $tagId . ' has children');
        
        // Check if this tag has children in the database
        // A child tag must have longer length and start with the parent tag ID
        $children = $this->db->select('cat')
            ->from('blog_cat')
            ->where('cat', 'like', $tagId . '%')
            ->where('LENGTH(cat) > ' . $tagLength)
            ->rows();
        
        $hasChildren = !empty($children);

        $this->logger->debug('Tag ' . $tagId . ' has children: ' . ($hasChildren ? 'yes' : 'no') . ', count: ' . count($children));

        return $hasChildren;
    }

    /**
     * Ajax API to unlink an article from a tag
     */
    public function unlinkArticle() {
        try {
            // Get the article ID and tag ID from the request
            $articleId = isset($_POST['articleId']) ? $_POST['articleId'] : null;
            $tagId = isset($_POST['tagId']) ? $_POST['tagId'] : null;

            // Add detailed logging
            $this->logger->info("unlinkArticle called with articleId: {$articleId}, tagId: {$tagId}");

            // Validate the inputs
            if (!$articleId) {
                $this->jsonResponse(['success' => false, 'message' => 'Article ID is required']);
                return;
            }

            if (!$tagId) {
                $this->jsonResponse(['success' => false, 'message' => 'Tag ID is required']);
                return;
            }

            // Check if the link exists
            $linkExists = $this->db->select('COUNT(*) as count')
                ->from('blog_tag')
                ->where('tid', $articleId)
                ->where('tag', $tagId)
                ->row();

            $linkExists = ($linkExists && $linkExists['count'] > 0);
            $this->logger->debug("Link exists check: " . ($linkExists ? 'true' : 'false'));

            if (!$linkExists) {
                $this->jsonResponse(['success' => false, 'message' => 'Article is not linked to this tag']);
                return;
            }

            // Get tag name for logging
            $tagInfo = $this->db->select('name, info')
                ->from('blog_cat')
                ->where('cat', $tagId)
                ->row();
            $tagName = $tagInfo ? ($tagInfo['info'] ?: $tagInfo['name']) : 'Unknown';

            // Remove the link
            $result = $this->db->where('tid', $articleId)
                ->where('tag', $tagId)
                ->delete('blog_tag');

            if ($result) {
                $this->logger->info("ARTICLE UNLINK SUCCESS: Unlinked article $articleId from tag $tagId ($tagName)");
                $this->jsonResponse(['success' => true, 'message' => 'Article unlinked from tag successfully']);
            } else {
                $error = $this->db->err(1);
                $this->logger->error("ARTICLE UNLINK FAILED: Failed to unlink article $articleId from tag $tagId ($tagName) - DB error: " . $error);
                $this->jsonResponse(['success' => false, 'message' => 'Failed to unlink article from tag: ' . $error]);
            }
        } catch (\Exception $e) {
            $this->logger->error("Error in unlinkArticle: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error unlinking article: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Ajax API to unlink multiple articles from a tag
     */
    public function unlinkMultipleArticles() {
        try {
            // Get the article IDs and tag ID from the request
            $articleIds = isset($_POST['articleIds']) ? $_POST['articleIds'] : null;
            $tagId = isset($_POST['tagId']) ? $_POST['tagId'] : null;

            // Add detailed logging
            $this->logger->info("unlinkMultipleArticles called with articleIds: " . print_r($articleIds, true) . ", tagId: {$tagId}");

            // Validate the inputs
            if (!$articleIds || !is_array($articleIds) || empty($articleIds)) {
                $this->jsonResponse(['success' => false, 'message' => 'Article IDs array is required']);
                return;
            }

            if (!$tagId) {
                $this->jsonResponse(['success' => false, 'message' => 'Tag ID is required']);
                return;
            }

            $successCount = 0;
            $errorCount = 0;
            $notLinkedCount = 0;
            $errors = [];

            foreach ($articleIds as $articleId) {
                try {
                    // Check if the link exists
                    $linkExists = $this->db->select('COUNT(*) as count')
                        ->from('blog_tag')
                        ->where('tid', $articleId)
                        ->where('tag', $tagId)
                        ->row();

                    if ($linkExists && $linkExists['count'] > 0) {
                        // Remove the link
                        $result = $this->db->where('tid', $articleId)
                            ->where('tag', $tagId)
                            ->delete('blog_tag');

                        if ($result) {
                            $successCount++;
                            $this->logger->info("Successfully unlinked article {$articleId} from tag {$tagId}");
                        } else {
                            $errorCount++;
                            $error = $this->db->err(1);
                            $errors[] = "Article {$articleId}: {$error}";
                            $this->logger->error("Failed to unlink article {$articleId}: {$error}");
                        }
                    } else {
                        $notLinkedCount++;
                        $this->logger->debug("Article {$articleId} is not linked to tag {$tagId}");
                    }
                } catch (\Exception $e) {
                    $errorCount++;
                    $errors[] = "Article {$articleId}: " . $e->getMessage();
                    $this->logger->error("Exception unlinking article {$articleId}: " . $e->getMessage());
                }
            }

            // Prepare response message and determine overall success
            $message = "Unlinked {$successCount} article(s) from tag";
            if ($notLinkedCount > 0) {
                $message .= " ({$notLinkedCount} were already unlinked)";
            }
            if ($errorCount > 0) {
                $message .= " ({$errorCount} failed)";
            }

            $this->logger->info("Unlink operation completed: {$successCount} success, {$notLinkedCount} not linked, {$errorCount} errors");

            // Consider it a success if:
            // 1. At least one article was unlinked successfully, OR
            // 2. All articles were already unlinked (goal achieved), OR
            // 3. No actual errors occurred (only "not linked" cases)
            $overallSuccess = ($successCount > 0) || ($errorCount == 0);

            $this->jsonResponse([
                'success' => $overallSuccess,
                'message' => $message,
                'successCount' => $successCount,
                'errorCount' => $errorCount,
                'notLinkedCount' => $notLinkedCount,
                'errors' => $errors
            ]);
        } catch (\Exception $e) {
            $this->logger->error("Error in unlinkMultipleArticles: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error unlinking articles: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Ajax API to view articles using a tag
     */
    public function viewArticles() {
        try {
            // Start output buffering to catch any unexpected output
            ob_start();
            
            // Get the tag ID from the request
            $id = isset($_POST['id']) ? $_POST['id'] : null;
            
            // Validate the tag ID
            if (!$id) {
                $this->jsonResponse(['success' => false, 'message' => 'Tag ID is required']);
                return;
            }
            
            // Log the request
            $this->logger->info("View articles request for tag ID={$id}");

            // Check if the tag exists
            $tagInfo = $this->db->select('name, info')
                ->from('blog_cat')
                ->where('cat', $id)
                ->row();

            if (!$tagInfo) {
                $this->jsonResponse(['success' => false, 'message' => 'Tag not found']);
                return;
            }

            $tagName = $tagInfo['info'] ?: $tagInfo['name'];

            // Check if the tag is used in any articles
            $usages = $this->db->select('COUNT(*) as count')
                ->from('blog_tag')
                ->where('tag', $id)
                ->row();

            // Store the count of articles using this tag
            $articleCount = $usages ? $usages['count'] : 0;
            $this->logger->info("Tag {$id} is used in {$articleCount} articles");
            
            if ($articleCount == 0) {
                $this->jsonResponse([
                    'success' => true,
                    'articles' => [],
                    'totalCount' => 0,
                    'message' => 'No articles are using this tag'
                ]);
                return;
            }
            
            // Determine which table to use for articles
            $articleTable = 'blog_topic'; // Default to blog_topic
            $articleIdField = 'tid';      // Default to tid
            
            // Get a sample article to determine the correct field names
            $sampleArticle = $this->db->select('*')
                ->from($articleTable)
                ->limit(1)
                ->row();
            
            // Build the query using the fluent interface
            $articles = $this->db->select(
                    "t.tid as id, " . 
                    (isset($sampleArticle['headline']) ? "a.headline" : "a.title") . " as title, " .
                    (isset($sampleArticle['created']) ? "a.created" : "a.date_pub") . " as date_pub, " .
                    "a.acs as acs, " .
                    "CASE " .
                    "  WHEN a.acs = 0 THEN 'published' " .
                    "  WHEN a.acs = 7 THEN 'unpublished' " .
                    "  WHEN a.acs = 8 THEN 'draft' " .
                    "  WHEN a.acs = 9 THEN 'deleted' " .
                    "  ELSE 'published' " .
                    "END as status"
                )
                ->from('blog_tag t')
                ->leftJoin([$articleTable . ' a'], "t.tid = a." . $articleIdField)
                ->where('t.tag', $id)
                ->order(isset($sampleArticle['created']) ? "a.created DESC" : "a.date_pub DESC")
                // ->limit(100) // Limit to 100 articles for performance
                ->rows();
            
            $this->logger->info('Found ' . count($articles) . ' articles using tag ' . $id);
            
            $this->jsonResponse([
                'success' => true,
                'articles' => $articles,
                'totalCount' => $articleCount,
                'tagId' => $id,
                'tagName' => $tagName
            ]);
        } catch (\Exception $e) {
            // Catch any unexpected exceptions
            $this->logger->error("Unexpected error in viewArticles: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->jsonResponse([
                'success' => false,
                'message' => 'An unexpected error occurred: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Export articles using a tag to CSV
     */
    public function exportCsv() {
        try {
            // Get the tag ID from the request
            $id = isset($_POST['id']) ? $_POST['id'] : null;
            
            // Validate the tag ID
            if (!$id) {
                echo "Error: Tag ID is required";
                return;
            }
            
            // Check if the tag exists
            $tagInfo = $this->db->select('name, info')
                ->from('blog_cat')
                ->where('cat', $id)
                ->row();
                
            if (!$tagInfo) {
                echo "Error: Tag not found";
                return;
            }
            
            $tagName = $tagInfo['info'] ?: $tagInfo['name'];
            $tagSlug = strtolower(preg_replace('/[^a-zA-Z0-9]+/', '-', $tagName));
            
            // Check if the tag is used in any articles
            $usages = $this->db->select('COUNT(*) as count')
                ->from('blog_tag')
                ->where('tag', $id)
                ->row();
            
            // Store the count of articles using this tag
            $articleCount = $usages ? $usages['count'] : 0;
            
            if ($articleCount == 0) {
                echo "No articles found using this tag";
                return;
            }
            
            // Determine which table to use for articles
            $articleTable = 'blog_topic'; // Default to blog_topic
            $articleIdField = 'tid';      // Default to tid
            
            // Get a sample article to determine the correct field names
            $sampleArticle = $this->db->select('*')
                ->from($articleTable)
                ->limit(1)
                ->row();
            
            // Build the query using the fluent interface - no limit for CSV export
            $articles = $this->db->select(
                    "t.tid as ID, " . 
                    (isset($sampleArticle['headline']) ? "a.headline" : "a.title") . " as Title, " .
                    (isset($sampleArticle['created']) ? "a.created" : "a.date_pub") . " as Publication_Date, " .
                    "CASE " .
                    "  WHEN a.acs = 0 THEN 'published' " .
                    "  WHEN a.acs = 7 THEN 'unpublished' " .
                    "  WHEN a.acs = 8 THEN 'draft' " .
                    "  WHEN a.acs = 9 THEN 'deleted' " .
                    "  ELSE 'published' " .
                    "END as Status"
                )
                ->from('blog_tag t')
                ->leftJoin([$articleTable . ' a'], "t.tid = a." . $articleIdField)
                ->where('t.tag', $id)
                ->order(isset($sampleArticle['created']) ? "a.created DESC" : "a.date_pub DESC")
                ->rows();
            
            // Generate a filename based on the tag name and current date
            $filename = 'articles-with-tag-' . $tagSlug . '-' . date('Ymd');
            
            // Download the CSV
            $this->downloadCsv($articles, $filename);
        } catch (\Exception $e) {
            // Log the error
            $this->logger->error("Error in exportCsv: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            // Output a simple error message
            echo "An error occurred while generating the CSV: " . $e->getMessage();
        }
    }

    /**
     * Search for articles that can be linked to a tag
     */
    public function searchArticles() {
        try {
            // Get the search query and tag ID from the request
            $search = isset($_POST['search']) ? trim($_POST['search']) : '';
            $tagId = isset($_POST['tagId']) ? $_POST['tagId'] : null;
            
            // Validate the inputs
            if (empty($search)) {
                $this->jsonResponse(['success' => false, 'message' => 'Search query is required']);
                return;
            }
            
            if (!$tagId) {
                $this->jsonResponse(['success' => false, 'message' => 'Tag ID is required']);
                return;
            }
            
            // Log the search parameters for debugging
            $this->logger->info("Searching for articles with query: '{$search}', tagId: {$tagId}");
            
            // Determine which table to use for articles
            $articleTable = 'blog_topic'; // Default to blog_topic
            $articleIdField = 'tid';      // Default to tid
            
            // Get a sample article to determine the correct field names
            $sampleArticle = $this->db->select('*')
                ->from($articleTable)
                ->limit(1)
                ->row();
            
            // Determine the title field name based on the sample article
            $titleField = isset($sampleArticle['headline']) ? 'headline' : 'title';
            $dateField = isset($sampleArticle['created']) ? 'created' : 'date_pub';
            
            // Get the total number of articles in the database for reference
            $totalArticles = $this->db->select('COUNT(*) as count')
                ->from($articleTable)
                ->row();
            
            $this->logger->debug("Total articles in database: " . ($totalArticles ? $totalArticles['count'] : 'unknown'));
            
            // Check if the search is a comma-separated list of IDs (must contain at least one comma)
            // Allow trailing commas to avoid "no articles found" while typing
            if (strpos($search, ',') !== false && preg_match('/^(\d+\s*,\s*)*\d+\s*,?\s*$/', $search)) {
                // This is a comma-separated list of IDs
                $articleIds = explode(',', $search);
                $articleIds = array_map('trim', $articleIds); // Trim any whitespace
                $articleIds = array_filter($articleIds, function($id) { return $id !== ''; }); // Remove empty values from trailing commas
                
                $this->logger->debug("Detected comma-separated ID list with " . count($articleIds) . " IDs");
                
                // Get articles that match these IDs
                $allMatchingArticles = $this->db->select(
                        "a.{$articleIdField} as id, " . 
                        "a.{$titleField} as title"
                    )
                    ->from([$articleTable . ' a'])
                    ->where("a.{$articleIdField}", $articleIds)
                    ->rows();
                
                // Track which IDs were found and which weren't
                $foundIds = array_column($allMatchingArticles, 'id');
                $notFoundIds = array_diff($articleIds, $foundIds);
                
                $this->logger->debug("Found " . count($foundIds) . " articles from ID list, " . count($notFoundIds) . " IDs not found");
                
                // If no articles match the IDs, return empty results with a message
                if (empty($allMatchingArticles)) {
                    $this->jsonResponse([
                        'success' => true,
                        'articles' => [],
                        'count' => 0,
                        'message' => 'None of the specified article IDs were found'
                    ]);
                    return;
                }
            } else {
                // Regular search by ID or title
                $searchLike = "%{$search}%";
                $searchStart = "{$search}%";
                
                $allMatchingArticles = $this->db->select(
                        "a.{$articleIdField} as id, " . 
                        "a.{$titleField} as title"
                    )
                    ->from([$articleTable . ' a'])
                    ->where('OR', [
                        ["a.{$articleIdField}", 'like', $searchStart],
                        ["a.{$titleField}", 'like', $searchLike]
                    ])
                    ->limit(10)
                    ->rows();
                
                $this->logger->debug("Found " . count($allMatchingArticles) . " articles matching search without tag filter");
            }
            
            // If no articles match the search criteria, return empty results
            if (empty($allMatchingArticles)) {
                $this->jsonResponse([
                    'success' => true,
                    'articles' => [],
                    'count' => 0,
                    'message' => isset($notFoundIds) ? 'None of the specified article IDs were found' : 'No articles found matching your search'
                ]);
                return;
            }
            
            // Get the IDs of articles that match the search
            $matchingIds = array_column($allMatchingArticles, 'id');
            
            // Get the IDs of articles already linked to this tag
            $linkedIds = $this->db->select('tid')
                ->from('blog_tag')
                ->where('tag', $tagId)
                ->enum();

            $this->logger->debug("Articles already linked to tag {$tagId}: " . count($linkedIds));

            // Filter out articles that are already linked to the tag
            $availableIds = array_diff($matchingIds, $linkedIds);

            $this->logger->debug("Found " . count($availableIds) . " articles not yet linked to tag {$tagId}");
            
            if (empty($availableIds)) {
                $message = 'All matching articles are already linked to this tag';
                
                // If we were searching by ID list, specify which IDs were not found or already linked
                if (isset($notFoundIds) && !empty($notFoundIds)) {
                    $message .= '. The following IDs were not found: ' . implode(', ', $notFoundIds);
                }
                
                $this->jsonResponse([
                    'success' => true,
                    'articles' => [],
                    'count' => 0,
                    'message' => $message
                ]);
                return;
            }
            
            // Get the full details of available articles
            $articles = $this->db->select(
                    "a.{$articleIdField} as id, " . 
                    "a.{$titleField} as title, " .
                    "a.{$dateField} as date_pub, " .
                    "a.acs as acs, " .
                    "CASE " .
                    "  WHEN a.acs = 0 THEN 'published' " .
                    "  WHEN a.acs = 7 THEN 'unpublished' " .
                    "  WHEN a.acs = 8 THEN 'draft' " .
                    "  WHEN a.acs = 9 THEN 'deleted' " .
                    "  ELSE 'published' " .
                    "END as status"
                )
                ->from([$articleTable . ' a'])
                ->where('a.' . $articleIdField, $availableIds)
                ->order("a.{$dateField} DESC")
                ->limit(isset($articleIds) ? count($articleIds) : 10) // Higher limit for ID list searches
                ->rows();
            
            $this->logger->debug("Returning " . count($articles) . " articles for linking");
            
            // Prepare the response message
            $message = null;
            if (isset($notFoundIds) && !empty($notFoundIds)) {
                $message = 'The following IDs were not found: ' . implode(', ', $notFoundIds);
            }
            
            // Return the results
            $this->jsonResponse([
                'success' => true,
                'articles' => $articles,
                'count' => count($articles),
                'message' => $message
            ]);
        } catch (\Exception $e) {
            $this->logger->error("Error in searchArticles: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error searching articles: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Link an article to a tag
     */
    public function linkArticle() {
        try {
            // Get the article ID and tag ID from the request
            $articleId = isset($_POST['articleId']) ? $_POST['articleId'] : null;
            $tagId = isset($_POST['tagId']) ? $_POST['tagId'] : null;
            
            // Add detailed logging
            $this->logger->info("linkArticle called with articleId: {$articleId}, tagId: {$tagId}");
            
            // Validate the inputs
            if (!$articleId) {
                $this->jsonResponse(['success' => false, 'message' => 'Article ID is required']);
                return;
            }
            
            if (!$tagId) {
                $this->jsonResponse(['success' => false, 'message' => 'Tag ID is required']);
                return;
            }
            
            // Check if the article exists
            $articleCount = $this->db->select('COUNT(*) as count')
                ->from('blog_topic')
                ->where('tid', $articleId)
                ->row();
            
            $articleExists = ($articleCount && $articleCount['count'] > 0);
            $this->logger->debug("Article exists check: " . ($articleExists ? 'true' : 'false') . ", count: " . ($articleCount ? $articleCount['count'] : '0'));
            
            if (!$articleExists) {
                $this->jsonResponse(['success' => false, 'message' => 'Article not found']);
                return;
            }
            
            // Check if the tag exists
            $tagExists = $this->db->select('COUNT(*) as count')
                ->from('blog_cat')
                ->where('cat', $tagId)
                ->row();
            
            $tagExists = ($tagExists && $tagExists['count'] > 0);
            $this->logger->debug("Tag exists check: " . ($tagExists ? 'true' : 'false'));
            
            if (!$tagExists) {
                $this->jsonResponse(['success' => false, 'message' => 'Tag not found']);
                return;
            }
            
            // Check if the link already exists
            $linkExists = $this->db->select('COUNT(*) as count')
                ->from('blog_tag')
                ->where('tid', $articleId)
                ->where('tag', $tagId)
                ->row();
            
            $linkExists = ($linkExists && $linkExists['count'] > 0);
            $this->logger->debug("Link exists check: " . ($linkExists ? 'true' : 'false'));
            
            if ($linkExists) {
                // If the link already exists, return success instead of error
                // This makes the UI experience better - the user doesn't need to know
                // that the link already existed
                $this->jsonResponse([
                    'success' => true, 
                    'message' => 'Article is already linked to this tag',
                    'alreadyLinked' => true
                ]);
                return;
            }
            
            // Find the highest sort value for this article's tags
            $maxSort = $this->db->select('MAX(sort) as max_sort')
                ->from('blog_tag')
                ->where('tid', $articleId)
                ->row();
            
            // Default to 0 if no tags exist, otherwise use max_sort + 1
            $sortValue = ($maxSort && isset($maxSort['max_sort'])) ? (int)$maxSort['max_sort'] + 1 : 0;
            
            $this->logger->debug("Using sort value: {$sortValue} for new tag link");

            // Get tag name for logging
            $tagInfo = $this->db->select('name, info')
                ->from('blog_cat')
                ->where('cat', $tagId)
                ->row();
            $tagName = $tagInfo ? ($tagInfo['info'] ?: $tagInfo['name']) : 'Unknown';

            // Create the link with the appropriate sort value
            $result = $this->db->insert('blog_tag', [
                'tid' => $articleId,
                'tag' => $tagId,
                'sort' => $sortValue // Use the calculated sort value instead of 0
            ]);

            if ($result != null) {
                $this->logger->info("ARTICLE LINK SUCCESS: Linked article $articleId to tag $tagId ($tagName) with sort value $sortValue");
                $this->jsonResponse(['success' => true, 'message' => 'Article linked to tag successfully']);
            } else {
                $error = $this->db->err(1);
                $this->logger->error("ARTICLE LINK FAILED: Failed to link article $articleId to tag $tagId ($tagName) - DB error: " . $error);
                $this->jsonResponse(['success' => false, 'message' => 'Failed to link article to tag: ' . $error]);
            }
        } catch (\Exception $e) {
            $this->logger->error("Error in linkArticle: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error linking article to tag: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Link multiple articles to a tag at once
     */
    public function linkMultipleArticles() {
        try {
            // Get the article IDs and tag ID from the request
            $articleIds = isset($_POST['articleIds']) ? $_POST['articleIds'] : null;
            $tagId = isset($_POST['tagId']) ? $_POST['tagId'] : null;
            
            // Add detailed logging
            $this->logger->info("linkMultipleArticles called with tagId: {$tagId}, articleIds: " . json_encode($articleIds));
            
            // Validate the inputs
            if (!$articleIds || !is_array($articleIds) || empty($articleIds)) {
                $this->jsonResponse(['success' => false, 'message' => 'Article IDs are required']);
                return;
            }
            
            if (!$tagId) {
                $this->jsonResponse(['success' => false, 'message' => 'Tag ID is required']);
                return;
            }
            
            // Check if the tag exists
            $tagExists = $this->db->select('COUNT(*) as count')
                ->from('blog_cat')
                ->where('cat', $tagId)
                ->row();
            
            $tagExists = ($tagExists && $tagExists['count'] > 0);
            $this->logger->debug("Tag exists check: " . ($tagExists ? 'true' : 'false'));
            
            if (!$tagExists) {
                $this->jsonResponse(['success' => false, 'message' => 'Tag not found']);
                return;
            }
            
            // Get the IDs of articles that exist
            $existingArticles = $this->db->select('tid')
                ->from('blog_topic')
                ->where('tid', $articleIds)
                ->enum();
            
            $this->logger->debug("Found " . count($existingArticles) . " existing articles out of " . count($articleIds) . " requested");
            
            if (empty($existingArticles)) {
                $this->jsonResponse(['success' => false, 'message' => 'None of the specified articles exist']);
                return;
            }
            
            // Get the IDs of articles already linked to this tag
            $linkedIds = $this->db->select('tid')
                ->from('blog_tag')
                ->where('tag', $tagId)
                ->where('tid', $existingArticles)
                ->enum();
            
            $this->logger->debug("Found " . count($linkedIds) . " articles already linked to tag {$tagId}");

            // Filter out articles that are already linked
            $articlesToLink = array_diff($existingArticles, $linkedIds);

            $this->logger->debug("Will link " . count($articlesToLink) . " articles to tag {$tagId}");
            
            if (empty($articlesToLink)) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => 'All articles are already linked to this tag',
                    'linkedCount' => 0,
                    'alreadyLinkedCount' => count($linkedIds),
                    'notFoundCount' => count($articleIds) - count($existingArticles)
                ]);
                return;
            }
            
            // Insert links one by one without transaction for better error handling
            $successCount = 0;
            $failedArticles = [];
            
            foreach ($articlesToLink as $articleId) {
                // Find the highest sort value for this article's tags
                $maxSort = $this->db->select('MAX(sort) as max_sort')
                    ->from('blog_tag')
                    ->where('tid', $articleId)
                    ->row();
                
                $sortValue = ($maxSort && isset($maxSort['max_sort'])) ? (int)$maxSort['max_sort'] + 1 : 0;
                
                // Insert the link
                $result = $this->db->insert('blog_tag', [
                    'tid' => $articleId,
                    'tag' => $tagId,
                    'sort' => $sortValue
                ]);
                
                if ($result != null) {
                    $successCount++;
                    $this->logger->info("BULK LINK SUCCESS: Linked article {$articleId} to tag {$tagId} with sort {$sortValue}");
                } else {
                    $error = $this->db->err(1);
                    $this->logger->error("BULK LINK FAILED: Failed to link article {$articleId} to tag {$tagId} - DB error: " . $error);
                    $failedArticles[] = $articleId;
                }
            }

            // Get tag name for comprehensive logging
            $tagInfo = $this->db->select('name, info')
                ->from('blog_cat')
                ->where('cat', $tagId)
                ->row();
            $tagName = $tagInfo ? ($tagInfo['info'] ?: $tagInfo['name']) : 'Unknown';

            // Log comprehensive summary
            $this->logger->info("BULK LINK OPERATION COMPLETE: Tag $tagId ($tagName) - {$successCount} successful, " . count($failedArticles) . " failed out of " . count($articlesToLink) . " articles");
            if (!empty($failedArticles)) {
                $this->logger->warning("BULK LINK FAILURES: Failed to link articles " . implode(', ', $failedArticles) . " to tag $tagId ($tagName)");
            }

            // Determine the response based on success/failure
            if ($successCount == count($articlesToLink)) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $successCount . ' articles linked to tag successfully',
                    'linkedCount' => $successCount,
                    'alreadyLinkedCount' => count($linkedIds),
                    'notFoundCount' => count($articleIds) - count($existingArticles)
                ]);
            } else if ($successCount > 0) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => $successCount . ' articles linked to tag successfully, ' . 
                                 count($failedArticles) . ' failed',
                    'linkedCount' => $successCount,
                    'alreadyLinkedCount' => count($linkedIds),
                    'notFoundCount' => count($articleIds) - count($existingArticles),
                    'failedArticles' => $failedArticles
                ]);
            } else {
                $this->jsonResponse([
                    'success' => false,
                    'message' => 'Failed to link any articles to tag',
                    'failedArticles' => $failedArticles
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error("Error in linkMultipleArticles: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            $this->jsonResponse([
                'success' => false,
                'message' => 'Error linking articles to tag: ' . $e->getMessage()
            ]);
        }
    }
}
