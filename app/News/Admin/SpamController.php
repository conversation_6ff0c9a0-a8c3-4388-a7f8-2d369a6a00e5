<?php
namespace App\News\Admin;

use Topnew\Data;

class SpamController extends Base
{
    public function home() {
        $this->set('page.title', 'Admin - Honeypot spam lookup');
        $rows = $this->db->from('log_honeypot');
        $data = Data::clean(['ip', 'page', 'name', 'lname', 'fname', 'email', 'company', 'phone']);
        if ($data['ip']) {
            $rows = $rows->where('ip', 'like', $data['ip']);
        }
        if ($data['page']) {
            $rows = $rows->where('page', 'like', $data['page']);
        }
        $arr = ['name', 'lname', 'fname', 'email', 'company', 'phone'];
        foreach ($arr as $k) {
            if ($data[$k]) {
                $rows = $rows->where('txt', 'like', '[' . $k . '] => ' . $data[$k]);
            }
        }

        $rows = $rows->order('id DESC')->limit(100)->all();
        $this->set('spam', $rows);
    }

    public function del($id = 0) {
        $this->db->where('id', ceil($id))->delete('log_honeypot');
        header('location:/admin/spam');
    }
}
