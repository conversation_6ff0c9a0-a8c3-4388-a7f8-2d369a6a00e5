<?php
namespace App\News\Admin;

use Topnew\Data;

class AcsController extends Base
{
    public function home() {
        $this->set('page.title', 'Staff access dashboard');
        if (!$this->inGrp('SYS Admin') && !$this->inGrp('Director')) {
            $this->set('msg', 'You do not have access to this page.');
            return;
        }

        $users = $this->db->select('uid,fname,lname,acs_staff')
            ->from('blog_user')->where('acs > 6')->order('2,3')->all();
        $this->set('grps', $this->groups);
        $this->set('users', $users);
    }

    public function upd() {
        $this->view = false;
        if (!$this->inGrp('SYS Admin') && !$this->inGrp('Director')) {
            $this->set('msg', 'You do not have access to this page.');
            return;
        }
        $data = ['uid' => 'int', 'gid' => 'int', 'acs' => 'int'];
        $data = Data::clean($data);
        if ($data['uid'] < 1 || $data['gid'] < 1) {
            return;
        }
        $user = $this->db->select('uid,acs,acs_staff')->from('blog_user')->where('uid', $data['uid'])->row();
        if (!$user || $user['acs'] < 6) {
            return;
        }
        $acs = explode(',', $user['acs_staff']);
        if ($data['acs']) {
            $acs[] = $data['gid'];
        } else {
            foreach ($acs as $k => $v) {
                if ($v - $data['gid'] == 0) {
                    unset($acs[$k]);
                }
            }
        }
        sort($acs);
        $acs = trim(implode(',', array_unique($acs)), ' ,');
        $this->db->where('uid', $data['uid'])->update('blog_user', ['acs_staff' => $acs]);
    }
}
