<?php
namespace App\News\Admin;

use App\News\Base As NewsBase;
use Topnew\Auth;

class Base extends NewsBase
{
    protected $groups = [];

    public function __construct() {
        $this->skip_side_foot = 1;
        $this->groups = [
            127 => 'SYS Admin',
            120 => 'Director',
            118 => 'Webinar Delete',
            117 => 'Webinar Regos',
            115 => 'Download Customer', // eg download user.csv
            114 => 'Global PCID limit',
            113 => 'User PCID limit',
            112 => 'Update Customer Security', // eg password, email, (acs ?)
            111 => 'Update Customer',
            110 => 'Lookup Customer',
            108 => 'Session',
            107 => 'Price',
            106 => 'Email',
            105 => 'Download Pay CSV', // download invoice and payment csv
            102 => 'Payment', // audit add payment
            101 => 'Update Invoice', // dates, qty, order, status, add user
            100 => 'Lookup Invoice', // lookup payment as well
        ];

        parent::__construct();
        // Check if this is an AJAX request
        $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
                  
        if (! $this->isStaff() || $this->uid != $this->loginUid()) {
            if ($isAjax) {
                // For AJAX requests, return a JSON response instead of redirecting
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Authentication required']);
                exit;
            } else {
                // For regular requests, redirect to login
                header('location:/login?url=' . $_SERVER['REQUEST_URI']);
                exit;
            }
        }
        $this->set('page.head', '/Admin/head.tpl');
        $this->set('page.foot', '/Admin/foot.tpl');
    }

    protected function inGrp($grp = '') {
        if (!$grp) {
            return; // failed
        }
        $gid = array_search($grp, $this->groups);
        if (!$gid) {
            return; // failed
        }
        $grps = explode(',', $this->get('user.acs_staff'));
        return in_array($gid, $grps);
    }

    /*private function loginUid() {
        // this method copied from Login page
        // this cookie is only set via the login page
        // auto login from email link, no such cookie
        $cms_uid = isset($_COOKIE['CMS_UID']) ? $_COOKIE['CMS_UID'] : 0;
        if (!$cms_uid) {
            return;
        }
        return Auth::sdec($cms_uid);
    }*/

    protected function downloadCsv($data, $file = '') {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . ($file ?: 'download') . '.csv"');
        $this->arr2csv($data);
        $this->view = false;
    }
    private function arr2csv($arr) {
        if (!$arr || !is_array($arr) || !isset($arr[0])) {
            return;
        }
        $keys = array_keys($arr[0]);
        $ttl = count($keys);
        foreach ($keys as $k => $v) {
            $keys[$k] = ucwords(str_replace('_', ' ', $v));
        }
        echo implode(',', $keys); // csv header
        foreach ($arr as $r) {
            echo "\n";
            $j = 0;
            foreach ($r as $v) {
                // Fix the double-quote escaping issue
                if ($v === null || $v === '') {
                    $v = '';
                } else {
                    // First normalize any existing double quotes to single quotes
                    // to prevent double-double quotes
                    $v = str_replace('""', '"', $v);
                    
                    // Then properly escape quotes according to CSV standard
                    $v = str_replace('"', '""', $v);
                    
                    // Wrap in quotes if the value contains commas, quotes, or newlines
                    if (strpos($v, ',') !== false || strpos($v, '"') !== false || 
                        strpos($v, "\n") !== false || strpos($v, "\r") !== false) {
                        $v = '"' . $v . '"';
                    }
                }
                
                echo $v;
                echo (++$j < $ttl) ? ',' : '';
            }
        }
    }
    
    protected function downloadCsvCompany($data, $file = '') {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . ($file ?: 'download') . '.csv"');
        $this->arr2csvCompany($data);
        $this->view = false;
}
    private function arr2csvCompany($arr) {
        if (!$arr || !is_array($arr) || !isset($arr[0])) {
            return;
        }
        $keys = array_keys($arr[0]);
        $ttl = count($keys);
        foreach ($keys as $k => $v) {
            $keys[$k] = ucwords(str_replace('_', ' ', $v));
        }
        echo implode(',', $keys); // csv header
        foreach ($arr as $r) {
            echo "\n";
            
            $Qty_next = $this->db->select('qty as next_qty')->from('inv_invoice')
                    ->where('purchaser', $r['Org_ID'])
                    ->order('inv_id DESC')
                    ->limit(1)
                    ->val();
            $r['Qty_next'] = $Qty_next;
            
            $j = 0;
            foreach ($r as $v) {
                $v = $v ? str_replace(['"', "\n", "\r"], ['""', '\n', '\r'], $v) : '';
                echo strpos($v, ',') !== false ? '"' . $v . '"' : $v;
                echo (++$j < $ttl) ? ',' : '';
            }
        }
    }
}
