<?php
namespace App\News\Cli;

include 'Base.php';

use App\News\Base As NewsBase;
use App\News\TraitInv;

class InvRenew extends NewsBase
{
    /**
     * 1 - A company's last invoice must be paid.
     *     If last invoice cancelled, Unpaid, etc, no need to create a renewal invoice. Just resend.
     * 2 - Last paid invoice expiration date will due within 6 weeks from today,
     *     for any very old invoices, i.e. expired one year ago,
     *     the new expiration will be re-calculated at 1 year from today.
     */
    use TraitInv;
    use TraitCli;

    public function __construct($argv = null) {
        parent::__construct();
        if (isset($argv[2]) && $argv[2] == 'echo') {
            $this->log = 'echo';
        }
    }

    public function run() {
        $this->log();
        $this->renew();
        $this->log('Done');
    }

    private function renew() {
        $sql = "
            SELECT a.purchaser, a.inv_id, a.qty, a.date_to, a.gst, a.item
            FROM inv_invoice AS a
            JOIN (
              SELECT i.purchaser, max(i.date_to) AS exp
              FROM inv_invoice AS i
              JOIN inv_purchaser AS p ON i.purchaser = p.id
              WHERE ifnull(i.date_to, '') > ''
              AND i.qty > 0
              AND p.status != 'cancelled'
              GROUP BY 1
              HAVING exp < '" . date('Y-m-d', strtotime('+42 days')) . "'
            ) AS b on a.purchaser = b.purchaser AND a.date_to = b.exp
            WHERE a.status IN ('Paid', 'Unpaid', 'Paused')
        ";
        //+43 days sometimes caused the InvSend on second day (6wk) so lets change to +42 days
        $inv = $this->db->arr($sql); // use arr to remove duplicated purchasers LOL
        foreach ($inv as $purchaser => $last_inv) {
            $last_inv['purchaser'] = $purchaser;
            $id = $this->saveRenew($last_inv);
            $this->log('Inv # ' . $last_inv['inv_id'] . ' renewed to # ' . $id);
            if ($id) {
                $new_inv = $this->db->from('inv_invoice')->where('inv_id', $id)->row();
                if ($new_inv) {
                    $this->sendPdf($new_inv);
                }
            }
        }
    }

    private function saveRenew($inv) {
        // this method very similar to Admin\InvController and SubscribeController
        $qty = $inv['qty'];
        $prices = $this->getPrices($qty, $inv['item']);
        $inv['amt'] = $this->getPrice($prices, $qty);
        $inv['exp'] = $this->getRenewExp($inv['date_to'], $inv['item']);
        $inv['status'] = 'Unpaid'; // auto mark as Unpaid, ready for cron to send
        $id = $this->saveInv($inv, $prices);
        $this->saveInvLog($id, $inv);
        return $id;
    }
}

(new InvRenew($argv))->run();
