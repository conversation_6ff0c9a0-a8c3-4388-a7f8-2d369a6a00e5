<?php
namespace App\News\Cli;

trait TraitCli
{
    private $log = null;
    private $msg = '';

    public function __construct($argv = null) {
        parent::__construct();
        if (isset($argv[2]) && $argv[2] == 'echo') {
            $this->log = 'echo';
        }
    }

    public function getLog() { return $this->msg; }

    private function log($str = '') {
        $str = $str ?: 'Server: ' . date('Y-m-d H:i:s') . ' -- DB: ' . $this->db->select('now()')->val();
        if ($this->log == 'echo') {
            echo PHP_EOL . $str;
        } else {
            $this->msg .= PHP_EOL . $str;// add to log file -- fix later
        }
    }
}
