<?php
namespace App\News\Cli;

include 'Base.php';

use App\News\Base As NewsBase;
use Topnew\Data;

class ExpFreetrial extends NewsBase
{
    /**
     * A Freetrial user (acs = 2, purchaser = 1)
     * When his last (biggest) trial date is 28 days old
     * Move him to Basic (acs = 1, purchaser = 5)
     * With an email of price options
     */
    use TraitCli;

    public function __construct($argv = null) {
        parent::__construct();
        if (isset($argv[2]) && $argv[2] == 'echo') {
            $this->log = 'echo';
        }
    }

    public function run() {
        $this->log();
        $this->exp();
        $this->log('Done');
    }

    private function exp() {
        $ymd = "greatest(ifnull(trial1,''),ifnull(trial2,''),ifnull(trial3,''))";
        $exp = 'adddate(date(now()), interval -28 day)';
        $cust= $this->db->select('uid,email,status')->from('blog_user')
            ->where('acs', 2)->where('purchaser', 1)
            ->where($ymd . " BETWEEN '1970-01-01' AND " . $exp)
            ->arr();
        if (!$cust) {
            $this->log('No expired freetrial user found');
            return;
        }
        $uids = array_keys($cust);
        $this->log('Expired freetrial moved to basic: ' . implode(', ', $uids));
        $this->db->where('uid', $uids)->update('blog_user', [
            'acs' => 1,
            'purchaser' => 5,
        ]); // move to basic

        foreach ($cust as $uid => $arr) {
            $this->db->insert('inv_log', [
                'uid' => $uid,
                'purchaser' => 1, // 1 or 5 ? none of them make sense anyway
                'status' => 'Sys',
                'txt' => 'Expired freetrial move to basic',
            ]);
            // send price option mail of email-camp #1 hard coded
            if ($arr['status'] == 'active' && Data::validEmail($arr['email'])) {
                $this->db->insert('blog_email', [
                    'camp_id' => 1, // hard coded
                    'uid' => $uid,
                ]);
            }
        }
    }
}

(new ExpFreetrial($argv))->run();
