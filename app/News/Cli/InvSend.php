<?php
namespace App\News\Cli;

include 'Base.php';

use App\News\Base As NewsBase;
use App\News\TraitInv;
use Topnew\Data;

class InvSend extends NewsBase
{
    /**
     * NOTE this cron should run within 24 hours after InvRenew Cron
     *
     * Send all invoices which status = Unpaid, with the date_to in the following:
     * +6wk +4wk +2wk +1wk +1d today -2wk -4wk -6wk ... until -1yr
     */
    use TraitInv;
    use TraitCli;

    public function __construct($argv = null) {
        parent::__construct();
        if (isset($argv[2]) && $argv[2] == 'echo') {
            $this->log = 'echo';
        }
    }

    public function run() {
        $this->log();
        $this->send();
        $this->log('Done');
    }

    private function send() {
        $today = date('Y-m-d');
        $dates = [$today];
        //$dates[] = date('Y-m-d', strtotime('+1 day')); // tomorrow
        $dates[] = date('Y-m-d', strtotime('+1 week'));
        //$dates[] = date('Y-m-d', strtotime('+2 week'));
        $dates[] = date('Y-m-d', strtotime('+4 week'));
        $dates[] = date('Y-m-d', strtotime('+6 week'));

        for ($i = -2; $i >= -52; $i -= 2) {
            $dates[] = date('Y-m-d', strtotime($i . ' week'));
        }
        $sql = "
            SELECT i.*
            FROM inv_invoice AS i
            JOIN inv_purchaser AS p ON i.purchaser = p.id
            WHERE p.status != 'cancelled'
            AND i.status = 'Unpaid'
            AND i.amt > 0
            AND i.qty > 0
            AND date_fm IN ('" . implode("','", $dates) . "')
            AND NOT exists (
              SELECT 1 FROM inv_log AS l
              WHERE l.inv_id = i.inv_id
              AND date(l.created) = '" . $today ."'
              AND l.txt LIKE 'Send Inv PDF%'
            )
            AND i.inv_date < '" . date('Y-m-d H:i:s', strtotime('-2 day')) . "'
        ";
//echo $sql;
//exit;
        $invs = $this->db->all($sql);
        foreach ($invs as $inv) {
            $this->sendPdf($inv);
        }
    }
}

(new InvSend($argv))->run();
