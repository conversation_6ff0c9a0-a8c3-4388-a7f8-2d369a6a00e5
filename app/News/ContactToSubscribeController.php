<?php
namespace App\News;

class ContactToSubscribeController extends Base
{
    public function home() {
        //$this->view = '/' . $this->blog['site']['template'] . '/view/Page/contact-to-subscribe.tpl';
        $this->view = '/' . $this->blog['site']['template'] . '/view/Freetrial/contact-to-subscribe.tpl';
        $title = $desc = '';
        $site = $this->blog['site']['id'];
        if ($site == 6) {
            $title = 'Footprint free trials';
            $desc  = 'Footprint free trials are only available to users in Australia and New Zealand. Paid subscriptions are available to all users.';
        }
        $this->set('page.title', $title);
        $this->set('page.desc',  $desc);
    }
}
