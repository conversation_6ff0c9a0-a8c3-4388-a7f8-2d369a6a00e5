<?php
namespace App\News;

use Topnew\Data;
use Topnew\Auth;

class NewsController extends Base
{
    public function home($tid = 0, $code = '') {
        // http://2020.demo/news/example-story-5576/MjQ4MTkxMjMzAQoK
        $this->view = '/' . $this->blog['site']['template'] . '/view/News/home';
        $arr = explode('-', $tid);
        // Fatal PHP Errors if string given - Is numeric condition added now - 07/08/2024
        $news_id = array_pop($arr);
        $tid = is_numeric($news_id) ? ceil($news_id) : $news_id;
        $topic = !$tid ? '' : $this->db->from('blog_topic')
            ->where('cat', 'like', $this->blog['cat_base'] . '%')
            ->where('tid', $tid)
            ->row();
        if (!$topic) {
            //$this->set('error', 'News not found');
            $this->set('page.title', 'News not found');
            $this->view = '/' . $this->blog['site']['template'] . '/view/News/error';
            return;
        }

        if ($topic['cat'] == $this->blog['cat_event']) {
            $this->autoLogin($topic, $code);
            header('location: /news-event/' . $tid); // event
            exit;
        }

        $this->db->where('tid', $topic['tid'])->update('blog_topic', 'hit = hit + 1');

        $qry_str = $_SERVER['QUERY_STRING'] ? '?' . $_SERVER['QUERY_STRING'] : '';
        $this->set('qry_str', urlencode($qry_str));
        if ($topic['acs'] == 7) {
            $meta = json_decode($topic['meta'], 1);
            if (isset($meta['redirect']) && $meta['redirect'] && $meta['redirect'] != $topic['tid']) {
                header('location:/news/' . $meta['redirect'] . urldecode($qry_str));
                exit;
            }
        }

        if (!$this->isStaff() && (
            $topic['acs'] > 6 ||
            $topic['created'] > date('Y-m-d H:i:s') ||
            ($topic['acs_only'] && $topic['acs_only'] != $this->blog['user_acs'])
        )) {
            //$this->set('error', 'You do not have access to this news');
            $this->set('page.title', 'News not found');
            $this->view = '/' . $this->blog['site']['template'] . '/view/News/error';
            // do we also need log before return ?
            // log_type = 15 tid-ref-spam ?
            // log_ref = error-number ?
            return;
        }

        $can_read = $this->canRead($topic['acs']);
        if (!$can_read && !$this->blog['sess_uid'] && $code) {
            $can_read = $this->autoLogin($topic, $code);
        }
        if ($topic['acs'] && $can_read) {
            $can_read = $this->checkPcId($code); // only check for non-open news i.e. acs > 0
        }
        if (!$can_read) {
            // need to give a list of reason why eg
            //  0 not login yet
            // -1 pending closed accounts
            // -2 free trial expired
            // -3 cancelled subs
            // -4 new unpaid subs
            // -5 pcid max used
            $this->set('err_code', $this->blog['user_acs']);
            $this->view = '/' . $this->blog['site']['template'] . '/view/News/preview';
            // do we also need log before return ?
            // log_type = 15 tid-ref-spam ?
            // log_ref = error-number ?
            if ($this->blog['user_acs'] == -4) {
                $last_inv = $this->db->from('inv_invoice')
                    ->where('purchaser', $this->get('user.purchaser'))
                    ->where('qty > 0')
                    ->where("ifnull(date_to,'') > ''")
                    ->where('status', '!=', 'Paid')
                    ->order('date_to DESC, inv_id DESC')
                    ->limit(1)
                    ->row();
                if ($last_inv) {
                    $last_inv['pay_code'] = $this->payCode($last_inv);
                }
                $this->set('last_inv', $last_inv);
            }/* elseif ($this->blog['user_acs'] != -3) {
                $this->set('latest_news', $this->db->select('tid, headline')
                    ->from('blog_topic')
                    ->where('tid', '!=', $tid)
                    ->where('acs < 7')
                    ->where('acs_only = 0')
                    ->where('created', '<', date('Y-m-d H:i:s'))
                    ->where('cat', '!=', $this->blog['cat_event'])
                    ->order('created DESC')
                    ->limit(5)
                    ->arr()
                );
            }*/
            $topic['meta'] = json_decode($topic['meta'], 1);
        } else {
            $url = $this->getTopicUrl($topic['tid'], $topic['url']);
            $print = Data::clean('print')['print'];
            if ($_SERVER['REDIRECT_URL'] != $url && !$print) {
                header('HTTP/1.1 301 Moved Permanently');
                header('Location: ' . $url . urldecode($qry_str));
                exit;
            }

            $this->blogLog($topic['tid']); // need track email ? cat ? tag ? page ?

            $topic['cats'] = $this->getTopicCatsLink($tid, 1, $topic['tags']);
            $topic['related'] = $this->getTopicRelated($tid);
            $topic['edit'] = $this->canEdit() ? '[Edit](/admin/news/edit/' . $topic['tid'] . ')' : '';
            $meta = json_decode($topic['meta'], 1);
            if (isset($meta['footer']) && $meta['footer']) {
                $topic['txt'] .= "\n\n" . $this->view('/' . $this->blog['site']['template'] . '/view/News/footer.' . $meta['footer'] . '.tpl');
            }
            $topic['txt'] = $this->txtImg($topic['tid'], $topic['txt'], $topic['created']);
            $topic['bookmark'] = $this->isBookmarked($topic['tid']);
            $this->set('lazy', 1);
            $topic['img'] = isset($meta['img']) ? $this->getTopicImgUrl($tid, $meta['img'], $topic['created']) : '';
            $meta['body_img'] = !isset($meta['body_img']) ? ''
                : $this->getTopicImgUrl($tid, $meta['body_img'], $topic['created']);
            $topic['meta'] = $meta;

            if ($print) {
                $this->view = '/' . $this->blog['site']['template'] . '/view/News/print';
                $this->set('page.foot', -1);
                $this->set('page.head', '/' . $this->blog['site']['template'] . '/view/News/print.head.tpl');
            }

            if (isset($_GET['keyw'])) {
                $this->set('keyw', str_replace(["'", '*'], ' ', $_GET['keyw']));
            }
            $this->load5($topic);
        }

        $topic['summary'] = $this->txtImg($topic['tid'], $topic['summary'], $topic['created']);
        $this->set('news', $topic);
        $this->set('page.title', $topic['headline']);
        $this->set('page.desc', trim(strip_tags($topic['summary'])));
    }

    public function lazy($tid = 0) {
        $this->view = false;
        // handle ajax call when user click on "Read the full story"
        $tid = is_numeric($tid) ? ceil($tid) : 0;
        if ($tid > 0) {
            $this->db->where('tid', $tid)->update('blog_topic', 'hit = hit + 1');
            $ref = isset($_GET['ref']) && is_numeric($_GET['ref']) ? ceil($_GET['ref']) : 0;
            $this->blogLog($tid, 11, $ref); // 11 = tid to tid
        }
    }

    private function load5($topic) {
        if (!$topic) {
            return;
        }
        $sql_tag = 1;
        if ($topic['meta'] && is_array($topic['meta']) && isset($topic['meta']['Lazy loading'])
            && $topic['meta']['Lazy loading'] == 'first-tag'
        ) {
            $sql_tag = 0;
        }
        if (!$sql_tag && isset($topic['tags']) && is_array($topic['tags'])) {
            foreach ($topic['tags'] as $k => $v) {
                if (!$sql_tag && $k != $this->blog['cat_base']) {
                    $sql_tag = 'n.tid IN (SELECT tid FROM blog_tag WHERE tag = ' . $k . ')';
                }
            }
        }
        $sql_tag = $sql_tag ?: 1;
        $rows = $this->baseSQL()
            ->where('n.tid != ' . $topic['tid'])
            ->where('n.acs < 6')
            ->where($sql_tag)
            ->order('n.created desc, n.tid desc')
            ->limit(5)
            ->rows();

        foreach ($rows as $k => $v) {
            $v['bookmark'] = $this->isBookmarked($v['tid']);
            $v['txt'] = $this->txtImg($v['tid'], $v['txt'], $v['created']);
            $v['cats'] = $this->getTopicCatsLink($v['tid'], 1, $v['tags']);
            $v['related'] = $this->getTopicRelated($v['tid']);
            $v['edit'] = $this->canEdit() ? '<a href="/admin/news/edit/' . $v['tid'] . '">Edit</a>' : '';
            $rows[$k] = $v;
        }
        $this->set('load5', $rows);
    }

    public function load($lazy = 1) {
        // no longer in use
        // news.js -- load-news also no longer in use
        // News/load.tpl -- no longer in use

        // when steve lazy loading frontend ready, turn on next line
        //exit;

        $this->view = false;
        if ($this->blog['user_acs'] < 0) {
            return;
        }
        // do we need also check PCID here ? -- todo later

        $lazy = ceil($lazy);
        if ($lazy > 5) {
            return; // max 5 lazy loading
        }
        $this->set('lazy', ++$lazy);
        $tid = $this->db
            ->select('tid')
            ->from('blog_log')
            ->where('uid', $this->blog['sess_uid'])
            ->where('log_type', 'like', '1%')
            ->order('lid desc')
            ->limit(1)
            ->val();
        if (!$tid) {
            return;
        }

        //$last_ts = $this->db->select('created')->from('blog_topic')->where('tid', $tid)->val();
        $last_topic = $this->baseSQL()
            ->leftJoin(['t' => 'blog_tag'], 'n.tid = t.tid')
            ->where('n.tid', $tid)
            ->where('t.tag', '!=', $this->blog['cat_base'])
            ->order('t.sort')
            ->limit(1)
            ->select('n.created,n.meta,t.tag')
            ->row();
        $last_ts = $last_topic ? $last_topic['created'] : '';
        $primary_tag = 1;
        if ($last_topic && $last_topic['meta'] && $last_topic['tag']) {
            $meta = json_decode($last_topic['meta'], 1);
            if ($meta && is_array($meta) && isset($meta['Lazy loading'])
                && $meta['Lazy loading'] == 'first-tag'
            ) {
                $primary_tag = 'n.tid IN (SELECT tid FROM blog_tag WHERE tag = ' . $last_topic['tag'] . ')';
            }
        }

        $topic = $this->baseSQL()
            ->where('n.tid != ' . $tid)
            ->where('n.created', '<=', $last_ts ?: date('Y-m-d H:i:s'))
            ->where('n.acs', '<=', $this->blog['user_acs'])
            ->where('n.acs < 6')
            ->where($primary_tag)
            ->order('n.created desc, n.tid desc')
            ->limit(1)
            ->row();
        if (!$topic) {
            return;
        }

        $this->blogLog($topic['tid'], 11, $tid); // 11 = tid to tid
        //$this->db->where('tid', $topic['tid'])->update('blog_topic', 'hit = hit + 1');

        //$topic['url'] = $this->getTopicUrl($topic['tid'], $topic['url']);
        $topic['bookmark'] = $this->isBookmarked($topic['tid']);
        $topic['txt'] = $this->txtImg($topic['tid'], $topic['txt'], $topic['created']);
        $topic['cats'] = $this->getTopicCatsLink($topic['tid'], 1, $topic['tags']);
        $topic['related'] = $this->getTopicRelated($topic['tid']);
        $topic['edit'] = $this->canEdit() ? '[Edit](/admin/news/edit/' . $topic['tid'] . ')' : '';
        $this->set('news', $topic);
        echo $this->view('/' . $this->blog['site']['template'] . '/view/News/load.tpl');
    }

    private function isBookmarked($tid) {
        return $this->uid ? $this->db->from('blog_log')
            ->where('uid', $this->uid)
            ->where('tid', $tid)
            ->where('log_type', 7)
            ->count() : 0;
    }

    protected function txtImg($tid, $txt = '', $year = '') {
        // We need to replace img from <img src="url.png" to "/files/year/url.png
        $txt = $this->txtToHtml($txt, 1);
        $cut = '<img src=';
        $arr = explode($cut, $txt);
        foreach ($arr as $i => $str) {
            if ($str && $i && in_array($str[0], ['"', "'"])) {
                $arr2 = explode($str[0], $str, 3); // "url.png"
                if (isset($arr2[1]) && $arr2[1]) {
                    $img = $this->getTopicImgUrl($tid, $arr2[1], $year);
                    if (!$img && strpos($arr2[1], '-')) { // to be del after moved to /files/year/xxx
                        $arr3 = explode('-', $arr2[1], 2); // mid-img.png
                        if (isset($arr3[1]) && is_numeric($arr3[0])) {
                            $img = $this->getTopicImgUrl($arr3[0], $arr3[1], $year);
                        }
                    }
                    if ($img && $img != $arr2[1]) {
                        $arr2[1] = $img;
                        $arr[$i] = implode($str[0], $arr2);
                    }
                }
            }
        }
        return implode($cut, $arr);
    }

    private function autoLogin($topic, $code = '') {
        if ($this->blog['site']['id'] == 4 && is_numeric($code)) {
            // redirect from nav.php?id=tid&no=code
            $uid = ceil($code / $topic['tid']);
            if ($uid && date('Y-m-d') > '2024-12-08') {
                return;// old site method expired -- pls ttl remove this block afterwards
            }
        } else {
            $uid = Auth::sdec($code, $this->blog['site']['code'] . $topic['tid'], $sec);
            if ($sec > 3600 * 24 * 30) {
                return; // max 30 days expire
            }
        }

        if (!is_numeric($uid)) {
            return;
        }
        $user = $this->db->from('blog_user')->where('uid', $uid)->row();
        if (!$user) {
            return; // also access level check, also pcid check
        }
        // now login user -- and need make a cookie
        $this->blog['sess_uid'] = $user['uid'];
        $this->blog['user_acs'] = $this->checkAcs($user); // acs only set in session -- do not update database
        $this->db->where('uid', $user['uid'])
            ->update('blog_user', ['last_login' => date('Y-m-d H:i:s')]);
        Auth::cookie($user['uid'], 1); //setcookie for 1 days
        return $this->canRead($topic['acs']);
    }

    private function canRead($story_acs) {
        /**
         * story / cat acs:
         * 0 - free to all
         * 1 - basic or above
         * 2 - freetrial users or above
         * 3 - premium users or above
         * 7 - staff only
         *
         * user acs:
         * 0 - no access, eg guest, expired free trial or expired paid users
         * 1 - basic users
         * 2 - freetrial users
         * 3 - premium users
         * 7 - support
         * 8 - editor
         * 9 - admin
         */
        return $this->isStaff() || !$story_acs || $this->blog['user_acs'] >= $story_acs;
    }

    protected function checkPcId($code = '') {
        /**
         * return true if same pcid found in user profile
         * else set pcid for any un-used
         */
        if (!$this->blog['sess_uid'] || $this->blog['user_acs'] < 1) {
            $this->blog['user_acs'] = 0;
            return;
        }
        $cid = $this->getPcId();

        $user = $this->db->select('uid, pcid_limit, pcid')
            ->from('blog_user')->where('uid', $this->uid)->row();
        $pcid_limit = $this->param('PCID_MAX') ?: 12;
        $pcid_limit = isset($user['pcid_limit']) && $user['pcid_limit'] ? $user['pcid_limit'] : $pcid_limit;
        $pcid = isset($user['pcid']) && $user['pcid'] ? json_decode($user['pcid'], 1) : [];

        // Comparison function to sort by 'updated'
        usort($pcid, function ($a, $b) {
            // Convert dates to timestamps for comparison
            return (int) (strtotime($b['updated']) - strtotime($a['updated']));
        });

        $used = 0;
        $today = date('Y-m-d 00:00:00');
        $key = $key_logout = -1;
        foreach ($pcid as $k => $v) {
            if ($v['pcid'] == $cid) {
                $key = $k;
            }
            if ($v['updated'] >= $today && !$v['logout']) {
                $used++;
                $key_logout = $k;
            }
        }
        if ($used > $pcid_limit || ($used == $pcid_limit && (
            $key == -1 || ($key > -1 && ($pcid[$key]['logout'] == 1 || $pcid[$key]['updated'] < $today))
        ))) {
            if ($this->loginUid() == $this->uid) { // login via password
                // logout the oldest pcid, also marked as logout
                $this->db->where('uid', $this->uid)
                    ->where('pcid', $pcid[$key_logout]['pcid'])->delete('log_login');
                $pcid[$key_logout]['logout'] = 1;
            } else { // login via email link
                $this->blog['user_acs'] = -5;
                return; // no access for email auto code
            }
        }
        $now = date('Y-m-d H:i:s');
        if ($key > -1) {
            $pcid[$key]['updated'] = $now;
            $pcid[$key]['logout']  = 0;
            $pcid[$key]['count']++;
        } else {
            $pcid[] = ['pcid' => $cid, 'ip' => Data::ip(), 'created' => $now, 'updated' => $now, 'count' => 1, 'logout' => 0];
        }

        // Comparison function to sort by 'updated'
        usort($pcid, function ($a, $b) {
            // Convert dates to timestamps for comparison
            return (int) (strtotime($b['updated']) - strtotime($a['updated']));
        });
        
        // Due to pcid JSON data exceeds the 65,535 byte limit, we have changed the user latest pcid from 999 to 400
        $pcid = array_slice($pcid, 0, 400); // keep latest 400 pcid
        $pcid = json_encode($pcid);
        $this->db->where('uid', $this->uid)->update('blog_user', ['pcid' => $pcid]);
        return 1;
    }
}
