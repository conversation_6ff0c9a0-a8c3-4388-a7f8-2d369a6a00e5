<?php
namespace App\News;

class HtmlHelper
{
    public static function addrCountry($country = '') {
        $arr = ['Australia', 'New Zealand', 'USA', 'UK'];
        $arr = ['' => 'Country'] + array_combine($arr, $arr);
        return self::getHtml($arr, $country);
    }

    public static function addrState($state = '') {
        $arr = ['ACT', 'NSW', 'NT', 'QLD', 'SA', 'TAS', 'VIC', 'WA'];
        $arr = ['' => 'State'] + array_combine($arr, $arr);
        return self::getHtml($arr, $state);
    }

    public static function companyStatus($status = '', $defa = '') {
        $arr = ['new' => 'Pending', 'active' => 'Active', 'cancelled' => 'Cancelled'];
        return self::getHtml($arr, $status, $defa);
    }

    public static function emailHtml($email_html = 0) {
        $arr = [1 => 'HTML', 'Text'];
        return self::getHtml($arr, $email_html);
    }

    public static function emailRepeat($email_repeat = '', $arr = []) {
        $arr = $arr ?: ['No email', 'Instant', 'Daily', 'Weekly'];
        $arr = array_combine($arr, $arr);
        return self::getHtml($arr, $email_repeat);
    }
    
    public static function emailRepeatUser($email_repeat = '') {
        $arrKey = ['No email', 'Instant', 'Daily', 'Weekly'];
        $arrValue = ['No email - do not use', 'Instant', 'Daily', 'Weekly'];
        $arrResult = array_combine($arrKey, $arrValue);
        return self::getHtml($arrResult, $email_repeat);
    }

    public static function invPay($var = 0, $defa = '') {
        $arr = [11 => 'EFT', 12 => 'CHQ', 2 => 'All credit card', 24 => 'Visa', 25 => 'Mastercard', 23 => 'Amex'];
        return self::getHtml($arr, $var, $defa);
    }

    public static function invStatus($var = '', $defa = '') {
        $arr = ['Draft', 'Unpaid', 'Paid', 'Cancelled', 'Refunded', 'Paused'];
        $arr = array_combine($arr, $arr);
        return self::getHtml($arr, $var, $defa);
    }

    public static function tempFile($file = '', $dir = '', $blog = '') {
        $html = '<optgroup label="System Templates">';
        $arr = ['', 'default', 'daily', 'weekly', 'instant'];
        $html .= self::getHtml(array_combine($arr, $arr), $file);
        $html .= '</optgroup>';
        if ($dir) {
            if (!file_exists($dir .'/'. $blog .'/view/Email/Custom')) {
                mkdir($dir .'/'. $blog .'/view/Email/Custom', 0777, true);
            }
            $arr2 = scandir($dir .'/'. $blog .'/view/Email/Custom');
            $arr3 = [];
            foreach ($arr2 as $k) {
                if (substr($k, -4) == '.tpl') {
                    $k = 'Custom/' . substr($k, 0, -4);
                    $arr3[$k] = $k;
                }
            }
        }
        if (!in_array($file, $arr) && !in_array($file, $arr3)) {
            $arr3[$file] = $file;
        }
        $html .= '<optgroup label="Custom Templates">';
        $html .= self::getHtml($arr3, $file);
        $html .= '</optgroup>';
        return $html;
        /*$arr = ['', 'default', 'daily', 'weekly', 'instant', $file];
        $arr = array_combine($arr, $arr);
        return self::getHtml($arr, $file);*/
    }

    public static function userAcs($acs = 0) {
        $arr = ['' => 'Access', 1 => 'Basic', 'Freetrial', 'Premium', 6 => 'Staff', 'Support', 'Editor', 'Admin'];
        return self::getHtml($arr, $acs);
    }

    public static function userGroup($grp = '') {
        $arr = ['ALL CUSTOMER', 'ALL CUSTOMER SET', 'ALL STAFF', 'ALL BASIC', 'ALL FREETRIAL', 'ALL PREMIUM', 'INACTIVE FREETRIAL'];
        $arr = array_combine($arr, $arr);
        return self::getHtml($arr, $grp);
    }

    public static function userStatus($status = '') {
        // might need to check globally to set status as ucfirst ?
        $arr = ['' => 'Status', 'active' => 'Active', 'pending' => 'Pending', 'closed' => 'Closed'];
        return self::getHtml($arr, $status);
    }

    public static function userTag($tag = '', $name = '', $css = '') {
        $arr = [
            'LEAD', 'ADDU', 'SL.TA', 'SL.AG', 'SL.MS', 'SL.VND', 'DNC',
        ];
        if (!$name && $css === 'SELECT') {
            $arr = array_combine($arr, $arr);
            return self::getHtml($arr, $tag);
        }

        if (!is_array($tag)) {
            $tag = $tag ? explode(';', trim($tag, '; ')) : array();
        }
        $html = '';
        $name = $name ?: 'user_tag';
        foreach ($arr as $v) {
            $html .= '<label' . ($css ? ' class="' . $css . '"' : '')
                . '><input type="checkbox" name="' . $name . '[]"'
                . (in_array($v, $tag) ? ' checked' : '')
                .' value="' . $v . '"> ' . $v . '</label> ';
        }
        return $html;
    }

    public static function getHtml($arr, $var, $defa = '') {
        $html = '';
        if ($defa) {
            if (is_array($defa)) {
                foreach ($defa as $k => $v) {
                    $html .= '<option value="' . $k . '"'
                        . ($var == $k ? ' selected' : '')
                        . '>' . $v . '</option>';
                }
            } else {
                $html .= '<option value="">' . $defa . '</option>';
            }
        }
        foreach ($arr as $k => $v) {
            $html .= '<option value="' . $k . '"'
                . ($var == $k ? ' selected' : '')
                . '>' . $v . '</option>';
        }
        return $html;
    }
}
