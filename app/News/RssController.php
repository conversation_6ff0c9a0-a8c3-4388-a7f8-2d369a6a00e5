<?php
namespace App\News;

class RssController extends Base
{
    public function home($xml = '') {
        $this->view = '/' . $this->blog['site']['template'] . '/view/Rss/home.tpl';
        if ($xml == 'rss_events.xml') {
        } elseif (substr($xml, 0, 16) == 'sitemap_news.xml') {
            return $this->sitemap($xml);
        } elseif ($xml && substr($xml, -4) == '.xml') {
            $xml = explode('_', $xml, 2);
            $xml = $xml[0];
            if ($xml[0] == 'X') {
                $this->xml(substr($xml, 1));
            }
        }

        $site = $this->blog['site']['name'];
        $this->set('page.title', $site . ' RSS feeds');
        $this->set('page.desc', 'Use RSS to be alerted to the latest ' . $site . ' articles.');
    }

    private function xml($stream) {
        if ($stream != 1) {
            $cat = $this->db->from('blog_cat')->where('stream', $stream)->row();
            if (!$cat) {
                return;
            }
            $sql = $this->baseSQL()
                ->join(['t' => 'blog_tag'], 't.tid = n.tid')
                ->where('t.tag', 'like', $cat['cat'] . '%');
        } else {
            $sql = $this->baseSQL();
            $cat = ['name' => 'AllArticles'];
        }
        $this->set('xml', 'X' . $stream . '_' . str_replace(' ', '', ucwords(str_replace('-', ' ', $cat['name']))));
        $topics = $sql->select('n.tid, n.created, n.headline, n.summary')
            ->order('n.created DESC')
            ->limit(30)
            ->arr();

        $this->view = '/' . $this->blog['site']['template'] . '/view/Rss/rssFeed.tpl';
        $this->set('topics', $topics);
        $this->set('page.head', -1);
        $this->set('page.foot', -1);
        $this->config->set('view_markdown', false);
        header('Content-type: text/xml');
    }

    private function sitemap($xml) {
        $gz = substr($xml, -3) == '.gz' ? '.gz' : '';
        $xml = $gz ? substr($xml, 0, -3) : $xml;
        $ymd = file_exists(WEB . $xml) ? date('Y-m-d', filemtime(WEB . $xml)) : '';
        if ($ymd >= date('Y-m-d')) {
            header('location:/' . $xml . $gz); // make sure .xml and .xml.gz always exists
            exit;
        }

        $rows = $this->baseSQL()
            ->select('n.tid, date(n.created) ymd, url')
            ->order('n.created DESC')
            ->arr();

        $days_30  = date('Y-m-d', strtotime('-30 days'));
        $days_90  = date('Y-m-d', strtotime('-90 days'));
        $days_365 = date('Y-m-d', strtotime('-365 days'));

        $fp = fopen(WEB . $xml, 'w');
        fwrite($fp, '<?xml version="1.0" encoding="UTF-8" ?>'
            . "\n" . '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">');

        foreach ($rows as $tid => $r) {
            if ($r['ymd'] < $days_365) {
                $freq = 'never';
            } elseif ($r['ymd'] < $days_90) {
                $freq = 'monthly';
            } elseif ($r['ymd'] < $days_30) {
                $freq = 'weekly';
            } else {
                $freq = 'daily';
            }

            fwrite($fp, "\n" . '<url>'
                . "\n" . '  <loc>' . $this->blog['site']['url']
                . substr($this->getTopicUrl($tid, $r['url']), 1)
                . '</loc>');
            fwrite($fp, "\n" . '  <lastmod>' . $r['ymd'] . '</lastmod>'
                . "\n" . '  <changefreq>' . $freq . '</changefreq>'
                . "\n" . '  <priority>0.5</priority>'
                . "\n" . '</url>');
        }
        fwrite($fp, "\n" . '</urlset>');
        fclose($fp);

        // now write to .gz
        if ($fp_out = gzopen(WEB . $xml . '.gz', 'w9')) {
            if ($fp_in = fopen($xml, 'rb')) {
                while (!feof($fp_in)) {
                    gzwrite($fp_out, fread($fp_in, 1024*512));
                }
                fclose($fp_in);
           }
           gzclose($fp_out);
        }

        header('location:/' . $xml . $gz);
        exit;
    }

    private function xmlOut($fp, $txt) {
        if ($fp) {
            fwrite($fp, $txt);
        } else {
            echo $txt;
        }
    }
}
