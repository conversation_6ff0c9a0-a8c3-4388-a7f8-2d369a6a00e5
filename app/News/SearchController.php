<?php
namespace App\News;

use Topnew\Data;

class SearchController extends Base
{
    public function home($x = '', $page = 0) {
        $this->search($page);
        $this->view = '/' . $this->blog['site']['template'] . '/view/search.tpl';
        $this->set('page.title', 'Footprint - find the environmental and carbon information you need');
        $this->set('page.desc', 'Search Footprint for the environmental and carbon information you need');
        $this->set('page.side', 0);

        $this->viewTags();
        // Copy side_tags to comm_tags for the search template
        $this->set('comm_tags', $this->get('side_tags'));
    }

    private function search($page) {
        $data = [
            'keyw', 'match', 'tag',
            'fm_y' => 'int', 'fm_m' => 'int', 'fm_d' => 'int',
            'to_y' => 'int', 'to_m' => 'int', 'to_d' => 'int',
            'pgno' => 'int', 'page' => 'int',
        ];
        $data = Data::clean($data);
        
        // Set default "to" date to today if not provided
        $data['to_y'] = $data['to_y'] ?: date('Y');
        $data['to_m'] = $data['to_m'] ?: date('m');
        $data['to_d'] = $data['to_d'] ?: date('d');
        
        // Set default "from" date to a distant past date if not provided
        if (!$data['fm_y'] && !$data['fm_m'] && !$data['fm_d']) {
            $data['fm_y'] = '2000';
            $data['fm_m'] = '01';
            $data['fm_d'] = '01';
        }
        
        $data['pgno'] = $page ?: $data['pgno'];

        $this->set('form', $data);
        $data['match'] = $data['match'] ?: 'all';

        $topics = $this->baseSQL();
        $keyw = $data['keyw'];
        if ($keyw) {
            $keyw = trim($keyw, ' "\'');
            $keyw = $data['match'] == 'all'
                ? str_replace(' ', ' AND ', $keyw)
                : ($data['match'] == 'any' ? $keyw : '"' . $keyw . '"');
            $keyw = Data::cleanKeyw($keyw);
            $topics = $this->getKeywSQL($topics, $keyw);
        }
        if ($data['tag']) {
            // Get the category ID for the selected tag using a direct SQL query
            $tagCat = null;
            $tagQuery = "SELECT cat FROM blog_cat WHERE name = :tag_name";
            $stmt = $this->db->run($tagQuery, ['tag_name' => $data['tag']]);
            if ($stmt) {
                $tagCat = $stmt->fetchColumn();
            }
            
            if ($tagCat) {
                // Check if it's a parent tag (21xx format)
                if (substr($tagCat, 0, 2) == '21') {
                    // For parent tags, use LIKE to include all children
                    $topics = $topics->where('EXISTS', 'SELECT 1 FROM blog_tag WHERE tid = n.tid AND tag IN (
                        SELECT cat FROM blog_cat WHERE cat LIKE "' . $tagCat . '%" 
                    )');
                } else {
                    // For regular tags, use exact match
                    $key = $topics->bindKey('tag', $data['tag']);
                    $topics = $topics->where('EXISTS', 'SELECT 1 FROM blog_tag WHERE tid = n.tid AND tag IN (
                        SELECT cat FROM blog_cat WHERE name = ' . $key . '
                    )');
                }
            } else {
                // Fallback to original behavior if tag not found
                $key = $topics->bindKey('tag', $data['tag']);
                $topics = $topics->where('EXISTS', 'SELECT 1 FROM blog_tag WHERE tid = n.tid AND tag IN (
                    SELECT cat FROM blog_cat WHERE name = ' . $key . '
                )');
            }
        }

        $fm = date('Y-m-d', strtotime($data['fm_y'] . '-' . $data['fm_m'] . '-' . $data['fm_d']));
        $to = date('Y-m-d', strtotime($data['to_y'] . '-' . $data['to_m'] . '-' . $data['to_d']));
        if ($to && $to < $fm) {
            Data::swap($fm, $to);
        }
        $topics = $topics->where('date(n.created)', 'between', $fm, $to);
        $topics = $topics->order('n.created DESC')->pgno($data['pgno'], $ttl, $max);
        $cats = $this->getTopicCatsLink(array_column($topics, 'tid'), 1, $tags);

        $bookmark = $this->uid ? $this->db->select('tid')->from('blog_log')
            ->where('uid', $this->uid)->where('log_type', 7)->enum() : [];

        foreach ($topics as $k => $topic) { // not sure what steve need, pls update a/c feedback
            $tid = $topic['tid'];
            $topic['url'] = $this->getTopicUrl($tid, $topic['url']);
            $summary = explode('<!--MORE-->', $topic['summary']);
            //$summary = rtrim($summary[0], ",. \t\n\r\0\x0B");
            $summary = trim($summary[0]);
            $topic['summary'] = nl2br(trim($summary), 1);
            $meta = json_decode($topic['meta'], 1);
            $topic['img'] = isset($meta['img']) ? $this->getTopicImgUrl($tid, $meta['img'], $topic['created']) : '';
            $topic['cats'] = isset($cats[$tid]) ? $cats[$tid] : '';
            $topic['tags'] = isset($tags[$tid]) ? $tags[$tid] : '';

            $topic['icon'] = $this->blog['acs_icon_' . $topic['acs']];
            $topic['stat'] = $this->isStaff() ? '<i class="fa fa-signal"></i>'
                . number_format($topic['hit']) . ' &nbsp;' : '';
            $topic['bookmark'] = in_array($tid, $bookmark);

            $topics[$k] = $topic;
        }

        $this->set('topics', $topics);
        $this->set('pgno', $data['pgno'] ?: 1);
        $this->set('pgno_max', $max); // ttl page of data
        $this->set('pgno_ttl', $ttl); // ttl num of data

        //for sl not use form so manually make the param here
        $param = '';
        foreach ($data as $k => $v) {
            if ($k != 'pgno' && $v) {
                $param .= '&' . $k . '=' . $v;
            }
        }
        $this->set('pgno_param', substr($param, 1));
    }

    // Make viewTags method accessible in SearchController
    protected function viewTags() {
        $new = $this->tagStatNew();
        $rows = $this->db->select('cat, name, info')
            ->from('blog_cat')
            ->where('cat', 'like', '21__%')
            ->order('length(cat), if (info="", name, info)')
            ->rows();
        $tags = [];
        foreach ($rows as $tag) {
            $cat = $tag['cat'];
            $tag['new'] = isset($new[$cat]) ? $new[$cat] : 0;
            $tag['info'] = $this->getCatName($tag['name'], $tag['info']);
            $cat4 = substr($cat, 0, 4);
            $cat6 = substr($cat, 0, 6);
            $cat8 = substr($cat, 0, 8);
            $cat10 = substr($cat, 0, 10);
            $len = strlen($cat);
            
            if ($len == 4) {
                $tags[$cat] = $tag;
            } elseif ($len == 6) {
                if (!isset($tags[$cat4])) {
                    // Create parent if it doesn't exist
                    $parent = $this->db->from('blog_cat')->where('cat', $cat4)->row();
                    if ($parent) {
                        $parent['new'] = isset($new[$cat4]) ? $new[$cat4] : 0;
                        $parent['info'] = $this->getCatName($parent['name'], $parent['info'] ?? '');
                        $tags[$cat4] = $parent;
                    }
                }
                $tags[$cat4]['sub'][$cat] = $tag;
            } elseif ($len == 8) {
                if (!isset($tags[$cat4])) {
                    $parent = $this->db->from('blog_cat')->where('cat', $cat4)->row();
                    if ($parent) {
                        $parent['new'] = isset($new[$cat4]) ? $new[$cat4] : 0;
                        $parent['info'] = $this->getCatName($parent['name'], $parent['info'] ?? '');
                        $tags[$cat4] = $parent;
                    }
                }
                if (!isset($tags[$cat4]['sub'][$cat6])) {
                    $parent = $this->db->from('blog_cat')->where('cat', $cat6)->row();
                    if ($parent) {
                        $parent['new'] = isset($new[$cat6]) ? $new[$cat6] : 0;
                        $parent['info'] = $this->getCatName($parent['name'], $parent['info'] ?? '');
                        $tags[$cat4]['sub'][$cat6] = $parent;
                    }
                }
                $tags[$cat4]['sub'][$cat6]['sub2'][$cat] = $tag;
            } elseif ($len == 10) {
                if (!isset($tags[$cat4])) {
                    $parent = $this->db->from('blog_cat')->where('cat', $cat4)->row();
                    if ($parent) {
                        $parent['new'] = isset($new[$cat4]) ? $new[$cat4] : 0;
                        $parent['info'] = $this->getCatName($parent['name'], $parent['info'] ?? '');
                        $tags[$cat4] = $parent;
                    }
                }
                if (!isset($tags[$cat4]['sub'][$cat6])) {
                    $parent = $this->db->from('blog_cat')->where('cat', $cat6)->row();
                    if ($parent) {
                        $parent['new'] = isset($new[$cat6]) ? $new[$cat6] : 0;
                        $parent['info'] = $this->getCatName($parent['name'], $parent['info'] ?? '');
                        $tags[$cat4]['sub'][$cat6] = $parent;
                    }
                }
                if (!isset($tags[$cat4]['sub'][$cat6]['sub2'][$cat8])) {
                    $parent = $this->db->from('blog_cat')->where('cat', $cat8)->row();
                    if ($parent) {
                        $parent['new'] = isset($new[$cat8]) ? $new[$cat8] : 0;
                        $parent['info'] = $this->getCatName($parent['name'], $parent['info'] ?? '');
                        $tags[$cat4]['sub'][$cat6]['sub2'][$cat8] = $parent;
                    }
                }
                $tags[$cat4]['sub'][$cat6]['sub2'][$cat8]['sub3'][$cat] = $tag;
            }
        }
        
        // Sort all levels of the hierarchy by info/name
        $this->sortTagsHierarchy($tags);
        
        $this->set('side_tags', $tags);
    }

    /**
     * Sort the tags hierarchy alphabetically by info/name
     * 
     * @param array &$tags The tags array to sort
     */
    private function sortTagsHierarchy(&$tags) {
        // Sort the top level
        uasort($tags, function($a, $b) {
            $aInfo = isset($a['info']) ? $a['info'] : '';
            $bInfo = isset($b['info']) ? $b['info'] : '';
            return strcasecmp($aInfo, $bInfo);
        });

        // Sort each sub-level
        foreach ($tags as &$tag) {
            if (isset($tag['sub'])) {
                uasort($tag['sub'], function($a, $b) {
                    $aInfo = isset($a['info']) ? $a['info'] : '';
                    $bInfo = isset($b['info']) ? $b['info'] : '';
                    return strcasecmp($aInfo, $bInfo);
                });

                // Sort sub2 level
                foreach ($tag['sub'] as &$subTag) {
                    if (isset($subTag['sub2'])) {
                        uasort($subTag['sub2'], function($a, $b) {
                            $aInfo = isset($a['info']) ? $a['info'] : '';
                            $bInfo = isset($b['info']) ? $b['info'] : '';
                            return strcasecmp($aInfo, $bInfo);
                        });

                        // Sort sub3 level
                        foreach ($subTag['sub2'] as &$sub2Tag) {
                            if (isset($sub2Tag['sub3'])) {
                                uasort($sub2Tag['sub3'], function($a, $b) {
                                    $aInfo = isset($a['info']) ? $a['info'] : '';
                                    $bInfo = isset($b['info']) ? $b['info'] : '';
                                    return strcasecmp($aInfo, $bInfo);
                                });
                            }
                        }
                    }
                }
            }
        }
    }
}
