<?php
namespace App\News;

class JobBoardController extends Base
{
    public function home() {
        $json = $this->downloadXml();
        $this->set('xml', $json);
        $this->view = '/' . $this->blog['site']['template'] . '/view/Pages/jobs';
    }

    public function downloadXml($download = 0) {
        $this->view = false;
        // download once a day, and save to View/Pages/jobs.json
        $file = $this->config->get('view_root') . '/' . $this->blog['site']['template'] . '/view/Pages/jobs.json';
        if (!$download && file_exists($file)) {
            $json = json_decode(file_get_contents($file), true);
            if (is_array($json) && isset($json[0]) && isset($json[0]['dt_download'])) {
                $ts_download = substr($json[0]['dt_download'], 0, 10);
                if ($ts_download >= date('Y-m-d')) {
                    return $json; //only download once a day
                }
            }
        }

        //$jobs_feed = 'https://workplaceexpress.mysmartjobboard.com/feeds/standard.xml';
        $jobs_feed = 'https://jobs.workplaceexpress.com.au/feeds/standard.xml';
        //$jobs_feed = '/home/<USER>/public_html/www/files/feeds/standard.xml';
        $xml = simplexml_load_file($jobs_feed);
        if (!$xml) {
            return;
        }
        $xml = json_decode(json_encode($xml), true);

        $data = [];
        $i = 0;
        foreach ($xml['job'] as $arr) {
            if ($i < 18) {
                unset($arr['expirationdate']);
                unset($arr['remote']);
                $data[$i++] = $arr;
            }
        }

        $data[0]['dt_download'] = date('Y-m-d H:i:s');

        $json = json_encode($data);
        $fh = fopen($file, 'w');
        fwrite($fh, $json);
        fclose($fh);
        if (!$download) {
            return $json;
        } else {
            echo 'downloaded: ' . date('Y-m-d H:i:s');
        }
    }
}
