<?php
namespace App\News;

class GoogleRecaptcha
{
    public static function err($recaptcha_secret = '') {
        // check google recaptcha v3
        if (!isset($_POST['recaptcha_response'])) {
            return 'Failed by Google recaptcha';
        } else {
            // Build POST request:
            $recaptcha_url = 'https://www.google.com/recaptcha/api/siteverify';
            //$recaptcha_secret = '6LfcS8gUAAAAAKRe2SBz5M5E00u9MDyCcmqy-7an'; // for footprintnews
            $recaptcha_response = $_POST['recaptcha_response'];
            // Make and decode POST request:
            $recaptcha = file_get_contents($recaptcha_url . '?secret=' . $recaptcha_secret . '&response=' . $recaptcha_response);
            $recaptcha = json_decode($recaptcha);
            // Take action based on the score returned:
            if (!isset($recaptcha->score) || $recaptcha->score < 0.5) {
                return 'Failed by Google recaptcha score';
            }
        }
    }
}
