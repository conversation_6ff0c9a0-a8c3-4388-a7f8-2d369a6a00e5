<?php
namespace App\News;

use Topnew\Data;

class TagController extends Base
{
    public function home($tags = '', $x = 'page', $page = 1) {
        if (!$tags) {
            return $this->tagIndex();
        }
        // tag=tag1+tag2 means tag1 AND tag2, no OR here
        $this->view = '/' . $this->blog['site']['template'] . '/view/topic';
        $page = max((int)ceil((float)$page), 1);
        $tags = $this->listTagNav($tags, $tag_ids);

        if ($tag_ids === 0) {
            // Tag wasn't found in blog_cat: set err and show the index page.
            $this->set('err', 'The tag you clicked on no longer exists. It may have been removed or renamed.');
            return $this->tagIndex();
        }

        $this->listTopic($tags, $page, $tag_ids);
        $this->listTagSub($tag_ids, $page);
        /*if ($this->blog['site']['id'] == 6) { // footprint -- jo requested to remove 20200505
            $this->listTagRelated($tags, $tag_ids);
        }*/
    }

    private function listTagNav($tags, &$tag_ids) {
        if ($tags) {
            $tags = explode(' ', str_replace('+', ' ', $tags));
            $tags = $this->db->select('cat, name, info')->from('blog_cat')
                ->where('name', $tags)->where('cat', 'like', '2%')->arr();
        }

        $breadcumbs = [['/', 'Home'], ['/tag', 'Browse news by topic']];
        $links = $names = $tag_ids = $tag_names = [];
        if (!$tags) {
            $tag_ids = 0;
        } elseif (count($tags) == 1 && substr(key($tags), 0, 2) == 21) {
            // only one tag which is nestable 21xx
            foreach ($tags as $cat => $tag) {
                $cat4 = substr($cat, 0, 4);
                $tag_root = $this->db->from('blog_cat')->where('cat', $cat4)->row();
                if ($tag_root) {
                    if ($cat > $cat4) {
                        $breadcumbs[] = $this->getCatUrl($tag_root);
                    }
                    $this->set('side_tags_cur', $tag_root['cat']);
                    $this->set('side_tag_cur', $tag['name']);
                }
                $names = [$this->getCatName($tag['name'], $tag['info'])];
                $breadcumbs[] = $names[0];
                $tag_ids = [$cat];
                $tag_names = [$tag['name']];
            }
        } else {
            // multiple tags
            foreach ($tags as $cat => $tag) {
                $names[] = $name = $this->getCatName($tag['name'], $tag['info']);
                $links[] = '<a href="/tag/' . $tag['name'] . '">' . $name . '</a>';
                $tag_ids[] = $cat;
                $tag_names[] = $tag['name'];
            }
            $breadcumbs[] = implode(' <i class="grey">•</i> ', $links);
        }
        $this->set('breadcumbs', $breadcumbs);

        $names = $names ? implode(', ', $names) : 'All articles';
        $this->set('page.title', $names . ' - Australian environmental and carbon news published by Footprint');
        $this->set('page.desc', 'Browse the latest environmental and carbon news news by topic - ' . $names);
        return $tag_names;
    }

    private function listTagRelated($tags, $tag_ids) {
        if (!$tag_ids || count($tag_ids) > 5) {
            return; // max 5 related
        }
        if (!$this->uid && count($tag_ids) > 1) {
            return; // stop bot from digging too much
        }

        $sql = $this->db->select('t.tag, c.name, c.info, count(*) num')
            ->from(['t' => 'blog_tag'])
            ->join(['c' => 'blog_cat'], 't.tag = c.cat')
            ->where('c.cat', 'like', '2%')
            ->group(1)
            ->order('4 desc')
            ->limit(25);
        foreach ($tag_ids as $i => $t) {
            $ti = 't' . $i;
            $sql = $sql->join([$ti => 'blog_tag'], $ti . '.tid = t.tid')
                ->where($ti . '.tag', $t)
                ->where('t.tag', '!=', $t);
        }
        $rows = $sql->all();
        $tags = implode('+', $tags);
        $url = '/tag/' . $tags . '+';
        $related = [];
        foreach ($rows as $tag) {
            $related[] = [
                'url'  => $url . $tag['name'],
                'name' => $this->getCatName($tag['name'], $tag['info']),
                'num'  => $tag['num'],
            ];
        }
        $this->set('tag_related', $related);
    }

    private function listTagSub($tag_ids, $page) {
        if ($page > 1 || !$tag_ids || count($tag_ids) > 1) {
            return;
        }
        $tag = array_pop($tag_ids);
        if (substr($tag, 0, 2) != 21 || $tag > 2200) {
            return;
        }
        $cats = $this->db->from('blog_cat')
            ->where('cat', 'like', $tag . '__')
            ->order('concat(info,name)')
            ->arr();
        if (!$cats) {
            return;
        }
        $stat = $this->baseSQL()->select('c.cat, count(*)')
            ->join(['t' => 'blog_tag'], 'n.tid = t.tid')
            ->join(['c' => 'blog_cat'], 'substr(t.tag, 1, 6) = c.cat')
            ->where('c.cat', 'like', $tag . '__')
            ->where('datediff(n.created, ' . date("'Y-m-d H:i:s'"). ') > -15') // within 14 days marked as new
            ->group(1)
            ->arr();
        $sub_cats = [];
        foreach ($cats as $cid => $arr) {
            $sub_cats[] = [
                '/tag/' . $arr['name'],
                $this->getCatName($arr['name'], $arr['info']),
                isset($stat[$cid]) ? $stat[$cid] : 0
            ];
        }
        $this->set('sub_cats', $sub_cats);
    }

    private function listTopic($tags, $page, $tag_ids) {
        $topics = $this->baseSQL();
        if ($tag_ids) {
            foreach ($tag_ids as $tag_id) {
                $topics = $topics->where('EXISTS',
                    "SELECT 1 FROM blog_tag AS t WHERE n.tid = t.tid AND t.tag " .
                    (substr($tag_id, 0, 2) == 21 ? "like '" . $tag_id . "%'" : '= '. $tag_id)
                );
            }
        }
        $topics = $this->listTopicDate($topics);

        $keyw = Data::Clean(['keyw' => 'keyw'])['keyw'];
        $this->getKeywSQL($topics, $keyw);
        $this->set('keyw', isset($keyw[0]) ? $keyw[0] : '');

        $ttl = clone $topics;
        $page = $this->getTopicsPageNo($ttl, $page, implode('+', $tags), 'tag');

        if (!$page) {
            //$this->set('topics', '');
            $topics = $topics->count(); // delete DB cache
            $this->set('no_result', 1);
            return $this->tagIndex($tags);
        }
        $this->set('topics', $this->getTopicHtml($topics, $page, 2));
        $this->set('sub_cats', '');
        $this->set('num_which', 'tag');
    }

    private function listTopicDate($sql) {
        $date = Data::Clean('date')['date'];
        if (!$date) {
            return $sql;
        }
        $arr = explode('..', $date);
        $fm = Data::cleanDate($arr[0]);
        $to = isset($arr[1]) ? Data::cleanDate($arr[1]) : '';
        if ($to && $to < $fm) {
            Data::swap($fm, $to);
        }
        $this->set('date', $date);
        $this->set('date_fm', $fm);
        $this->set('date_to', $to);

        $key = 'date(n.created)';
        if ($fm) {
            if ($fm == $to) {
                $sql = $sql->where($key, $fm);
            } elseif ($to) {
                $sql = $sql->where($key, 'between', $fm, $to);
            } else {
                $sql = $sql->where($key, '>=', $fm);
            }
        } elseif ($to) {
            $sql = $sql->where($key, '<=', $to);
        }
        return $sql;
    }

    private function tagIndex($tags = []) {
        $new = $this->tagStatNew();
        $rows = $this->db->select('cat, name, info')
            ->from('blog_cat')
            ->where('cat', 'like', '21__%')
            ->order("if (info='', name, info)")
            ->rows();
        $comm_tags = [];
        foreach ($rows as $r) {
            $comm_tags[$r['cat']] = [
                $r['name'],
                $this->getCatName($r['name'], $r['info']),
                'new' => isset($new[$r['cat']]) ? $new[$r['cat']] : 0,
            ];
        }
        $this->set('comm_tags', $comm_tags);
        $this->set('sel_tags', $tags);
        $this->view = '/' . $this->blog['site']['template'] . '/view/tag-index.tpl';
        $this->set('page.title', 'Australian environmental and carbon news published by Footprint');
        $this->set('page.desc', 'Browse the latest environmental and carbon news');
    }
}
