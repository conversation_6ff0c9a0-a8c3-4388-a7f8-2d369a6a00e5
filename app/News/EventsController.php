<?php
namespace App\News;

class EventsController extends Base
{
    public function home() {
        $this->view = '/' . $this->blog['site']['template'] . '/view/Events/home.tpl';
        $sql = $this->db->from(['n' => 'blog_topic'])
            ->where('n.cat', $this->blog['cat_event'])
            ->order('n.rank DESC') // event start date
            ->limit(50);
        $today = date('Y-m-d H:i:s');
        if (! $this->isStaff()) {
            $sql = $sql->where('n.acs < 7')
                ->where('n.created < now()') // non-staff
                ->where('n.updated', '>=', $today) // event end date
                ->order('n.rank');
        }
        $events = $sql->all();
        foreach ($events as $k => $event) {
            $event['meta'] = $meta = json_decode($event['meta'] ?: '[]', 1);
            $event['img']  = isset($meta['img']) ? $this->getTopicImgUrl($event['tid'], $meta['img'], $event['created']) : '';
            $fm = date('Y-m-d', strtotime($event['rank']));
            $to = substr($event['updated'], 0, 10);
            if ($fm == $to) {
                $event['when'] = date('l, d F, Y', strtotime($fm));
            } elseif (substr($fm, 0, 7) == substr($to, 0, 7)) { // same ym
                $event['when'] = substr($fm, -2) . ' - ' . substr($to, -2) . date(' F, Y', strtotime($fm));
            } elseif (substr($fm, 0, 4) == substr($to, 0, 4)) { // same y
                $event['when'] = date('d F', strtotime($fm)) . ' to ' . date('d F, Y', strtotime($to));
            } else {
                $event['when'] = date('d F, Y', strtotime($fm)) . ' to ' . date('d F, Y', strtotime($to));
            }
            $url = isset($meta['url']) ? $meta['url'] : '';
            $url = substr($url, 0, 4) == 'www.' ? 'http://' . $url : $url;
            $url = $url ?: $this->getTopicUrl($event['tid'], $event['url']);
            $event['url'] = $url;
            if (isset($meta['presenter'])) {
                $event['txt'] .= "\n" . '## About the presenter'
                  . "\n" . $this->view('/User/' . $meta['presenter']);
                unset($event['meta']['presenter']);
            }
            if ($meta) {
                foreach ($meta as $k2 => $v) {
                    if (in_array($k2, ['img', 'url', 'event_start', 'event_end', 'summary_short', 'uid_by'])) {
                        // pls add more to ignore
                        unset($event['meta'][$k2]);
                    }
                }
            }
            if ($this->isStaff()) {
                $event['edit'] = '<a href="/admin/news/edit/' . $event['tid'] . '">Edit</a>';
            }
            $event['expired'] = $event['updated'] >= $today ? 0 : 1;
            $event['txt'] = $this->txtToHtml($event['txt'], 1);
            $events[$k] = $event;
        }
        $this->set('events', $events);
        $this->set('page.nav_cur', 5);

        $site = $this->blog['site']['id'];
        $title = $desc = '';
        if ($site == 6) {
            $title = 'Upcoming environmental and carbon conferences and seminars';
            $desc  = 'Find upcoming environmental and carbon events from around Australia.';
        }
        $this->set('page.title', $title);
        $this->set('page.desc',  $desc);
    }
}
