<?php
namespace App\News;

use App\News\Admin\Base;
use App\News\Email\Camp;

class AdminController extends Base
{
    public function home() { 
        $this->set('cat_event', $this->blog['cat_event']);

        // Get the 10 most recent campaigns with template info
        $campaigns = $this->db->select('
            c.id, c.subject, c.queued_ts as ts, 
            SUBSTR(t.temp_type,1,1) as typ')
            ->from(['c' => 'blog_email_camp'])
            ->join(['t' => 'blog_email_temp'], 'c.temp_id = t.id')
            ->order('c.id DESC')
            ->limit(10)
            ->rows();
            
        // Get email stats for these campaigns in a separate query
        if (!empty($campaigns)) {
            $campaignIds = array_column($campaigns, 'id');
            
            $statsRows = $this->db->select('
                camp_id, 
                COUNT(id) as num,
                SUM(sent>0) as sent, 
                <PERSON><PERSON>(err>0) as err, 
                <PERSON><PERSON>(err<0) as stop, 
                <PERSON><PERSON>(bounced>0) as bounced,
                <PERSON>UM(opened>0) as opened, 
                <PERSON>UM(clicked>0) as clicked, 
                <PERSON>UM(unsub>0) as unsub,
                MIN(queued_ts) as ts_send')
                ->from('blog_email')
                ->where('camp_id', $campaignIds)
                ->group('camp_id')
                ->rows();
                
            // Convert rows to associative array with camp_id as key
            $stats = [];
            foreach ($statsRows as $row) {
                $stats[$row['camp_id']] = $row;
            }
            
            // Merge the stats with the campaign data
            foreach ($campaigns as &$campaign) {
                $id = $campaign['id'];
                if (isset($stats[$id])) {
                    $campaign = array_merge($campaign, $stats[$id]);
                } else {
                    // Set default values if no stats found
                    $campaign['num'] = 0;
                    $campaign['sent'] = 0;
                    $campaign['err'] = 0;
                    $campaign['stop'] = 0;
                    $campaign['bounced'] = 0;
                    $campaign['opened'] = 0;
                    $campaign['clicked'] = 0;
                    $campaign['unsub'] = 0;
                    $campaign['ts_send'] = null;
                }
            }
        }
        
        $this->set('camp', $campaigns);
        $this->set('page.title', 'Admin Dashboard');
    }

    public function campPreview($id = 0, $temp_id = 0) {
        $this->view = false;
        if ($id) {
            echo $this->db->select('txt')->from('blog_email_camp')->where('id', $id)->val();
        } elseif ($temp_id) {
            $temp = $this->db->from('blog_email_temp')->where('id', $temp_id)->row();
            if ($temp) {
                $camp = new Camp();
                echo $camp->getEmailTxt($temp);
            }
        }
    }
}
