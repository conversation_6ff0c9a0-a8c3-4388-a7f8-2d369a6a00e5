<?php
namespace App\News;

use Topnew\Data;

class UnsubscribeController extends Base
{
    public function home() {
        $user = $this->validData($camp);
    }

    public function thankYou() {
        $user = $this->validData($camp);
        if (!$user || $user['status'] == 'closed') {
            return;
        }
        if (!$user['cmd']) {
            return;
        }

        // now doing unsub: status=closed, non-premium moved to org#2
        $val = ['status' => 'closed'];
        if ($user['acs'] < 3) {
            $val['purchaser'] = 2;
        }
        $this->db->where('uid', $user['uid'])->update('blog_user', $val);

        // if unsub from email
        if ($user['eid']) {
            $this->db->where('id', $user['eid'])->update('blog_email', ['unsub' => 1]);
        }
        $this->view = '/' . $this->blog['site']['template'] . '/view/Unsubscribe/thankYou.tpl';

        // then user log
        $this->db->insert('inv_log', [
            'uid' => $user['uid'],
            'purchaser' => $user['purchaser'],
            'by_uid' => $this->uid,
            'status' => 'Note',
            'txt' => 'Unsubscribe' . ($camp ? ' from email #' . $camp['camp_id'] : ''),
        ]);
    }

    private function validData(&$camp = []) {
        $this->view = '/' . $this->blog['site']['template'] . '/view/Unsubscribe/home.tpl';
        $this->set('page.title', 'Unsubscribe | ' . $this->blog['site']['name']);
        $data = Data::clean(['eid' => 'int', 'uid' => 'int', 'cmd']);
        $camp = [];
        if ($data['uid']) {
            //double check eid and uid match with records
            $camp = $this->db->select('uid,camp_id')->from('blog_email')
                ->where('id', $data['eid'])->where('uid', $data['uid'])->row();
        }
        $uid = $camp ? $camp['uid'] : $this->uid;

        if (!$uid) {
            return [];
        }
        $user = $this->db->select('uid,acs,purchaser,status,fname,lname,email')
            ->from('blog_user')->where('uid', $uid)->row();

        $user['eid'] = $data['eid'];
        $user['cmd'] = $data['cmd'];
        $this->set('cust', $user);
        return $user;
    }
}
