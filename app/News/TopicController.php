<?php
namespace App\News;

use Topnew\Data;

class TopicController extends Base
{
    public function home($cat = '', $x = 'page', $page = 1) {
        if (isset($_GET['stream']) && $_GET['stream']) {
            $row = $this->db->from('blog_cat')
                ->where('stream', $_GET['stream'])->row();
            if ($row) {
                if (substr($row['cat'], 0, 1) == 2) {
                    header('location:/tag/' . $row['name']);
                    exit;
                } elseif (!$cat) {
                    $cat = $row['cat'];
                }
            }
        } // old system redirect from topics.php?stream=xxx

        $this->view = '/' . $this->blog['site']['template'] . '/view/topic';
        $data = [
            'cat',  // cat1,cat2,...
            'keyw' => 'keyw', // "exact match" // all +keyw // any keyw // all -notWord
            'date', // 2019-01-01 11:22:33 .. 2019-12-31 22:22:22
            'tag' => 'keyw',  // tag-name
            'acs' => 'int', // 0 open 1 free 2 plan 3 premium ...
            'hit',  // 123 .. 456
            'rank', // 123 .. 456
            'author' => 'int',
            'related' => 'int',
        ];
        $data = Data::clean($data);
        $this->data = array_merge($data, $this->data);
        $page = max((int)ceil((float)$page), 1);
        $cat = $this->listCatNav($cat, $cat_name);
        $this->listCatSub($cat, $page);
        $this->listTopic($cat, $page, $cat_name);
        $this->set('page.title', 'Footprint - The latest Australian environmental and carbon news');
        $this->set('page.desc', 'Browse the latest environmental and carbon news news');
        $this->set('page.nav_cur', 1);
        $this->set('keyw', isset($data['keyw'][0]) ? $data['keyw'][0] : '');
    }

    public function index($cat = '', $x = 'page', $page = 1) {
        $this->home($cat, $x, $page);
        $this->view =  $this->blog['site']['template'] . '/view/index.tpl';
    }

    private function listCatNav($cat, &$cat_name) {
        $cat = $this->validCat($cat);
        if ($cat == $this->blog['cat_base']) {
            $this->set('breadcumbs', 'Latest news');
            return $cat;
        }

        $cats = [];
        $len = strlen($cat);
        for ($i = strlen($this->blog['cat_base']) + 2; $i <= $len; $i += 2) {
            $cats[] = substr($cat, 0, $i);
        }

        $cats = $this->db
            ->from('blog_cat')
            ->where('cat', $cats)
            ->order('cat')
            ->rows();

        $breadcumbs = [['/', 'Home'], ['/topic', 'All Articles']];
        foreach ($cats as $arr) {
            $cat_name = $arr['name']; // pass back
            $breadcumbs[] = [
                '/' . substr($arr['cat'], 0, 1) == 2 ? 'tag' : 'topic' . '/' . $arr['name'],
                $this->getCatName($arr['name'], $arr['info'])
            ];
        }
        $this->set('breadcumbs', $breadcumbs);
        return $cat;
    }

    private function listCatSub($cat, $page) {
        $this->set('sub_cats', '');
        if ($cat == $this->blog['cat_base']) {
            if (in_array($_SERVER['REQUEST_URI'], ['/', '/index.php']) || $page > 1) {
                return;
            }
        }

        $cats = $this->db->from('blog_cat')
            ->where('cat', 'like', $cat . '__')
            ->where('cat', '!=', $this->blog['cat_event']) // exclude events
            ->order('concat(info,name)')
            ->arr();
        if (!$cats) {
            return;
        }

        $cat_len = strlen($cat) + 2;
        $stat = $this->baseSQL()->select('c.cat, count(*)')
            ->join(['t' => 'blog_tag'], 'n.tid = t.tid')
            ->join(['c' => 'blog_cat'], 'substr(t.tag, 1, ' . $cat_len . ') = c.cat')
            ->where('c.cat', 'like', $cat . '__')
            ->where('datediff(n.created, now()) > -15') // within 14 days marked as new
            ->group(1)
            ->arr();

        $sub_cats = [];
        foreach ($cats as $cid => $arr) {
            $sub_cats[] = [
                '/topic/' . $arr['name'],
                $this->getCatName($arr['name'], $arr['info']),
                isset($stat[$cid]) ? $stat[$cid] : 0
            ];
        }
        $this->set('sub_cats', $sub_cats);
    }

    private function listSearch() {
        if (! $this->isStaff()) {
            $this->data['acs'] = $this->data['hit'] = $this->data['rank'] = $this->data['author'] = $this->data['related'] = '';
        }

        $tags = $this->data['tag'];
        $tags['tag_and'] = $tags['AND'] ? $this->db->select('cat')->from('blog_cat')->where('cat', 'like', '2%')->where('name', $tags['AND'])->enum() : '';
        $tags['tag_or']  = $tags['OR']  ? $this->db->select('cat')->from('blog_cat')->where('cat', 'like', '2%')->where('name', $tags['OR'])->enum() : '';
        $tags['tag_not'] = $tags['NOT'] ? $this->db->select('cat')->from('blog_cat')->where('cat', 'like', '2%')->where('name', $tags['NOT'])->enum() : '';

        $sql = $this->baseSQL(); // this have to run after above tags to avoid cash
        if ($this->data['acs']) {
            $sql = $sql->where('n.acs', $this->data['acs'] < 0 ? 0 : $this->data['acs']);
        }
        if ($this->data['author']) {
            $sql = $sql->where('n.uid', $this->data['author']);
        }
        if ($this->data['cat']) {
            $arr = explode(',', $this->data['cat']);
            foreach ($arr as $k) {
                $k = ceil($k);
                if ($k > $this->blog['cat_base']) {
                    $sql = $sql->where('exists', "SELECT 1 FROM blog_tag WHERE tid = n.tid AND tag like '$k%'");
                }
            }
        }
        if ($this->data['related']) {
            $rid = $this->data['related'];
            $sql = $sql->where('exists', 'SELECT 1 FROM blog_related AS r WHERE (r.tid1 = n.tid AND r.tid2 = ' . $rid . ') OR (r.tid2 = n.tid AND r.tid1 = ' . $rid . ')');
        }
        $sql = $this->listSearchRange($sql, 'hit');
        $sql = $this->listSearchRange($sql, 'rank');
        $sql = $this->listSearchRange($sql, 'date');
        $sql = $this->listSearchTags($sql, $tags);
        $sql = $this->getKeywSQL($sql, $this->data['keyw']);
        return $sql;
    }

    private function listSearchTags($sql, $tags) {
        if ($tags['AND']) {
            if (!$tags['tag_and'] || count($tags['tag_and']) < count($tags['AND'])) {
                return $sql->where('1 = 0');
            }
            $sql = $sql->where('EXISTS', 'SELECT count(*) FROM blog_tag WHERE tid = n.tid AND tag IN (' . implode(', ', $tags['tag_and']) . ') having count(*) = ' . count($tags['tag_and']));
        }
        if ($tags['OR']) {
            if (!$tags['tag_or']) {
                return $sql->where('1 = 0');
            }
            $sql = $sql->where('EXISTS', 'SELECT 1 FROM blog_tag WHERE tid = n.tid AND tag IN (' . implode(', ', $tags['tag_or']) . ')');
        }
        if ($tags['tag_not']) {
            $sql = $sql->where('NOT EXISTS', 'SELECT 1 FROM blog_tag WHERE tid = n.tid AND tag IN (' . implode(', ', $tags['tag_not']) . ')');
        }
        return $sql;
    }

    private function listSearchRange($sql, $key) {
        if (!$this->data[$key]) {
            return $sql;
        }
        $arr = explode('..', $this->data[$key]);
        $fm = $key == 'date' ? Data::cleanDate($arr[0]) : ceil($arr[0]);
        $to = isset($arr[1]) ? ($key == 'date' ? Data::cleanDate($arr[1]) : ceil($arr[1])) : 0;
        if ($to && $to < $fm) {
            Data::swap($fm, $to);
        }
        $this->data[$key] = $fm . '..' . $to;
        $key = $key == 'date' ? 'date(n.created)' : 'n.' . $key;
        if ($fm) {
            if ($fm == $to) {
                $sql = $sql->where($key, $fm);
            } elseif ($to) {
                $sql = $sql->where($key, 'between', $fm, $to);
            } else {
                $sql = $sql->where($key, '>=', $fm);
            }
        } elseif ($to) {
            $sql = $sql->where($key, '<=', $to);
        }
        return $sql;
    }

    private function listTopic($cat, $page, $cat_name) {
        $topics = $this->listSearch()
            ->where('EXISTS', 'SELECT 1 FROM blog_tag AS t WHERE n.tid = t.tid AND t.tag like ' . "'" . ceil($cat) . "%'");
        $ttl = clone $topics;
        $page = $this->getTopicsPageNo($ttl, $page, $cat_name);

        if (!$page) {
            return;
        }
        $this->set('topics', $this->getTopicHtml($topics, $page, 1)); // inc tag ?
        $keyw = $this->data['keyw'][0];
        $this->set('num_which', $keyw ? 'search: ' . htmlspecialchars($keyw, ENT_NOQUOTES) : 'topic');
    }

    private function validCat($cat = '') {
        if (!$cat) {
            return $this->blog['cat_base'];
        }
        $sql = $this->db
            ->select('cat')
            ->from('blog_cat')
            ->where('cat', 'like', $this->blog['cat_base'] . '%');
        if (is_numeric($cat)) {
            $sql = $sql->where('or', ['name' => $cat, 'cat' => $cat]);
        } else {
            $sql = $sql->where('name', $cat);
        }
        return $sql->val() ?: $this->blog['cat_base'];
    }
}
