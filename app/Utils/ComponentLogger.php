<?php
namespace App\Utils;

/**
 * Custom logging component that allows customizing log file names based on the client code using it.
 * 
 * This component creates separate log files for different components (e.g., tag_controller_errors.log)
 * while maintaining the same format and behavior as PHP's built-in error_log function.
 * 
 * Usage:
 *   $logger = new ComponentLogger('tag_controller');
 *   $logger->error('Something went wrong');
 *   $logger->warning('This is a warning');
 *   $logger->info('Informational message');
 *   $logger->debug('Debug information');
 */
class ComponentLogger {
    
    // Log levels
    const ERROR = 'ERROR';
    const WARNING = 'WARNING';
    const INFO = 'INFO';
    const DEBUG = 'DEBUG';
    
    private $componentName;
    private $logFilePath;
    
    /**
     * Constructor
     * 
     * @param string $componentName The name of the component (e.g., 'tag_controller')
     */
    public function __construct($componentName) {
        $this->componentName = $componentName;
        $this->logFilePath = $this->getLogFilePath($componentName);
    }
    
    /**
     * Log an error message
     * 
     * @param string $message The message to log
     */
    public function error($message) {
        $this->log($message, self::ERROR);
    }
    
    /**
     * Log a warning message
     * 
     * @param string $message The message to log
     */
    public function warning($message) {
        $this->log($message, self::WARNING);
    }
    
    /**
     * Log an info message
     * 
     * @param string $message The message to log
     */
    public function info($message) {
        $this->log($message, self::INFO);
    }
    
    /**
     * Log a debug message
     * 
     * @param string $message The message to log
     */
    public function debug($message) {
        $this->log($message, self::DEBUG);
    }
    
    /**
     * Log a message with a specific level
     * 
     * @param string $message The message to log
     * @param string $level The log level (defaults to ERROR)
     */
    public function log($message, $level = self::ERROR) {
        // Format the message similar to PHP's error_log format
        $timestamp = date('d-M-Y H:i:s T');
        $formattedMessage = "[{$timestamp}] [{$level}] {$message}";
        
        // Use PHP's error_log function with custom destination
        error_log($formattedMessage . PHP_EOL, 3, $this->logFilePath);
    }
    
    /**
     * Get the log file path for a component
     * 
     * @param string $componentName The component name
     * @return string The full path to the log file
     */
    private function getLogFilePath($componentName) {
        // Get the default error log path from PHP configuration
        $defaultLogPath = ini_get('error_log');
        
        if ($defaultLogPath && $defaultLogPath !== 'syslog') {
            // If a specific file is configured, use its directory
            $logDir = dirname($defaultLogPath);
        } else {
            // Fallback to common log directories
            $possibleDirs = [
                'log',
                'logs',
                '/var/log',
                sys_get_temp_dir()
            ];
            
            $logDir = null;
            foreach ($possibleDirs as $dir) {
                if (is_dir($dir) && is_writable($dir)) {
                    $logDir = $dir;
                    break;
                }
            }
            
            // If no writable directory found, use current directory
            if (!$logDir) {
                $logDir = '.';
            }
        }
        
        // Create the log file name: {component_name}_errors.log
        $logFileName = $componentName . '_errors.log';
        
        return rtrim($logDir, '/') . '/' . $logFileName;
    }
    
    /**
     * Get the current log file path
     * 
     * @return string The log file path
     */
    public function getLogFile() {
        return $this->logFilePath;
    }
    
    /**
     * Get the component name
     * 
     * @return string The component name
     */
    public function getComponentName() {
        return $this->componentName;
    }
}
