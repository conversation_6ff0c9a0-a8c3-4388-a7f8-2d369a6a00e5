name: Deploy UAT to DED1 # WebApp2021 Application Code

env:
  BRANCH: "uat"

on:
  push:
    branches: 
      - uat

  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
      - name: HR Daily - ${{ github.event.repository.name }}
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DED1_SSH_HOST }}
          username: ${{ secrets.DED1_SSH_USER_HD }}uat
          key: ${{ secrets.DED1_SSH_KEY_UAT }}
          passphrase: ${{ secrets.DED1_SSH_KEY_UAT_PASSPHRASE }}
          envs: BRANCH

          script: |
            cd ~/public_html/
            git status
            git checkout $BRANCH
            git pull
            echo 'UAT Deployment successful'

      - name: OHS Alert - ${{ github.event.repository.name }}
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DED1_SSH_HOST }}
          username: ${{ secrets.DED1_SSH_USER_OA }}uat
          key: ${{ secrets.DED1_SSH_KEY_UAT }}
          passphrase: ${{ secrets.DED1_SSH_KEY_UAT_PASSPHRASE }}
          envs: BRANCH

          script: |
            cd ~/public_html/
            git status
            git checkout $BRANCH
            git pull
            echo 'UAT Deployment successful'

      - name: Shortlist - ${{ github.event.repository.name }}
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DED1_SSH_HOST }}
          username: ${{ secrets.DED1_SSH_USER_SL }}uat
          key: ${{ secrets.DED1_SSH_KEY_UAT }}
          passphrase: ${{ secrets.DED1_SSH_KEY_UAT_PASSPHRASE }}
          envs: BRANCH

          script: |
            cd ~/public_html/
            git status
            git checkout $BRANCH
            git pull
            echo 'UAT Deployment successful'

      - name: Workplace Express - ${{ github.event.repository.name }}
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.DED1_SSH_HOST }}
          username: ${{ secrets.DED1_SSH_USER_WE }}uat
          key: ${{ secrets.DED1_SSH_KEY_UAT }}
          passphrase: ${{ secrets.DED1_SSH_KEY_UAT_PASSPHRASE }}
          envs: BRANCH

          script: |
            cd ~/public_html/
            git status
            git checkout $BRANCH
            git pull
            echo 'UAT Deployment successful'
