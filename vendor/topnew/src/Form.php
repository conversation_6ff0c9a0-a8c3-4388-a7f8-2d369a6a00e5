<?php
namespace Topnew;

/**
 * TODO -- before $form->form(), there is no $form->data,
 * we need reverse the order so that $data is init before output of form
 * for the time being we have to heck in this way if you need data:
 * =================================================
 * $form = new Form($cols);
 * $html = $form->form(); // without this line, next line no value
 * $data = $form->data; // TODO we want this line happen after new Form()
 * echo $html; // whatever you want
 */

/**
 * Topnew Form v 2018.10.10 - PHP HTML Form generator
 * The MIT License (MIT) Copyright (c) 1998-2018, Topnew Geo, topnew.net
 *
 * css class used: form, formH, err, line, upload, upload-info  -- pls check form.css
 * jQuery used: $('.formH').each(); which need 2 html attr: data-kid, data-label
 * also js.ajax used and talk to /ajax via App.Controller.AjaxController
 *
 * For usage please check docs @ attr()
 */
class Form
{
    public  $data = [];
    private $boot  = 1;
    private $boot1 = "\n";
    private $boot2 = '';
    private $cols  = [];
    private $cout  = 0; // if true echo html
    private $err   = [];
    private $form  = [];
    private $indent= '';
    private $token = 'cms_form_token';
    private $ajax  = '/ajax';
    private $ajax_controller = 'App.Controller.AjaxController';

    public function __construct($cols = [], $init = []) { $this->setForm($cols, $init); }

    public function setForm($cols = [], $init = []) {
        if ($cols && !is_array($cols)) {
            $cols = json_decode($cols, 1);
        }
        $this->cols = is_array($cols) ? $cols : [];

        // assign init value for each col -- before form submit
        if ($init && is_array($init)) {
            foreach ($this->cols as $k => $v) {
                if (!isset($v['value']) && isset($init[$k]) && !isset($_POST[$k]) && !isset($_GET[$k])) {
                    $this->cols[$k]['value'] = $init[$k];
                }
            }
        }

        // init cols[form]
        $form = isset($cols['form']) ? $cols['form'] : [];
        $form['method'] = isset($form['method']) && $form['method'] == 'get' ? 'get' : 'post'; // default post
        if (!isset($form['global']) || !is_array($form['global'])) {
            $form['global'] = [];
        }
        if (!isset($form['global']['box_class'])) {
            $form['global']['box_class'] = 'line'; // default css line class
        }
        if (!isset($form['global']['err_msg'])) {
            $form['global']['err_msg'] = '<b>There are some errors. Please correct them below:</b>'; // default err msg
        }
        if (!isset($form['global']['acs'])) {
            $form['global']['acs'] = 'W'; // default access for all cols: W | R | N -- be careful of this setting
        }
        $arr = ['label', 'id_prefix', 'class', 'label_class', 'valid', 'valid_all', 'all_req', 'role'];
        foreach ($arr as $k) {
            if (!isset($form['global'][$k])) {
                $form['global'][$k] = '';
            }
        }

        // Syn form.class.label-0 == form.global.label.-1
        $form['class'] = isset($form['class']) ? $form['class'] : '';
        $form_no_label = (strpos($form['class'], 'label-0') !== false);
        if ($form_no_label) {
            $form['global']['label'] = -1;
        } elseif ($form['global']['label'] == -1) {
            $form['class'] = trim($form['class'] . ' label-0');
        }
        $this->form = $this->cols['form'] = $form;

        if ($cols && !isset($this->cols['end'])) {
            $this->cols['end'] = '';
        }
    }
    // method short-hand alias
    public function cbox ($name = '', $data = '', $attr = []) { return $this->checkbox($name, $data, $attr); }
    public function radio($name = '', $data = '', $attr = []) { return $this->checkbox($name, $data, $attr, 'radio'); }
    public function sele ($name = '', $data = '', $attr = []) { return $this->select  ($name, $data, $attr); }

    public function ajax ($name = '', $data = '', $attr =[] ) {
        /**
         * This need to work with js.ajax
         * attr.data-src = ajax will trigger against data-src tag
         * attr.data-cmd = mouse(enter) == hover | click | change (default)
         * attr.data-url = ajax controller page
         * ajax.func == AjaxController->func()
         * ajax.name ????
         * ajax.tag  -- html.tag default div
         * ajax.html = -1 no output | html | default to call AjaxController::func()
         */
        $attr = $this->attr($name, $data, $attr, 'ajax');
        $ajax = [];
        if (isset($attr['ajax']) && is_array($attr['ajax'])) {
            $ajax = $attr['ajax'];
            unset($attr['ajax']);
        }

        if (!isset($ajax['func'])) {
            $ajax['func'] = (substr($name, -3) == '_id') ? substr($name, 0, -3) : $name;
        }
        if (!isset($ajax['name'])) {
            $ajax['name'] = $name; // do we need name here ?
        }

        $tag = 'div';
        if (isset($ajax['tag'])) {
            $tag = $ajax['tag'];
            unset($ajax['tag']);
        }

        // ======== start to get ajax.attr
        if (!isset($ajax['attr']) || !is_array($ajax['attr'])) {
            $ajax['attr'] = $attr;
        } else {
            $ajax['attr'] = array_merge($ajax['attr'], $attr);
        }
        if (isset($ajax['attr']['data-url'])) {
            unset($ajax['attr']['data-url']);
        }
        if (isset($ajax['attr']['data-cmd'])) {
            unset($ajax['attr']['data-cmd']);
        }

        $global = $this->form['global'];
        if (isset($this->form['data-kid'])) {
            $ajax['attr']['class'] = trim($ajax['attr']['class'] . ' ' . $this->form['data-kid']);
        }

        $ajax['attr']['id'] = $attr['id'];
        unset($attr['id']);
        // ======== end of get ajax.attr

        if (!isset($attr['data-url'])) { // url of ajax controller
            $attr['data-url'] = isset($global['ajax']) ? $global['ajax'] : $this->ajax;
        }

        if (isset($attr['data-cmd'])) {
            if ($attr['data-cmd'] == 'mouse') {
                $attr['data-cmd'] = 'mouseenter'; // alias of mouseenter == css.hover i.e. mouse over
            } elseif (!in_array($attr['data-cmd'], ['click', 'mouseenter'])) {
                unset($attr['data-cmd']); // default change
            }
        }

        if (isset($ajax['html'])) {
            $html = $ajax['html'] == -1 ? '' : $ajax['html'];
            unset($ajax['html']);
        } else {
            $ajax_controller = isset($attr['ajax_controller']) ? $attr['ajax_controller'] : (isset($global['ajax_controller']) ? $global['ajax_controller'] : $this->ajax_controller);
            $ajax_controller = str_replace('.', '\\', $ajax_controller);

            // get data-src -- this need do via $this->data() in case $this->cols[$src][value]
            // not exists yet due to the sequence of form elements run from top to bottom
            // unless in future in setForm() to pre-populate each col
            if (isset($attr['data-src'])) {
                $src_val = $attr['data-src'];
                $src_val = isset($_POST[$src_val]) ? $_POST[$src_val] : (
                    isset($_GET[$src_val]) ? $_GET[$src_val] : (
                        isset($this->cols[$src_val]['value']) ? $this->cols[$src_val]['value'] : (
                            isset($this->cols[$src_val]['data']) ? $this->cols[$src_val]['data'] : ''
                )));
            } else {
                $src_val = isset($attr['data-val']) ? $attr['data-val'] : '';
            }

            $ajax_controller = new $ajax_controller($ajax, $src_val);
            $html = $ajax_controller->home();
        }
        if (isset($attr['data-src'])) {
            $attr['data-src'] = '#' . $attr['data-src'];
        }
        if (isset($attr['ajax_controller'])) {
            unset($attr['ajax_controller']);
        }

        $attr['data-ajax'] = base64_encode(json_encode($ajax));
        $attr['class'] = isset($attr['class']) ? trim($attr['class'] .' ajax') : 'ajax';
        // ajax.tag no need name and value -- fix later as it does not hurt
        return $this->cout('<' . $tag . $this->attrHtml($attr) . '>' . $html . '</' . $tag . '>', $attr);
    }

    public function boot1($name = '', $data = '', $attr = []) {
        $attr = $this->attr($name, $data, $attr, 'boot1');
        return $this->cout('', $attr, 1, 0);
    }
    public function boot2()   { return $this->cout("\n</div>",  [], 0, 0); }
    public function end()     { return $this->cout("\n</form>", [], 0, 0); }
    public function coutOff() { $this->cout = 0; }
    public function coutOn()  { $this->cout = 1; }

    // attr.class_sepa : separator between items
    // attr.class_no_hidden: if set, no hidden tag, which will be lost after form submit if no value set
    public function checkbox($name = '', $data = '', $attr = [], $type = 'checkbox') {
        $type = ($type == 'radio') ? $type : 'checkbox';
        $attr = $this->attr($name, $data, $attr, $type);
        if ($attr['acs'] == 'N') {
            return;
        }
        $html = '';
        if ($attr['name'] && !in_array('class_no_hidden', $attr) && !isset($attr['class_no_hidden'])) {
            $html = '<input type="hidden" name="' . $attr['name'] . '">' . $this->indent;
        }
        if ($attr['name'] && 'checkbox' === $type && substr($attr['name'], -1) != ']' && count($attr['list']) > 1) {
            $attr['name'] .= '[]';
        }

        $sepa = isset($attr['class_sepa']) ? $attr['class_sepa'] : ' '; // default seperator
        $i = 0;
        $attrHtml = $attr['label'] == -1 ? $this->attrHtml($attr) : (isset($attr['class_cbox_label']) ? ' class="' . $attr['class_cbox_label'] .'"' : '');
        foreach ($attr['list'] as $k => $v) {
            $html .= ($i++ ? $sepa . $this->indent : '');
            $html .= '<label' . $attrHtml . '><input type="' . $type . '" name="' . $attr['name'] . '" value="' . $this->html8($k) . '"';
            $html .= $attr['acs'] == 'R' ? ' disabled' : '';
            $html .= $this->selected($k, $attr['value'], ' checked') .'>';
            $html .= strlen($v) ? ' ' : '';
            $html .= isset($attr['class_' . $k]) ? '<span class="' . $attr['class_' . $k] . '">' . $v . '</span>' : $v;
            $html .= '</label>';
        }
        return $this->cout($html, $attr);
    }

    public function err($err = '') {
        $err = $err ?: $this->err;
        if (!$err) {
            return;
        }

        $html = "\n" . '<p class="err">' . "\n  " . $this->form['global']['err_msg'] . '<br>';
        $err = is_array($err) ? $err : [$err];
        foreach ($err as $k => $v) {
            $v = is_array($v) ? $v : [$v];
            foreach ($v as $e) {
                $html .= "\n" . '  » <a href="#' . $this->form['global']['id_prefix'] . $k . '">' . $e . '</a><br>';
            }
        }
        return $this->cout($html . "\n</p>", [], 0, 0);
    }

    // form()                 will loop over $cols to generate the whole form
    // form()                 will generate <form> if $cols not set
    // form(name, data, type) will goto ->type(name, data)
    // form(name, data, attr) will goto ->type(name, data, attr)
    public function form($name = '', $data = '', $attr = [], $loop = 1) {
        if (!$name && !$data && !$attr && $loop) { // $form->form()
            if ($this->cols) {
                $cout = $this->cout;
                $this->cout = 0;
                $html = '';
                foreach ($this->cols as $name => $attr) {
                    $html .= $this->form($name, '', $attr, 0);
                }
                $err = $this->err();
                $this->cout = $cout;
                return $this->cout($err . $html, [], 0, 0);
            } else {
                return $this->form('form', '', ['type' => 'form'], 0);
            }
        } elseif ($loop) { // $form->form($name) -- manually call one by one
            if ($name && isset($this->cols[$name])) {
                $col = $this->cols[$name];
                if (!$attr) {
                    $attr = $col;
                } elseif ($col) {
                    $col  = is_array($col)  ? $col  : [$col];
                    $attr = is_array($attr) ? $attr : [$attr];
                    $attr = array_merge($col, $attr);
                }
            }
            return $this->form($name, $data, $attr, 0);
        }

        $method = $this->attrTypeMethod($name, $attr, $type);
        if ($type != 'form') {
            return $this->$method($name, $data, $attr, $type);
        }

        if (!$method) {
            return;
        }
        // the following is actually <form ...>
        $attr = $this->attr($name, $data, $attr, 'form');
        if (!isset($attr['action']) && isset($attr['url'])) {
            $attr['action'] = $attr['url']; // alias
            unset($attr['url']);
        }
        if (!isset($attr['action']) || !$attr['action']) {
            $url = explode('?', isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : $_SERVER['SCRIPT_NAME'], 2);
            $arr = explode('/', $url[0]);
            $attr['action'] = (end($arr) == 'index.php') ? './' : implode('/', $arr);
        }
        $html = "\n<form" . $this->attrHtml($attr) . '>';
        if ($this->form['method'] == 'post') {
            $html .= $this->token(isset($attr['token']) ? $attr['token'] : '');
        }
        return $this->cout($html, [], 0, 0);
    }

    public function html($name = '', $data = '', $attr = []) {
        $attr = $this->attr($name, $data, $attr, 'html');
        $data = $data ?: $attr['value'];
        return $this->cout($data, $attr);
    }

    /**
     * Build SQL where ------------------------------------
     * if col[sql] == -1 no search
     * if col[sql] is not set, default search operator is =
     * like %value%; start = like value%; end = like %value
     * col[sql] = [col-name, operator, value]
     */
    public function search($search = null) {
        $where = [];
        $skip = ['form', 'submit', 'reset', 'html']; // default no search
        foreach ($this->cols as $k => $attr) {
            if (is_array($search) && isset($search[$k])) {
                if ($search[$k] != -1) {
                    $where[] = $search[$k];
                }
            } else {
                $op = isset($attr['sql']) ? $attr['sql'] : '=';
                $val = isset($attr['value']) ? $attr['value'] : '';
                if (is_array($val) && !reset($val)) {
                    $val = ''; // if first value is 0 ignore?
                }
                if ($val && $op != -1 && ((!isset($attr['type']) || !in_array($attr['type'], $skip)) || $op == 1)) {
                    if (is_array($op)) { // [col, op, val]
                        $k   = $op[0] ?: $k;
                        $val = isset($op[2]) ? $op[2] : $val;
                        $op  = isset($op[1]) ? $op[1] : '=';
                    }
                    if ('start' == $op) {
                        $val .= '%';
                    } elseif ('end' == $op) {
                        $val = '%' . $val;
                    } elseif ('like' == $op) {
                        $val = '%' . $val . '%';
                    }
                    $op = (1 == $op) ? '=' : (in_array($op, ['start', 'end', 'like']) ? 'LIKE' : $op);
                    $where[] = [$k, $op, $val];
                }
            }
        }
        if ($search && !is_array($search)) {
            $where[] = $search;
        }
        return $where;
    }

    public function select($name = '', $data = '', $attr = []) {
        $attr = $this->attr($name, $data, $attr, 'select');
        if ($attr['acs'] == 'N') {
            return;
        }
        if (in_array('multiple', $attr)) {
            $attr['name'] .= '[]';
        }
        $html = '<select size="1"' . $this->attrHtml($attr) . '>';
        foreach ($attr['list'] as $k => $v) {
            if (is_array($v)) {
                $html .= $this->indent . '<optgroup label="' . $this->html8($k) . '"';
                $html .= isset($attr['class_' . $k]) ? ' class="' . $attr['class_' . $k] .'"' : '';
                $html .= '>';
                foreach ($v as $k2 => $v2) {
                    $html .= $this->selectOption($attr, $k2, $v2, '  ');
                }
                $html .= $this->indent . '</optgroup>';
            } else {
                $html .= $this->selectOption($attr, $k, $v);
            }
        }
        $html .= "\n" . ($this->boot ? '  ' : '') . '</select>';
        return $this->cout($html, $attr);
    }

    public function text($name = '', $data = '', $attr = [], $type = 'text') {
        $attr = $this->attr($name, $data, $attr, $type);
        if ($attr['acs'] == 'N') {
            return;
        }
        $html = '<input type="' . $attr['type'] . '"' . $this->attrHtml($attr) . '>';
        return $this->cout($html, $attr);
    }

    public function textarea($name = '', $data = '', $attr = []) {
        $attr = $this->attr($name, $data, $attr, 'textarea');
        if ($attr['acs'] == 'N') {
            return;
        }
        $html = '<textarea' . $this->attrHtml($attr) . '>';
        $html.= str_replace('</textarea>', '&lt;/textarea>', $attr['value']) . '</textarea>';
        return $this->cout($html, $attr);
    }

    /* eg $cols = ['form' => [ // some of form attributes will apply for all childrens inside form -- must be 1st key
        'url' => 'action.php',
        'method' => 'get', // default post
        'global' => [
            'id_prefix' => 'form_',
            'class' => 'global css class for any form field within',
            'label_class' => 'global css class for any label within',
            'box_class'=> 'line',
            'valid' => URL (on page loading) | GET | POST (default when form posted)
            'err_msg'  => '<b>Customer Error Msg</b>',
            'acs' => 'W', // N | R
            'valid' => 'URL', // URL: onload; GET; POST (default); NONE (no need valid)
            'valid_all' => 0, // 0 stop at err, else all
            'all_req' => 1 // 1 all required
            'role' => 'admin' // current user role / group / acs etc
            'ajax' => '/ajax' // page where ajax can call it
            'ajax_controller' => 'App.Controller.AjaxController' // ajax controller
            'label' => -1, // or class = label-0
        ],
        'data-label' => 'cell-3', // add to all kid line's label
        'data-kid'   => 'cell-9', // add to all kid line's input select textarea etc
    ], 'key' => [
        'name' => 'otherName' // default key of this item i.e. 'key'
        'type' => 'cbox' // default 'sele' if isset(list) else 'ajax' if isset(ajax) else 'text'
        'list' => [....] // for select, cbox, radio -- if type sele cbox radio and no list, list = attr ?: label
        'id'   => 'name' // default 'key'
        'style'=> 'css styles'
        'other html attributes ...'
        'multiple', // for a multi-select
        'defa' => 'Please select', // for a select or submit
        'label'=> '....', // if -1 no label
        'class_sepa' => '<br>', // default sepa for cbox is ' '
        'class_no_hidden', // or 'class_no_hidden' => '' for cbox
        'class_cbox_label' => 'css class for cbox item label'
        'label_class'=> 'css class for label'
        'label_'     => 'html attributes for label eg style="..." id="..." etc'
        'box_class'  => 'css class for parent box'
        'box_'       => 'html attributes for parent box'
        'sql' => '!=' // or array(key_alias[, op[, value]]) or -1 turn off or 1 turn on default =
        'clean' => str,1,1,1 | enum | int | num,2 | date etc pls check Data::clean()
        'acs' => N (no access) | R (read only) | W (writeable) or [All => R, Admin => W, etc]
        'valid' => req | email | min | max etc pls check Data::valid()
        'class_prefix' => 'some text before'
        'class_postfix'=> 'some text after'
    ], ]; */
    private function attr($name = '', $data = '', $attr = [], $type = '') {
        $global = $this->form['global'];
        $name = trim(strtr($name, ['[' => '.', ']' => ''])); // used for cols[key]
        $col  = strlen($name) && isset($this->cols[$name]) ? $this->cols[$name] : [];
        $col['type'] = $type = $this->attrType($name, $col, $type);

        $is_cbox = in_array($type, ['checkbox', 'radio']);
        $is_sele = ($type == 'select');
        if (($is_cbox || $is_sele) && $attr && !isset($col['list']) && (!is_array($attr) || !isset($attr['list']))) {
            $attr = ['list' => $attr];
        } // init list before next block

        if ($attr) {
            if (is_array($attr)) {
                if (isset($attr['type'])) {
                    unset($attr['type']);
                }
                $col = array_merge($col, $attr);
            } else {
                $col[] = $attr;
            }
        }

        if (!isset($col['name'])) {
            if (strpos($name, '.')) {
                $arr = explode('.', $name);
                $col['name'] = array_shift($arr);
                $col['name'].= '[' . implode('][', $arr) . ']';
            } else {
                $col['name'] = $name;
            }
        } // eg name[x]

        if (!isset($col['id'])) {
            $col['id'] = $global['id_prefix'] . $name;
        }

        $label = $this->label($name, $col); // eg Name X
        if (!isset($col['label'])) {
            $col['label'] = $global['label'] == -1 ? -1 : (in_array($type, ['submit', 'reset', 'button']) ? '' : $label);
        }

        if ($is_sele || $is_cbox) {
            if (!isset($col['list'])) {
                $col['list'] = [1 => $label];
            } elseif (!is_array($col['list'])) {
                $col['list'] = [1 => $col['list']];
            }
            if ($is_sele) {
                if (!isset($col['defa'])) { //  && !in_array('multiple', $col)
                    $col['defa'] = ['' => $label];
                }
                if (!is_array($col['defa'])) {
                    $col['defa'] = ['' => $col['defa']];
                }
                $col['list'] = $col['defa'] + $col['list']; // either merge or + got bug -- fix later
            }
        }

        $placeholder = ['text', 'textarea', 'number', 'date', 'password'];
        if (!isset($col['placeholder']) && in_array($type, $placeholder) && $col['label'] == -1) {
            $col['placeholder'] = $label;
        }

        if ($this->attrAcs($col['acs'], $global['role'], $global['acs']) == 'R') {
            $col[] = 'disabled';
        }
        $col = $this->data($name, $data, $col);

        if ($col['label'] != -1) {
            $col['label_class']  = isset($col['label_class']) ? $col['label_class'] : '';
            $col['label_class'] .= ' ' . $global['label_class'];
            $col['label_class']  = trim($col['label_class']);
            $col['label_class'] .= isset($this->err[$name]) ? ' err' : '';
            $col['label_class']  = trim($col['label_class']);
        }

        $col['class']  = !isset($col['class'])     ? '' : $col['class'];
        $col['class'] .= !isset($this->err[$name]) ? '' : ' err';
        $col['class']  = trim($col['class']);
        $col['class'] .= ($type == 'form') ? '' : ' ' . $global['class'];
        $col['class']  = trim($col['class']);

        $this->indent = "\n" . ($is_cbox ? '' : '  ') . ($this->boot ? '  ' : '');
        $this->boot($col, $is_cbox);

        return $col;
    }

    private function attrAcs(&$acs, $role, $defa) {
        $acs = $acs ?: $defa;
        $role = is_array($role) ? $role : [$role];
        if (in_array('admin', $role)) {
            $acs = 'W'; // admin always W
        } elseif (!is_array($acs)) {
            $acs = in_array($acs, ['R', 'W']) ? $acs : 'N';
        } else {
            $acs = 'N';
            foreach ($acs as $r => $a) { // eg staff => W
                if (in_array($r, $role) && in_array($a, ['N', 'R', 'W'])) {
                    $acs = $a;
                }
            }
        }
        return $acs;
    }

    private function attrHtml($attr = [], $part = '') { // part = '' | box | label
        // generate HTML attributes eg class="..." id="..." style="..." etc
        $part = ($part == 'box' || $part == 'label') ? $part . '_' : '';
        if (!is_array($attr)) {
            return $part ? '' : $attr;
        }

        $html = '';
        $skip = ['type', 'label', 'list', 'token', 'sql', 'clean', 'valid', 'acs'];
        if (in_array($attr['type'], ['checkbox', 'radio', 'ajax'])) {
            $skip[] = 'name';
        } elseif (in_array($attr['type'], ['html', 'ajax', 'select', 'password'])) {
            $skip[] = 'value';
        }
        foreach ($attr as $k => $v) {
            if ($v && !is_array($v) && $v != -1 && $v != 'class_no_hidden' && strlen($v)) {
                if (($part && strpos($k, $part) === 0) || (!$part
                 && substr($k, 0, 6) != 'class_' && (!in_array($k, $skip) || !$k)
                 && substr($k, 0, 6) != 'label_' && substr($k, 0, 4) != 'box_'
                )) {
                    $k = $part ? substr($k, strlen($part)) : $k;
                    $html .= ' ' . (is_numeric($k) || !$k ? $v : $k . '="' . $this->html8($v) . '"');
                }
            }
        }
        return $html;
    }

    private function attrType($name = '', $attr = [], $type = '') {
        $type = $type ?: (
            isset($attr['type']) ? $attr['type'] : (
                isset($attr['ajax']) ? 'ajax' : (
                    isset($attr['list']) ? 'select' : (
                        in_array($name, ['form', 'end']) ? $name : 'text'
        ))));
        $alias= ['sele' => 'select', 'cbox' => 'checkbox', 'pass' => 'password'];
        return isset($alias[$type]) ? $alias[$type] : $type;
    }

    private function attrTypeMethod($name, &$attr, &$type) {
        if (is_array($attr) && isset($attr['type'])) {
            $type = $attr['type'];
        } elseif ($name && isset($this->cols[$name]) && isset($this->cols[$name]['type'])) {
            $type = $this->cols[$name]['type'];
        } elseif (in_array($name, ['form', 'end', 'html', 'boot1', 'boot2']) && (!$attr || !isset($attr['type']))) {
            $type = $name;
        } elseif (!is_array($attr) && !strpos($attr, ' ')) {
            $type = $attr;
            $attr = ''; // updated
        } else {
            $type = $this->attrType($name, $attr);
        }

        $methods = [
            'ajax', 'boot1', 'boot2', 'cbox', 'checkbox', 'end', 'html',
            'radio', 'sele', 'select', 'text', 'textarea', 'form',
        ];
        return in_array($type, $methods) ? $type : 'text';
    }

    private function boot($attr = [], $is_cbox = 0) {
        $attrHtml = $this->attrHtml($attr);
        if ($attr['label'] == -1 || $attr['type'] == 'hidden') {
            $this->boot = 0;
            if ($attrHtml && $attr['type'] == 'html') {
                $this->boot1 = "\n<span" . $attrHtml . ">\n  ";
                $this->boot2 = "\n</span>";
                $this->indent .= '  ';
            } else {
                $this->boot1 = "\n";
                $this->boot2 = '';
            }
            return;
        }

        $attr['box_class'] = isset($attr['box_class']) ? $attr['box_class'] : $this->form['global']['box_class'];
        $boxHtml = $this->attrHtml($attr, 'box');

        $labelHtml   = $this->attrHtml($attr, 'label');
        $this->boot  = 1;
        $this->boot1 = "\n<div$boxHtml>\n  <label$labelHtml>$attr[label]</label>\n  ";
        $this->boot2 = "\n</div>";
        if ($attr['type'] == 'html' || $is_cbox) {
            $this->boot1 .= "<div$attrHtml>\n    ";
            $this->boot2  = "\n  </div>" . $this->boot2;
            $this->indent.= '  ';
        }
    }

    private function cout($html = '', $attr = [], $boot1 = 1, $boot2 = 1) { // print html on screen or return
        if (is_array($attr) && isset($attr['class_prefix']) && !is_array($attr['class_prefix'])) {
            $html = $attr['class_prefix'] . $html;
        }
        if (is_array($attr) && isset($attr['class_postfix']) && !is_array($attr['class_postfix'])) {
            $html .= $attr['class_postfix'];
        }
        $html = ($boot1 ? $this->boot1 : '') . $html . ($boot2 ? $this->boot2 : '');
        if ($this->cout) {
            echo $html;
            return $this;
        } else {
            return $html;
        }
    }

    private function data($name = '', $data = '', $attr = []) {
        if ($data) {
            $attr['value'] = $data;
        } elseif (isset($attr['data']) && !isset($attr['value'])) {
            $attr['value'] = $attr['data']; // alias
            unset($attr['data']);
        }

        if (in_array($attr['type'], ['submit', 'reset', 'button'])) {
            $attr['value'] = isset($attr['value']) ? $attr['value'] : $name;
            $this->data[$name] = $attr['value'];
        } elseif (!in_array($attr['type'], ['form', 'end', 'html'])) {
            $data = $this->form['method'] == 'get' ? $_GET : $_POST;
            $keys = explode('.', $name);
            foreach ($keys as $k) {
                // next line need re-test eg some.kid if some set, ignore kid
                $data = isset($data[$k]) ? $data[$k] : null;
            }
            if ($data && $attr['type'] == 'select' && in_array('multiple', $attr)
                && is_array($data) && count($data) == 1 && isset($data[0]) && !$data[0]
            ) {
                $data = null; // reset multiple select val = [[]]
            }
            if ($data === null && isset($attr['value'])) {
                $data = $attr['value'];
            }

            if (!isset($attr['clean'])) {
                if ($attr['type'] == 'date') {
                    $attr['clean'] = 'date';
                } elseif ($attr['type'] == 'number') {
                    $attr['clean'] = 'int';
                } elseif (in_array($attr['type'], [])) {
                    $attr['clean'] = 'enum';
                } else {
                    $attr['clean'] = 'str,1'; // trim
                }
            }
            $attr['value'] = $attr['clean'] == 'enum' ? Data::cleanEnum($data, $attr['list']) : Data::clean($data, $attr['clean']);
            $this->dataValid($name, $attr);
            $this->data[$name] = $attr['value'];
        }
        return $attr;
    }

    private function dataValid($name, $attr) {
        /**
         * $attr[valid] = [
         *   'req'   => errMsg,
         *   'email' => errMsg,
         *   'pwd'   => errMsg,
         *   'email_unique'=>[uid, errMsg],
         *   'len'   => [min, max, errMsg],
         *   'minmax'=> [min, max, errMsg],
         *   'same'  => [val, isV, errMsg]
         * ];
         */
        $valid_when = strtoupper($this->form['global']['valid']);
        if ($attr['acs'] != 'W' || $valid_when == 'NONE') {
            return; // only validate Writeable cols
        }

        $rules = [];
        if ($valid_when == 'URL' || ($valid_when == 'GET' && $_GET) || $_POST) {
            $rules = isset($attr['valid']) ? $attr['valid'] : [];
            $rules = is_array($rules) ? $rules : [$rules];
            if ($this->form['global']['all_req'] && !isset($rules['req']) && !in_array('req', $rules)) {
                $rules[] = 'req';
            }
        }

        $cname = ($attr['label'] ?: ucwords(str_replace('_', ' ', $name))) . ' ';
        if (substr($cname, -4) == ' Id ') {
            $cname = substr($cname, 0, -3);
        }

        $data = $attr['value'];
        foreach ($rules as $rule => $param) {
            if (is_numeric($rule)) {
                $rule = $param;
                $param = [];
            }
            $param = is_array($param) ? $param : [$param];
            for ($i = 0; $i < 3; $i++) {
                if (!isset($param[$i])) {
                    $param[$i] = null;
                }
            }
            $err = '';
            if (!$data) {
                if ($this->form['global']['all_req'] || $rule == 'req') {
                    $err = $cname . ($param[0] ?: 'is required');
                }
            } elseif ($rule == 'email' && !Data::validEmail($data)) {
                $err = $cname . ($param[0] ?: 'is not valid');
            } elseif ($rule == 'email_unique' && !Data::validEmailUnique($data, $param[0])) {
                $err = $cname . ($param[1] ?: 'already exists');
            } elseif ($rule == 'len' && !Data::validLen($data, $param[0], $param[1])) {
                $err = !$param[0] ? 'max ' . $param[1] : (!$param[1] ? 'at least ' . $param[0] : 'between ' . $param[0] . ' and ' . $param[1]);
                $err = $cname . ($param[2] ?: 'must be') . $err . ' chars';
            } elseif ($rule == 'max' && !Data::validMax($data, $param[0])) {
                $err = $cname . ($param[1] ?: 'must be less than ' . $param[0]);
            } elseif ($rule == 'min' && !Data::validMin($data, $param[0])) {
                $err = $cname . ($param[1] ?: 'must be greater than ' . $param[0]);
            } elseif ($rule == 'minmax' && !Data::validMinMax($data, $param[0], $param[1])) {
                $err = $cname . ($param[2] ?: 'must be between ' . $param[0] . ' and ' . $param[1]);
            } elseif ($rule == 'pwd' && !Data::validPwd($data)) {
                $err = $cname . ($param[0] ?: 'is at least 8 chars and contains lower case and upper case chars, and numbers and special chars');
            } elseif ($rule == 'same' && !Data::validSame($data, $param[1] ? $param[0] : $this->cols[$param[0]]['value'])) {
                $err = $cname . ($param[2] ?: 'not same' . ($param[1] ? '' : ' as ' . $param[0]));
            }

            if ($err) {
                $this->err[$name][] = $err;
                if (!$this->form['global']['valid_all']) {
                    return; // stop at error
                }
            }
        }
    }

    private function html8($str = '') { return htmlspecialchars($str, ENT_QUOTES, 'UTF-8'); }

    private function label($name = '', $attr = []) {
        if (isset($attr['label']) && $attr['label'] != -1) {
            return $attr['label'];
        }

        $str = $name ?: $attr['name'];
        if (strlen($str) > 3 && substr($str, -3) == '_id') {
            $str = substr($str, 0, -3);
        }
        return ucwords(strtr($str, ['_' => ' ', '[' => ' ', ']' => '']));
    }

    private function selected($k, $val, $str = ' selected') {
        if ((is_array($val) && in_array((string)$k, $val)) || ($k == $val && !empty($k) && !empty($val))) {
            return $str;
        }
    }

    private function selectOption($attr, $k, $v, $indent = '') {
        return $this->indent . $indent
            . '<option value="' . $this->html8($k) . '"'
            . $this->selected($k, $attr['value'])
            . (isset($attr['class_' . $k]) ? ' class="' . $attr['class_' . $k] . '"' : '')
            . '>' . $v . '</option>';
    }

    private function token($cmd = '', $token = '') {
        if (-1 == $cmd) {
            return;
        }

        $ses_token = isset($_SESSION[$this->token]) ? $_SESSION[$this->token] : '';
        if ('valid' === $cmd) {
            if (!$token && isset($_POST[$this->token])) {
                $token = $_POST[$this->token]; // token only available for POST
            }
            unset($_SESSION[$this->token]);
            return ($token && $ses_token === $token);
        }

        if (!$ses_token) {
            $ses_token = $_SESSION[$this->token] = md5(rand() . microtime(true) . $_SERVER['REMOTE_ADDR']);
        }
        return "\n" . '<input type="hidden" name="' . $this->token . '" value="' . $ses_token . '">';
    }
}
