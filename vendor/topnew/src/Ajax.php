<?php
namespace Topnew;

/**
 * Topnew Ajax v201807 - Web Ajax library - Released on 2018.07.07
 * The MIT License (MIT) Copyright (c) 1998-2018, Topnew Geo, topnew.net
 *
 * It handles Topnew\Form() ajax call as well as css.class=ajax call
 * Triggled by js.ajax, default route /ajax (App\Controller\AjaxController)
 *
 * Individual public ajax methods should be kept in your App\Controller\AjaxController
 */
class Ajax
{
    protected $view = '';
    protected $ajax = [];
    protected $val  = null;
    protected $form = null;
    protected $attr = [];

    public function __construct($ajax = [], $val = null) {
        $this->ajax = $ajax ?: (isset($_POST['ajax']) ? json_decode(base64_decode($_POST['ajax']), 1) : []);
        $this->val  = $val  ?: (isset($_POST['val'])  ? $_POST['val'] : null);
        $this->form = new Form();
        if (isset($this->ajax['attr'])) {
            $this->attr = $this->ajax['attr'];
        }
        $this->attr['label'] = -1;
        $this->attr['class'] = trim($this->attr['class'] . ' cell-12');
    }

    public function home() {
        if (!isset($this->ajax['func']) || !$this->ajax['func']) {
            return $this->view;
        }
        if (!method_exists($this, $this->ajax['func'])) {
            // also need support eg func = SomeController @ someFunc -- upgrade later
            return $this->view;
        }
        return $this->{$this->ajax['func']}();
    }
}
