<?php
namespace Topnew;

/**
 * Topnew Pass v201812 - Released on 2018.12.12
 * The MIT License (MIT) Copyright (c) 1998-2018, Topnew Geo, topnew.net
 *
 * enc() dec() works with CryptoJS.AES as well (js without base64)
 * Login pwd check user.passA == pass(user, pwd256, salt) ## if !salt => generate a new user.passA
 * Get session current user id = uid()
 *
 * $user[uid, passA, created, email]
 * user.passA = salt (27chars) . hash (64chars) = hash[0] . hash(pwd256, hash[1])
 * log_login.passB = md5(random str) for remember-me cookies
 * cook.cms.passC = passC(passB + session + ip) for live session id
 *
 * senc() sdec() -- to generate rand short encrypted code (url safe) which is decryptable
 */

class Auth
{
    private static $app_key;
    private static $sess_uid; // current session visitor user id

    public static function acs($acs = '', $unit_id = 0) {
        // check if current sess_uid has access to eg $acs = 'DBA;Cust_Read', $unit_id = '2;3'
        $uid = self::uid();
        if (!$acs || !$uid) {
            return;
        }
        $acs = is_array($acs) ? $acs : explode(';', $acs);
        $db = make('db')->select(1)->from('per_access_user AS u')
            ->join(['a' => 'per_access'], 'a.acs_id = u.acs_id')
            ->where('u.uid', $uid)
            ->where('a.name', $acs);
        if ($unit_id) {
            $unit_id = is_array($unit_id) ? $unit_id : explode(';', $unit_id);
            $db = $db->where('u.unit_id', $unit_id);
        }
        return $db->limit(1)->val();
    }

    public static function cookie($uid = 0, $auto = 0, $log_login = '', $lid = 0) {
        $db    = make('db');
        $ip    = Data::ip();
        $now   = time();
        $auto  = ceil($auto); // php 7.1 warning if string
        $passB = md5($auto . self::salt(rand(), rand() . $uid) . $now . $ip);
        $osBr  = self::osBr($passB);
        $sesIp = self::sessIp($passB . $osBr . $ip);

        $log_login = $log_login ?: make('config')->get('log_login');

        $data = ['passB' => $passB, 'ip' => $ip, 'ts' => date('Y-m-d H:i:s'), 'auto' => $auto];
        $pcid = isset($_COOKIE['CID']) ? $_COOKIE['CID'] : 0;
        $pcid = explode('.', $pcid);
        $data['pcid'] = $pcid[0];
        if ($lid) {
            $db->where('id', $lid)->update($log_login, $data);
            // OR set old passB to null, and insert above line?
        } else {
            $data['uid']  = $uid;
            $lid = $db->insert($log_login, $data);
        }
        $passC = self::passC($passB . $auto . $uid, $lid . $osBr);
        $cook = $lid .'.'. $now .'.'. $auto .'.'. $sesIp .'.'. $passC;
        $exp = $now + 12 * 3600 + $auto * 24 * 3600;
        $db->where('id', $lid)->update($log_login, ['exp' => date('Y-m-d H:i:s', $exp)]);
        $cook = base64_encode($cook);
        $_COOKIE['CMS'] = $cook; // need this line to update cookie on current page
        setcookie('CMS', $cook, $exp, '/');
    }

    public static function dec($str, $key = '', $base64 = 1) {
        /**
         * Modified from https://github.com/brainfoolong/cryptojs-aes-php
         * It can talk to js.CryptoJSAesJson 3.1.2 only
         * Decrypt json{ct,iv,s} from self::enc or CryptoJS.AES.encrypt()
         */
        $arr = json_decode($base64 ? base64_decode($str) : $str, true);
        try {
            $salt = hex2bin($arr['s']);
            $iv   = hex2bin($arr['iv']);
        } catch(Exception $e) { return null; }
        $ct = base64_decode($arr['ct']);
        $key .= $salt;
        $md5 = [md5($key, true)];
        $result = $md5[0];
        for ($i = 1; $i < 3; $i++) {
            $md5[$i] = md5($md5[$i - 1] . $key, true);
            $result .= $md5[$i];
        }
        $key = substr($result, 0, 32);
        $dec = openssl_decrypt($ct, 'aes-256-cbc', $key, true, $iv);
        return json_decode($dec, true);
    }

    public static function enc($str, $key = '', $base64 = 1) {
        /**
         * Modified from https://github.com/brainfoolong/cryptojs-aes-php
         * Return json{crypted-text, iv, salt}
         * Can be decrypted by self::dec() or CryptoJS.AES.decrypt() v3.1.2
         */
        $salt = openssl_random_pseudo_bytes(8);
        $salted = '';
        $dx = '';
        while (strlen($salted) < 48) {
            $dx = md5($dx . $key . $salt, true);
            $salted .= $dx;
        }
        $key = substr($salted, 0, 32);
        $iv  = substr($salted, 32,16);
        $enc = openssl_encrypt(json_encode($str), 'aes-256-cbc', $key, true, $iv);
        $json= json_encode(['ct' => base64_encode($enc), 'iv' => bin2hex($iv), 's' => bin2hex($salt)]);
        return $base64 ? base64_encode($json) : $json;
    }

    public static function hash($user = [], $rand = 0) {
        // if $rand, a new rand salt will be generated -- used for generate a new salt for user.passA
        $arr = ['uid' => 0, 'passA' => '1.2', 'created' => date('Y-m-d H:i:s'), 'email' => 'a@b.c'];
        $user = is_array($user) ? $user : $arr;
        foreach ($arr as $k => $v) {
            if (!isset($user[$k])) {
                $user[$k] = $v;
            }
        }
        $arr = explode('.', $user['passA'], 2);
        $salt = $rand ? self::saltRand() : $arr[0];
        $hash = self::salt(self::appKey() . $salt, $salt . $user['email']);
        $hash = self::salt($salt . $user['uid'], $hash . $user['created']);
        return [$salt, $hash];
    }

    public static function logout() {
        setcookie('CMS', '', -1, '/');
        self::$sess_uid = 0;
        return 0;
    }

    public static function pass($user = [], $pwd256 = '', $rand = 0) {
        // pwd256 = js.sha256(sha256(p)+p) -- once login form submit, raw password no longer available anywhere
        // Generate user.passA based on userSalt.pwd256
        $salt = self::hash($user, $rand);
        $hash = self::salt(self::salt($salt[1], $pwd256), self::salt($pwd256, $salt[1]));
        // passA = salt (27chars) + dot + hash (64 chars) = 92 chars if salt generated by saltRand()
        return $salt[0] . '.' . $hash;
    }

    public static function pass256($pass = '') { return $pass ? hash('sha256', hash('sha256', $pass) . $pass) : ''; }

    public static function uid() {
        /**
         * Get $sess_uid (session user ID) according to cookie (CMS)
         * cook[CMS] = base64 of 0lid.1time.2auto.3sidIp.4passC
         * lid  = log_login.id to find log_login.uid = sess_uid
         * time = created time of the cookie
         * auto = days of expiration after above time
         * sidIp= hash of passB.osbr.lid.ip.session_id
         * passC= hash of passB.auto.uid.lid.osbr
         */
        if (isset(self::$sess_uid)) {
            return self::$sess_uid;
        }
        if (!isset($_COOKIE['CMS'])) {
            return self::logout();
        }

        $cook = explode('.', base64_decode($_COOKIE['CMS']), 5);
        for ($i = 0; $i < 5; $i++) {
            if (!isset($cook[$i])) {
                $cook[$i] = 0;
            }
        }
        // Fatal PHP Errors if string given - Is numeric condition added now - 07/08/2024
        $lid  = is_numeric($cook[0]) ? ceil($cook[0]) : $cook[0]; 
        $time = ceil($cook[1]);
        $auto = ceil($cook[2]); // $auto days of automatic login i.e. remember me on this computer etc
        $now  = time();
        $exp  = $time + 12 * 3600 + ceil($auto) * 24 * 3600; // expire in 12 hours by default (Changed from 4hrs on 15/01/2025)

        if ($lid < 1 || $time < 1 || $auto < 0 || $time > $now || $exp < $now || !$cook[3] || !$cook[4]) {
            return self::logout();
        }

        $log_login = make('config')->get('log_login');
        if (!$log_login) {
            return self::logout();
        }

        $log = make('db')->select('id, uid, auto, passB, ip')->from($log_login)->where('id', $lid)->row();

        if (!$log || !$log['id'] || !$log['uid'] || $auto != $log['auto'] || !$log['passB']) {
            return self::logout();
        }

        $passB = $log['passB'];
        $osBr  = self::osBr($passB);
        $sesIp = self::sessIp($passB . $osBr . $log['ip']);
        $passC = self::passC($passB . $auto . $log['uid'], $log['id'] . $osBr); // must excl sesIp

        if ($passC != $cook[4]) {
            return self::logout();
        }
        if ($sesIp == $cook[3]) {
            return $log['uid']; // same osbr + session_id + ip
        }
        if (!$auto) {
            return self::logout();
        }
        // The following: passC (cook[4]) same, but session (sesIp, cook[3]) not same, but use remember cookie
        // Regenerate a new cookie to make it more secure
        self::cookie($log['uid'], $auto, $log_login, $log['id']);
        return $log['uid'];
    }

    public static function senc($str = '', $key = '') {
        // URL safe decrytable short encryption vs openssl_encrypt
        // rand = rand4 + y1 + 365 day * 24 hour -- recycle every 10 years
        $rand = rand(1000, 9999) . substr(date('y'), -1) . str_pad(floor((time() - mktime(0,0,0,1,1,date('Y'))) / 3600), 4, 0, STR_PAD_LEFT);
        return str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(self::sencdec($rand . $str, $key)));
    }

    public static function sdec($str = '', $key = '', &$sec = '', $fmt = 'diff') {
        /**
         * @param sec = seconds diff from day-hour of last enc
         * @param sec = timestamp of last enc if @param fmt is date format
         */
        $str = base64_decode(str_replace(['-', '_'], ['+', '/'], $str));
        $str = self::sencdec($str, $key);
        $pre = substr($str, 0, 9); // RANDydddH
        if (! is_numeric($pre)) {
            return; // error
        }
        $age = substr(date('Y'), -1) - $pre[4];
        $age = $age < 0 ? $age + 10 : $age; // if negative then 10 years ago
        $ymd = substr($pre, 5) * 3600 + mktime(0,0,0,1,1, date('Y') - $age);
        if (is_numeric($pre) && substr(date('y', $ymd), -1) == $pre[4]) {
            $sec = ($fmt == 'diff' || !$fmt) ? time() - $ymd : date($fmt, $ymd);
        } else {
            $sec = 0; // err
        }
        return $sec ? substr($str, 9) : '';
    }

    private static function sencdec($str = '', $key = '') {
        $pre = substr($str, 0, 9);
        $str = substr($str, 9);
        $len = strlen($str);
        $key = md5($key . md5($key . $pre) . $pre);
        for ($i = 0; $i < $len; $i++) {
            $str[$i] = $str[$i] ^ $key[$i % 32];
        }
        return $pre . $str;
    }

    private static function appKey() {
        if (!isset(self::$app_key)) {
            self::$app_key = make('config')->get('app.key');
        }
        return self::$app_key;
    }

    private static function osBr($append = '') { return md5(self::appKey() . (isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '') . $append); }

    // hash512 log_login.passB and use for cookie[CMS].passC
    private static function passC($passB = '', $append = '') { return hash('sha512', self::salt($passB, $append)); }

    private static function salt($a = '', $b = '') {
        // salt 2 str and produce a hash256
        $a = hash('sha256', $a);
        $b = hash('sha256', $b ?: self::saltRand($a));
        return hash('sha256', $a[0] > $b[0] ? $b . $a : $a . $b . ($a[0] == $b[0] ? '' : $a));
    }

    private static function saltRand($salt = '') {
        // generate a random salt based on ymd Hour + rand
        $time = str_pad(date('z') * date('H') + date('H'), 4, 0, STR_PAD_LEFT);
        $rand = substr(date('y'), -1) . $time . str_pad(rand(0, 9999), 4, 0, STR_PAD_LEFT); // yd**hrand total 9digit
        $hash = base64_encode(md5($rand . rand() . self::appKey() . time() . $salt, true)); // base64 of md5 will be 22char + ==
        return $time . substr($hash, 0, 22); // 5+22 = 27chars
        //return $rand . md5($rand . rand() . self::appKey() . time() . $salt); // 9+32=41chars
    }

    private static function sessIp($append = '') { return md5(self::appKey() . session_id() . Data::ip() . $append); }
}
