<?php
namespace Topnew;

/**
 * Topnew Config v201805 - Released on 2018.05.05
 * The MIT License (MIT) Copyright (c) 1998-2018, Topnew Geo, topnew.net
 *
 * It parse /config.php into $config, the config file.php can contain multple lines of comment like this or
 * single line comment start by ## or -- or // at any place of a line
 * If one line contains multiple ## abc ## xyz : the later comment will be removed
 * Basically no need quote a val, eg abc = 123
 * However you need to quote "null", "true", "false" if they are string.
 * you can do multiple dimentional vars eg : db.user = user_name etc
 */

class Config
{
    private $data; // no need static

    public function __construct($file = '') {
        if (!$file && isset($_SERVER['DOCUMENT_ROOT'])) {
            $file = $_SERVER['DOCUMENT_ROOT'] . DIRECTORY_SEPARATOR . 'config.php';
        }
        if (!$file || !file_exists($file)) {
            return;
        }
        $data = file_get_contents($file);
        // Remove multiple line comments
        $data = preg_replace('!/\*.*?\*/!s', '', $data);
        $arr = explode(PHP_EOL, $data);
        foreach ($arr as $line) {
            $line = trim($line);
            if ($line) { // Remove empty line
                if (!in_array(substr($line, 0, 2), ['--', '##', '//'])) {
                    $arr2 = explode('=', $line, 2);
                    $k = trim($arr2[0]);
                    if ($k && isset($arr2[1])) { // Remove some funny line start with =
                        $v = trim($arr2[1]);
                        if ($this->isQuoted($v)) {
                            $v = trim(substr($v, 1, -1));
                        } else {
                            $v = $this->removeComment($this->removeComment($this->removeComment($v, '//'), '##'), '--');
                            $v = $this->isQuoted($v) ? trim(substr($v, 1, -1)) : $this->nullTrueFalse($v);
                        }
                        $this->set($k, $v);
                    }
                } // Remove single line comments
            }
        }
    }

    public function del($key) {
        $key = explode('.', $key);
        $end = array_pop($key);
        $tmp = &$this->data;
        foreach($key as $k) {
            if (is_array($tmp[$k])) {
                $tmp = &$tmp[$k];
            } else {
                return; // Error -- key not exists
            }
        }
        unset($tmp[$end]);
    }

    public function get($key = '', $default = null) {
        $key = explode('.', $key);
        $tmp = &$this->data;
        foreach ($key as $k) {
            if (!isset($tmp[$k])) {
                return $default;
            }
            $tmp = &$tmp[$k];
        }
        return $tmp;
    }

    public function set($key = '', $val = null) {
        $key = explode('.', $key);
        $tmp = &$this->data;
        foreach ($key as $k) {
            $tmp = &$tmp[$k];
        }
        $tmp = $val;
    }

    private function isQuoted($txt) {
        return ($txt && $txt[0] == substr($txt, -1) && strlen($txt) > 2 && in_array($txt[0], ['"', "'"]));
    }

    private function nullTrueFalse($txt) {
        $V = strtoupper($txt);
        if ($V == 'NULL') {
            return null;
        } elseif ($V == 'TRUE') {
            return 1;
        } elseif ($V == 'FALSE') {
            return 0;
        }
        return $txt;
    }

    private function removeComment($txt = '', $comment = '##') {
        $arr = explode($comment, $txt);
        if (isset($arr[1])) {
            array_pop($arr);
        }
        return trim(implode($comment, $arr));
    }
}
