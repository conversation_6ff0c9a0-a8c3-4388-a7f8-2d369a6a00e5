<?php
namespace Topnew;

/**
 * Topnew Data v 2018.11.11 - Form Data Clean and Validator
 * The MIT License (MIT) Copyright (c) 1998-2018, Topnew Geo, topnew.net
 *
 * It contains data clean() and data valid(), work closely with Topnew\Form()
 */
class Data
{
    public static function ip($int = 0) {
        // Return visitors IP as string | int
        $arr = ['HTTP_X_FORWARDED_FOR', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        $ip = '';
        if ($_SERVER) {
            foreach ($arr as $k) {
                if (!$ip && isset($_SERVER[$k])) {
                    $ip = $_SERVER[$k];
                }
            }
        }
        return $int ? sprintf('%u', ip2long($ip)) : $ip;
    }

    public static function dict($str = '', $val = null) {
        if (is_null($val)) {
            return $str;
        }
        if (!is_array($val)) {
            return str_replace('%0%', $val, $str);
        }
        foreach ($val as $k => $v) {
            $fm[] = '%' . $k . '%';
            $to[] = $v;
        }
        return str_replace($fm, $to, $str);
    }

    public static function swap(&$x, &$y) { $t = $x; $x = $y; $y = $t; }

    public static function botExit($honeypot = '', $get = 0) {
        // common honey will be: name,email etc
        if (!$honeypot) {
            return;
        }
        $honeypot = explode(',', $honeypot);
        $honeypot = is_array($honeypot) ? $honeypot : [$honeypot];
        foreach ($honeypot as $pot) {
            $pot = trim($pot);
            if ((isset($_POST[$pot]) && $_POST[$pot]) || (isset($_GET[$pot]) && $_GET[$pot]) ) {
                return 1;
                //header('location:/');
                //exit; // bot honeypot filled in the form found
            }
        }
        if ($get) {
            return;
        }
        if (!isset($_SERVER['REQUEST_METHOD']) || $_SERVER['REQUEST_METHOD'] != 'POST') {
            return 1;
            //header('location:/');
            //exit; // only post allowed
        }
    }

    public static function clean($data = null, $typ = '') {
        // PART I - no typ - $data is an array of col => typ
        if (!$typ) {
            if (!isset($data)) {
                return;
            }
            if (!is_array($data)) {
                $data = [$data];
            }
            $arr = [];
            foreach ($data as $k => $typ) {
                if (is_numeric($k)) {
                    $k = $typ;
                    $typ = 'str';
                }
                $v = isset($_POST[$k]) ? $_POST[$k] : (isset($_GET[$k]) ? $_GET[$k] : null);
                $arr[$k] = self::clean($v, $typ);
            }
            return $arr;
        }

        /**
         * PART II - $typ can be any of following or array of following:
         * Bool
         * Date / out = Y-m-d / in =
         * DateTime / out / in
         * Digit
         * Enum / enum
         * Int
         * MinMax / min / max / default
         * Num / dec
         * Str /trim / tag / slash
         * Time / out
         */
        if (is_array($typ)) {
            $arr = [];
            foreach ($typ as $k => $typ2) {
                if (is_numeric($k)) {
                    $k = $typ2;
                    $typ2 = 'str';
                }
                $v = is_array($data) && isset($data[$k]) ? $data[$k] : null;
                $arr[$k] = self::clean($v, $typ2);
            }
            return $arr;
        }
        $typs = explode(',', $typ); // eg num,2 | minmax,0,99,50 etc
        for ($i = 1; $i < 4; $i++) {
            if (!isset($typs[$i])) {
                $typs[$i] = null;
            }
        }
        $typ = strtolower($typs[0]);

        // PART III - clean data
        if ($typ == 'bool') {
            return self::cleanBool($data);
        } elseif ($typ == 'digit') {
            return self::cleanDigit($data);
        } elseif ($typ == 'enum') {
            return self::cleanEnum($data, $typs[1]);
        } elseif ($typ == 'int') {
            return self::cleanInt($data);
        } elseif ($typ == 'minmax') {
            return self::cleanMinMax($data, $typs[1], $typs[2], $typs[3]);
        } elseif ($typ == 'num') {
            return self::cleanNum($data, $typs[1]);
        } elseif ($typ == 'str') {
            return self::cleanStr($data, $typs[1], $typs[2], $typs[3]);
        } elseif ($typ == 'date') {
            return self::cleanDate($data, $typs[1], $typs[2]);
        } elseif ($typ == 'datetime') {
            return self::cleanDatetime($data, $typs[1], $typs[2]);
        } elseif ($typ == 'time') {
            return self::cleanTime($data, $typs[1]);
        } elseif ($typ == 'keyw') {
            return self::cleanKeyw($data);
        }
        return $data;
    }

    public static function cleanBool($x) {
        if (!is_array($x)) {
            return ($x ? 1 : 0);
        }
        foreach ($x as $k => $v) {
            $x[$k] = self::cleanBool($v);
        }
        return $x;
    }

    public static function cleanDate($x, $out = 'Y-m-d', $in = 'YMD') { // YMD DMY MDY
        if ($x) {
            if (!is_array($x)) {
                $str = '';
                $arr = explode(' ', $x);
                $arr = explode('-', str_replace(['/', '.'], '-', $arr[0]));
                $Y   = ($arr[0]) ? ceil($arr[0]) : 0;
                $m   = ($arr[1]) ? ceil($arr[1]) : 0;
                $d   = ($arr[2]) ? ceil($arr[2]) : 0;
                if (!$Y && !$m && !$d) {
                    return;
                }
                $in = $in ? strtoupper($in) : '';
                if (!$in) {
                    $in = 'YMD';
                }
                if ($in != 'YMD') {
                    self::swap($Y, $d);
                }
                if ($in == 'MDY') {
                    self::swap($m, $d);
                } // becomes DMY
                if ($Y > -1 && $Y < 100) {
                    $cent = substr(date('Y'), 0, 2);
                    $Y = ($Y < 50 || date('y') > 49 ? $cent : $cent - 1) . str_pad($Y, 2, 0, STR_PAD_LEFT);
                }
                if ($m < 1 || $m > 13) {
                    $m = 1;
                }
                if ($d < 1 || $d > 31 || !checkdate($m, $d, $Y)) {
                    $d = 1;
                }
                $n   = $m;
                $m   = str_pad($m, 2, 0, STR_PAD_LEFT);
                $j   = $d;
                $d   = str_pad($d, 2, 0, STR_PAD_LEFT);
                $y   = substr($Y, 2);
                $mon = [1 => 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                $M   = $mon[$n];
                $arrStr  = str_split($out ?: 'Y-m-d');
                $arrDate = ['Y', 'y', 'M', 'm', 'n', 'd', 'j'];
                foreach ($arrStr as $s) {
                    $str .= (in_array($s, $arrDate) ? $$s : $s);
                }
                return $str;
            }
            foreach ($x as $k => $v) {
                $x[$k] = self::cleanDate($v, $out, $in);
            }
        }
        return $x;
    }

    public static function cleanDatetime($x, $out = 'Y-m-d H:i:s', $in = 'YMD') {
        if (!is_array($x)) {
            $date = explode(' ', $x, 2);
            $outs = explode(' ', $out ?: 'Y-m-d H:i:s', 2);
            $ymd  = self::cleanDate($date[0], $outs[0], strtoupper($in));
            if (!$ymd) {
                return;
            }
            return $ymd . ' ' . self::cleanTime(isset($date[1]) ? $date[1] : '', $outs[1]);
        }
        foreach ($x as $k => $v) {
            $x[$k] = self::cleanDatetime($v, $out, $in);
        }
        return $value;
    }

    public static function cleanDigit($x) {
        if (!is_array($x)) {
            return preg_replace('/[^0-9]/', '', $x);
        }
        foreach ($x as $k => $v) {
            $x[$k] = self::cleanDigit($v);
        }
        return $x;
    }

    public static function cleanEnum($x, $enum) { // enum = default|a|b|c|d|... or array
        if (!is_array($enum)) {
            $enum = explode('|', $enum);
        }
        if (!is_array($x)) {
            $x = trim($x);
            return in_array($x, $enum) ? $x : $enum[0];
        }
        foreach ($x as $k => $v) {
            $x[$k] = self::cleanEnum($v, $enum);
        }
        return $x;
    }

    public static function cleanInt($x) {
        if($x) {
            if (!is_array($x)) {
                return ceil((int)str_replace(',', '', $x));
            }
            foreach ($x as $k => $v) {
                $x[$k] = self::cleanInt($v);
            }
        }
        return $x;
    }

    public static function cleanKeyw($keyw = '') {
        /**
         * ---- current not support nested array -------
         *
         * any word : one or  two | one | two | one  two
         * all word : one and two | one + two | one +two
         * exact wd : "on two"
         * not word : one not tow | one - two | one -two
         *
         * return '' or [keyw, AND, NOT, OR]
         */
        $search = ['', 'AND'=>[], 'OR'=>[], 'NOT'=>[]];
        if (!$keyw) {
            return $search;
        }
        $arr = explode('"', $keyw);
        $kw = [];
        foreach ($arr as $k => $v) {
            $v = trim($v);
            if ($k % 2) {
                $kw[] = $v; // inside quotes "exact match"
            } else {
                $arr2 = explode(' ', $v);
                $kw = array_merge($kw, $arr2);
            }
        }
        $op_AND = ['AND', '+']; // url auto change + to space, please use and instead
        $op_NOT = ['NOT', '-', '!'];
        $op_OR  = ['OR',  '|'];
        $op = 'AND'; // first keyw is AND
        foreach ($kw as $k) {
            if ($k) {
                $KK = strtoupper($k);
                if (in_array($KK, $op_AND)) {
                    $op = 'AND';
                } elseif (in_array($KK, $op_NOT)) {
                    $op = 'NOT';
                } elseif (in_array($KK, $op_OR)) {
                    $op = 'OR';
                } else {
                    if ($k[0] == '+') {
                        $op = 'AND';
                        $k = substr($k, 1);
                    } elseif ($k[0] == '!' || $k[0] == '-') {
                        $op = 'NOT';
                        $k = substr($k, 1);
                    } elseif ($k[0] == '|') {
                        $op = 'OR';
                        $k = substr($k, 1);
                    }
                    $k = trim($k);
                    if (strlen($k) > 2) {
                        $search[$op][] = $k;
                    }
                    $op = 'OR'; // default op to OR [space]
                }
            }
        }
        if (!$search) {
            return;
        }

        // remove duplicated AND -- keep the long keyw
        foreach ($search['AND'] as $k1 => $v1) {
            foreach ($search['AND'] as $k2 => $v2) {
                if ($k1 != $k2 && strpos($v2, $v1) !== false) {
                    unset($search['AND'][$k1]);
                }
            }
        }

        // remove duplicated NOT -- keep the short keyw
        foreach ($search['NOT'] as $k1 => $v1) {
            foreach ($search['NOT'] as $k2 => $v2) {
                if ($k1 != $k2 && strpos($v2, $v1) !== false) {
                    unset($search['NOT'][$k2]);
                }
            }
        }

        // if found in not, remove and
        if ($search['NOT'] && $search['AND']) {
            foreach ($search['NOT'] as $k1 => $v1) {
                foreach ($search['AND'] as $k2 => $v2) {
                    if (strpos($v2, $v1) !== false) {
                        unset($search['AND'][$k2]);
                    }
                }
            }
        }

        // if found in or, remove not
        if ($search['OR'] && $search['NOT']) {
            foreach ($search['OR'] as $k1 => $v1) {
                foreach ($search['NOT'] as $k2 => $v2) {
                    if (strpos($v2, $v1) !== false) {
                        unset($search['NOT'][$k1]);
                    }
                }
            }
        }

        if ($search['OR'] && count($search['AND']) == 1) {
            array_unshift($search['OR'], array_pop($search['AND']));
        }

        // remove duplicated OR -- keep the short keyw
        foreach ($search['OR'] as $k1 => $v1) {
            foreach ($search['OR'] as $k2 => $v2) {
                if ($k1 != $k2 && strpos($v2, $v1) !== false) {
                    unset($search['OR'][$k2]);
                }
            }
        }

        $search[0] = '';
        foreach ($search['AND'] as $k) {
            $search[0] .= ' AND ' . (strpos($k, ' ') ? '"' . $k . '"' : $k);
        }
        $search[0] = $search[0] ? substr($search[0], 5) : '';
        foreach ($search['NOT'] as $k) {
            $search[0] .= ' -' . (strpos($k, ' ') ? '"' . $k . '"' : $k);
        }
        foreach ($search['OR'] as $k) {
            $search[0] .= ' ' . (strpos($k, ' ') ? '"' . $k . '"' : $k);
        }
        $search[0] = trim($search[0]);

        return $search;
    }

    public static function cleanMinMax($x, $min = 0, $max = 9999, $default = '') {
        if (!is_array($x)) {
            if ($x < $min || $x > $max) {
                return (strlen($default) ? $default : $min);
            }
            return $x;
        }
        foreach ($x as $k => $v) {
            $x[$k] = self::cleanMinMax($v, $min, $max, $default);
        }
        return $x;
    }

    public static function cleanNum($x, $dec = 2) {
        if (!is_array($x)) {
            return round((float)str_replace(',', '', $x), $dec);
        }
        foreach ($x as $k => $v) {
            $x[$k] = self::cleanNum($v, $dec);
        }
        return $x;
    }

    public static function cleanStr($x, $trim = 1, $tag = 0, $slash = 0) { // tag==1,0 or <p><b>...
        if($x) {
            if (!is_array($x)) {
                if ($tag) {
                    $x = strip_tags($x, ($tag == 1 ? '' : $tag));
                }
                if ($slash == 'sql') { // heck free + no slash + no quotes
                    $x = str_replace(['#', '--', ';', '/*', '\'', '"', '\\'], '', $x);
                } elseif ($slash) {
                    $x = stripslashes($x);
                }
                if ('r' == $trim || 'R' == $trim) {
                    $x = rtrim($x);
                } elseif ('l' == $trim || 'L' == $trim) {
                    $x = ltrim($x);
                } elseif ($trim || $trim === null) {
                    $x = trim($x);
                }
                return $x;
            }
            foreach ($x as $k => $v) {
                $x[$k] = self::cleanStr($v, $trim, $tag, $slash);
            }
        }
        return $x;
    }

    public static function cleanTime($x, $out = 'H:i:s') {
        if($x) {
            if (!is_array($x)) {
                $arr = explode(':', substr($x, 0, 8), 3);
                $H   = ceil(isset($arr[0]) ? $arr[0] : '00');
                $i   = ceil(isset($arr[1]) ? $arr[1] : '00');
                $s   = ceil(isset($arr[2]) ? $arr[2] : '00');
                if ($H < 13 && strtolower(substr(trim($x), -2)) == 'pm') {
                    $H += 12;
                }
                return date(($out ?: 'H:i:s'), strtotime(date("Y-m-d $H:$i:$s")));
            }
            foreach ($x as $k => $v) {
                $x[$k] = self::cleanTime($v, $out);
            }
        }
        return $x;
    }

    public static function valid($data, $rules = '') { }

    public static function validEmail($email = '') {
        return filter_var($email, FILTER_VALIDATE_EMAIL);
    }

    /**
     * id uid_of_email
     * 1  0 unique
     * 1  1 unique
     * 1  2 already existing
     * 0  0 unique
     * 0  1 already existing
     */
    public static function validEmailUnique($email = '', $id = 0) {
        if (!$email) {
            return 1;//no email always unique?
        }
        // next line need change to actually system design of user table
        // maybe need global helper func eg getUserIdByEmail()
        $uid = make('db')->val('SELECT uid FROM per_user WHERE email = :email', [':email' => $email]);
        $id = ceil($id);
        return ($id == $uid || ($id && !$uid));
    }

    public static function validLen($x, $min = null, $max = null) {
        return self::validMinMax(strlen($x), $min, $max);
    }

    public static function validMax($x, $max = null) {
        return self::validMinMax($x, null, $max);
    }

    public static function validMin($x, $min = null) {
        return self::validMinMax($x, $min);
    }

    public static function validMinMax($x, $min = null, $max = null) {
        if (isset($min) && $x < $min) {
            return;
        }
        if (isset($max) && $x > $max) {
            return;
        }
        return 1; // valid
    }

    public static function validPwd($pwd = '') {
        return strlen($pwd) > 5 && preg_match('/[A-Z]/',$pwd) && preg_match('/[a-z]/',$pwd) && preg_match('/[0-9]/',$pwd);
    }

    public static function validSame($x, $b = '') { return $x == $b; }
}
