<?php
namespace Topnew\Page;

use Topnew\Data;

trait DirTrait
{
    private function dir($dir, $uploaded) {
        $files = $this->dirScan($this->root . $dir);
        $arr_f = [];
        $today = date('Y-m-d');
        foreach ($files as $f => $is_dir) {
            if ($is_dir) {
                $icon = 'folder';
                $ext = '';
            } else {
                $arr = explode('.', $f);
                $ext = strtolower(end($arr));
                $icon = $this->fileIcon($ext);
            }
            $file = $this->root . $dir . '/' . $f;
            $size = $this->fileSize($file, $is_dir, $kb);
            $mt = date('Y-m-d H:i', filemtime($file));
            $mt_color = in_array($f, $uploaded) ? 'red'
                : (substr($mt, 0, 10) == $today ? 'green'
                   : (substr($mt, 0, 9) == substr($today, 0, 9) ? 'blue'
                      : (substr($mt, 0, 7) == date('Y-m') ? '' : 'grey')
                   )
                );
            $arr_f[] = [
                'Modified' => $mt,
                'Type' => $ext,
                'icon' => $icon,
                'size_str' => $size,
                'is_dir' => $is_dir,
                'mt_color' => $mt_color,
                'RWX' => $this->fileRwx($file),
                'Owner' => $this->fileUserName($file, 1),
                'Group' => $this->fileUserName($file),
                'Size' => $kb,
                'Name' => $f,
                'can_edit' => !$is_dir && $this->fileCanEdit($ext),
                'img_info' => ($icon == 'img' && $kb) ? getimagesize($this->root . $dir . '/' . $f) : [],
            ];
        }

        $sort = Data::clean('s')['s'];
        if ($arr_f) {
            $s1 = $sort ? substr($sort, 0 , 1) : $sort;
            $s2 = $sort ? substr($sort, 1) == 'd' ? 'd' : '' : $sort;
            if (!in_array($s1, array_keys($this->cols))) {
                $s1 = 'n'; // name type size rwx mtime owner group
            }
            $name = array_column($arr_f, 'Name');
            $dirs = array_column($arr_f, 'is_dir');
            $cols = array_column($arr_f, $this->cols[$s1]);
            array_multisort($dirs, SORT_DESC, $cols, ($s2 ? SORT_DESC : SORT_ASC), $name, ($s2 ? SORT_DESC : SORT_ASC), $arr_f);
            $sort = $s1 . $s2;
        }
        $this->set('sort', $sort);
        $this->set('files', $arr_f);
    }

    private function dirAdd($dir) {
        $folder = Data::clean('folder')['folder'];
        $folder = preg_replace('/[^a-zA-Z0-9_\-]/', '', $folder);
        if (!strlen($folder)) {
            return;
        }
        mkdir($this->root . $dir . '/' . $folder);
        $this->set('msg', 'Added folder: ' . $folder);
    }

    private function dirClean() {
        $dir = Data::clean('f')['f'];
        $dir = $dir ? trim($dir, " /\t\n\r\0\x0B") : $dir;
        return (!$dir || !file_exists($this->root . $dir)) ? '' : $dir;
    }

    private function dirNav($dir) {
        $arr = explode('/', $dir);
        $url = '';
        $nav = [];
        foreach ($arr as $f) {
            $url .= '/' . $f;
            $nav[$url] = $f;
        }
        $this->set('nav', $nav);
    }

    private function dirScan($dir) {
        $arr = [];
        $files = scandir($dir);
        foreach ($files as $f) {
            if ($f != '.' && $f != '..') {
                $arr[$f] = is_dir($dir . '/' . $f);
            }
        }
        return $arr;
    }
}
