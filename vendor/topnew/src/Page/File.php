<?php
namespace Topnew\Page;

use Topnew\Data;

// TODO: zip, unzip, link, chmod, search, copy, file info, edit file, tree menu

class File
{
    use DirTrait;
    use FileTrait;
    private $root = ''; // root of file
    private $web  = ''; // root of web
    private $cols  = [];
    private $data  = [];

    public function __construct($init = []) {
        $this->init($init);
        if (isset($_GET['prev'])) {
            return $this->filePreview($_GET['prev']);
        }

        $dir = $this->dirClean();
        $cmd = Data::clean('cmd')['cmd'];
        $uploaded = [];
        $this->set('msg', '');
        if ($cmd == 'Create Folder') {
            $this->dirAdd($dir);
        } elseif (in_array($cmd, ['Copy', 'Rename'])) {
            $err = $this->fileMove($cmd);
        } elseif ($cmd == 'Add File') {
            $err = $this->fileNew($dir);
        } elseif ($cmd == 'Upload' && $_FILES) {
            $uploaded = $this->fileUpload($dir);
        }
        $this->fileDel($dir);
        $this->set('dir', $dir);
        $this->dirNav($dir);

        if (!$dir || is_dir($this->root . $dir)) {
            $this->set('is_dir', 1);
            $this->dir($dir, $uploaded);
        } else {
            $this->file($dir, $cmd);
        }
    }

    public function get($k) { return isset($this->data[$k]) ? $this->data[$k] : null; }
    public function set($k, $v = null) { $this->data[$k] = $v; }

    private function init($init) {
        $this->root = isset($init['root']) && $init['root'] && is_dir($init['root'])
            ? $init['root'] : $_SERVER['DOCUMENT_ROOT'] . '/';
        $this->web  = isset($init['web']) ? $init['web'] : '/';
        $this->cols = [
            'n' => 'Name', 't' => 'Type', 's' => 'Size', 'r' => 'RWX',
            'm' => 'Modified', 'o' => 'Owner', 'g' => 'Group',
        ];
        $this->set('cols', $this->cols);
        $this->set('web', $this->web);
    }
}
