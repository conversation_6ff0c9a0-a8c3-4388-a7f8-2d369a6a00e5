<?php

namespace Topnew\Page;

use Topnew\Data;

trait FileTrait
{
    private function file($file, $cmd) {
        $arr = explode('.', $file);
        $ext = array_pop($arr);
        if (!$this->fileCanEdit($ext)) {
            return;
        }
        if ($cmd == 'Save File') {
            $txt = Data::clean('txt')['txt'];
            $fh = fopen($this->root . $file, 'w');
            if ($fh) {
                fwrite($fh, $txt);
                fclose($fh);
            }
        } else {
            $txt = file_get_contents($this->root . $file);
        }
        $this->set('txt', $txt);
    }

    private function fileClean($file) {
        $file = str_replace('../', '/', trim($file, '/ '));
        return str_replace(['/////', '////', '///', '//'], '/', $file);
    }

    private function fileCanEdit($ext) {
        return $ext && in_array($ext, [
            'css', 'csv', 'htm', 'html', 'js', 'json', 'php', 'sql', 'tpl', 'txt', 'xml',
            'md',
        ]);
    }

    private function fileCopy($old, $new) {
        if (is_file($old)) {
            if (is_dir($new)) {
                $new .= '/' . basename($old);
            }
            $new_f = substr($new, strlen($this->root));
            if (is_file($new)) {
                $this->data['msg'] .= '<b class="red">Copy FAILED</b> - File exists: ' . $new_f . '<br>';
                return 3;
            } elseif (!copy($old, $new)) {
                $this->data['msg'] .= '<b class="red">Copy FAILED</b> - ' . $new_f . '<br>';
                return 4;
            }
            $this->data['msg'] .= 'File Copied - ' . $new_f;
            chmod($new, 0664);
        } elseif (is_dir($old)) {
            if (strtoupper(substr($new . '/', 0, strlen($old) + 1)) == strtoupper($old . '/')) {
                $this->data['msg'] .= '<i class="red">Do not copy to self</i>. Please copy to other folder than move<br>';
                return 5;
            }
            if (!is_dir($new)) {
                mkdir($new, 0775);
            }
            $fh = opendir($old . '/');
            $err = 0;
            while ($file = readdir($fh)) {
                if ($file != '.' && $file != '..') {
                    $err += $this->fileCopy($old . '/' . $file, $new . '/' . $file);
                }
            }
            closedir($fh);
            return $err;
        }
    }

    private function fileDel($dir) {
        $del = Data::clean('del')['del'];
        if ($del && !strlen($del)) {
            return;
        }
        $files = $del ? (($del == '*all*') ? scandir($this->root . $dir) : explode('*', $del)) : [];
        foreach ($files as $f) {
            if (!in_array($f, ['.', '..', ''])) {
                $file = $this->root . $dir . '/' . $f;
                $ok = is_dir($file) ? @rmdir($file) : @unlink($file);
                $this->data['msg'] .= 'Delete <b>' . $f . '</b> - ' . ($ok ? 'OK' : '<i class="red">FAILED</i>') . '<br>';
            }
        }
    }

    private function fileIcon($ext) {
        if (in_array($ext, [
            'folder',
            'css', 'doc', 'dvd', 'eml', 'exe', 'fla', 'htm', 'mdb',
            'mp3', 'pdf', 'php', 'ppt', 'txt', 'xls', 'xml', 'zip',
        ])) {
            return $ext;
        }
        $icons = [
            'tar' => 'zip', 'gz'  => 'zip',
            'jpg' => 'img', 'gif' => 'img', 'png' => 'img', 'tiff' => 'img', 'bmp' => 'img', 'ico' => 'img',
            'wav' => 'mp3', 'ogg' => 'mp3', 'wma' => 'mp3', 'ra' => 'mp3',
            'mp4' => 'dvd',
            'html'=> 'htm', 'xhtml'=> 'htm','shtml'=> 'htm',
            'tpl' => 'php',
            'sql' => 'txt', 'js'  => 'txt',
            'docx'=> 'doc',
            'xlsx'=> 'xls',
        ];
        return $ext && isset($icons[$ext]) ? $icons[$ext] : 'xxx';
    }

    private function fileMove($cmd) {
        $data = Data::clean(['old_file', 'new_file']);
        $old = $this->fileClean($data['old_file']);
        $new = $this->fileClean($data['new_file']);
        if (!$old || !$new || $old == $new) {
            return;
        }
        $OLD = $this->root . $old;
        $NEW = $this->root . $new;
        if ($cmd == 'Copy') {
            return $this->fileCopy($OLD, $NEW);
        }

        if (file_exists($NEW)) {
            $this->set('msg', '<b class="red">Rename FAILED</b> - File exists: ' . $new);
            return 1;
        } elseif (@rename($OLD, $NEW)) {
            $this->set('msg', $old . ' - renamed to - ' . $new);
        } else {
            $this->set('msg', '<b class="red">Rename FAILED</b> - ' . $old);
            return 2;
        }
    }

    private function fileNew($dir) {
        $file = Data::clean('file')['file'];
        $file = preg_replace('/[^a-zA-Z0-9_\-\.]/', '', $file);
        if (!strlen($file)) {
            return;
        }
        $FILE = $this->root . ($dir ? $dir . '/' : '') . $file;
        if (file_exists($FILE)) {
            $this->set('msg', '<b class="red">File exists</b>: ' . $file);
            return 8;
        } elseif (!@fopen($FILE, 'x')) {
            $this->set('msg', '<b class="red">Fail to created file</b>: ' . $file);
            return 9;
        } else {
            $this->set('msg', 'File created: ' . $file);
        }
    }

    private function filePreview($dir) {
        $this->set('ajax', 1);
        $dir = $this->root . $dir;
        $arr = explode('.', $dir);
        if ($this->fileCanEdit(array_pop($arr))) {
            $this->set('txt', str_replace('</textarea>', '&lt;/textarea>', file_get_contents($dir)));
        }
    }

    private function fileRwx($file) {
        $perms = fileperms($file);
        if (($perms & 0xC000) == 0xC000) {
            $info = 's';//socket
        } elseif (($perms & 0xA000) == 0xA000) {
            $info = 'l';//symbolic link
        } elseif (($perms & 0x8000) == 0x8000) {
            $info = '-';//regular
        } elseif (($perms & 0x6000) == 0x6000) {
            $info = 'b';//block special
        } elseif (($perms & 0x4000) == 0x4000) {
            $info = 'd';//dir
        } elseif (($perms & 0x2000) == 0x2000) {
            $info = 'c';//char special
        } elseif (($perms & 0x1000) == 0x1000) {
            $info = 'p';//fifo pipe
        } else {
            $info = 'u';//unknown
        }
        //owner
        $info .= (($perms & 0x0100) ? 'r' : '-') . (($perms & 0x0080) ? 'w' : '-')
            . (($perms & 0x0040) ? (($perms & 0x0800) ? 's' : 'x') : (($perms & 0x0800) ? 'S' : '-'));
        //group
        $info .= (($perms & 0x0020) ? 'r' : '-') . (($perms & 0x0010) ? 'w' : '-')
            . (($perms & 0x0008) ? (($perms & 0x0400) ? 's' : 'x') : (($perms & 0x0400) ? 'S' : '-'));
        //world
        $info .= (($perms & 0x0004) ? 'r' : '-') . (($perms & 0x0002) ? 'w' : '-')
            . (($perms & 0x0001) ? (($perms & 0x0200) ? 't' : 'x') : (($perms & 0x0200) ? 'T' : '-'));
        return $info;
    }

    private function fileSize($file, $is_dir, &$size) {
        if ($is_dir) {
            $size = count($this->dirScan($file));
            return '<span class="' . ($size > 100 ? 'red'
                : ($size > 50 ? 'green' : ($size < 10 ? 'grey' : ''))
            ) . '">' . $size . 'Files</span>';
        }
        $size = filesize($file);
        if ($size > 1024*1024) {
            return '<span class="red">' . number_format($size/1024/1024, 1) . 'M</span>';
        } elseif ($size > 1024) {
            return '<span class="' . ($size > 512000 ? 'gold'
                : ($size > 102400 ? 'blue' : ($size > 20480 ? 'green' : ''))
            ) . '">' . number_format($size / 1024, 1) . 'K</span>';
        } else {
            return '<span class="grey">' . $size . '</span>';
        }
    }

    private function fileUpload($dir) {
        $over_write = Data::clean('over_write')['over_write'];
        $file = $_FILES['file'];
        $uploaded = [];
        foreach ($file['name'] as $k => $name) {
            if (!$file['error'][$k] && $file['size'][$k]) {
                $ext = explode('.', $name);
                $ext = end($ext);
                if (!in_array($ext, ['exe', 'bat'])) {
                    $f = $this->root . $dir . '/' . $name;
                    if (!$over_write && file_exists($f)) {
                        $this->data['msg'] .= '<b class="red">FAILED</b> - file exists: ' . $name . '<br>';
                    } else {
                        move_uploaded_file($file['tmp_name'][$k], $f);
                        $this->data['msg'] .= 'File uploaded: ' . $name . '<br>';
                        $uploaded[] = $name;
                    }
                } else {
                    $this['msg'] .= '<b class="red">FAILED</b> - not allow: ' . $name . '<br>';
                }
            }
        }
        return $uploaded;
    }

    private function fileUserName($file, $owner = 0) {
        $uid = $owner ? fileowner($file) : filegroup($file);
        if (function_exists('posix_getpwuid')) {
            $a = posix_getpwuid($uid);
            return isset($a['name']) ? $a['name'] : '';
        }
        return $uid;
    }
}
