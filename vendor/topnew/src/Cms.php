<?php
namespace Topnew;

/**
 * Topnew Cms v 2020.02.22 - Base CMS library
 */

class Cms
{
    private static $instance_shared;
    private static $instance_alias = [
        'Db'     => 'Topnew.Db',
        'Config' => 'Topnew.Config',
    ];
    private static $view_root = '';

    public static function make($class = '', $param = []) {
        // Create shared instance
        $class = $class ? ucfirst($class) : 'Topnew.Cms';
        if (!isset(self::$instance_shared[$class])) {
            $alias = isset(self::$instance_alias[$class]) ? self::$instance_alias[$class] : $class;
            if (!$param && $alias == 'Topnew.Db') {
                $param = self::make('config')->get('db');
            }
            $alias = str_replace('.', '\\', $alias);
            self::$instance_shared[$class] = new $alias($param);
        }
        return self::$instance_shared[$class];
    }

    public static function run() {
        /**
         * Parse URL to Controller, Method, View, Args, _GET, _POST
         * eg URL     = test.com/some-folder/some-page/some-action/arg1/arg2?a=1&b=2
         * namespace  = App\Controller\SomeFolder
         * Controller = SomePageController
         * Method     = someAction($arg1, $arg2)
         * View       = App/View/SomeFolder/SomePage/someAction.tpl
         */
        $config = self::make('config');
        $namespace = str_replace('.', '\\', $config->get('controller_ns')) ?: 'App\\Controller';
        $method = $config->get('method_defa') ?: 'home';

        $url  = parse_url($_SERVER['REQUEST_URI']);
        if (!isset($url['path'])) {
            $url['path'] = '';
        }
        $path = $url['path'];
        $args = [];
        $class= '';

        // get controller from url
        $controller = '';
        $path = in_array(substr($path, -4), ['.php', '.htm']) ? substr($path, 0, -4) : $path;
        $path = substr($path, -5) == '.html' ? substr($path, 0, -5) : $path;
        while (strlen($path) && !$controller) {
            // some-folder/some-page -> SomeFolder\SomePageController
            $class = self::path2psr($path);
            $controller = $namespace . $class . 'Controller';
            // need upgrade to ignore after invalid eg /good/555/no-need-check-here
            // valid class name start with _a-Z followed by _a-Z0-9
            if (!class_exists($controller)) {
                $path = explode('/', $path);
                array_unshift($args, array_pop($path));
                $path = implode('/', $path);
                $controller = '';
            }
        }
        if (!$controller) {
            if (in_array($url['path'], ['/', '/404.php'])) {
                return;
            }
            header('HTTP/1.0 404 Not Found');
            include WEB . '404.php';
            exit;
            //header('location:/404.php');
        }
        $controller = new $controller();

        // get method from url
        if ($args && $args[0] && method_exists($controller, str_replace('-', '', $args[0]))) {
            $arg0 = lcfirst(self::path2psr($args[0]));
            if (!in_array(strtolower($arg0), $controller->method404)) {
                $method = $arg0;
                array_shift($args);
            }
        }

        // Produce view
        $res  = call_user_func_array([$controller, $method], $args);
        if (is_string($res) && strlen($res)) {
            echo $res;
        } elseif ($controller->view !== false) {
            $controller->view = $controller->view ?: $method;
            if (!in_array($controller->view[0], ['.', '\\', '/'])) {
                $controller->view = $class . '/' . $controller->view;
            }
            $controller->view();
        }
    }

    private static function path2psr($path = '') {
        // eg some-folder/some-page --> SomeFolder\SomePage
        return str_replace(' ', '', ucwords(strtr($path, ['/' => '\\ ', '-' => ' '])));
    }

    // The following 4 methods for view [optional]
    public static function view($file = '', $data = [], $is_text = 0) {
        if ($is_text) {
            $text = $file;
        } elseif (!$file) { // what about file name == '0' ? upgrade later
            return;
        } else {
            $ds = DIRECTORY_SEPARATOR;
            $file = str_replace(['/', '\\'], $ds, $file); //$ds . '.', '/.', '.',
            $ext  = make('config')->get('view_ext');
            $file = self::viewRoot() . $ds . $file . ($ext && substr($file, 0 - strlen($ext)) != $ext ? $ext : '');
            // next line possibly no need - just safety check
            $file = str_replace($ds . $ds, $ds, $file);
            // This view only allow html plain text + [[var]] -- no php code allowed :D
            if (!file_exists($file)) { // case insensitive
                return;
            }
            $text = file_get_contents($file);
        }
        $text = self::viewSub($text);
        if (!$data || !is_array($data)) {
            return $text;
        }

        $tran = [];
        $data = self::arrFlat($data);
        foreach ($data as $k => $v) {
            if (!is_array($v) && !is_object($v)) {
                $tran['{{' . $k . '}}'] = $v; // {{ val.abc }} not htmlentitied
            }
        }
        if (is_array($tran) && $tran) {
            $text = strtr(preg_replace('/\{\{ *(.+?)(\|raw)? *\}\}/', '{{\1}}', $text), $tran);
        }
        return $text;
    }
    private static function arrFlat($arr, $prefix = '') {
        // Convert $arr[a][b] = 3 to $arr[a.b] = 3
        $result = [];
        if (is_array($arr)) {
            foreach ($arr as $k => $v) {
                $k = $prefix . $k;
                if (is_array($v)) {
                    $result += self::arrFlat($v, $k . '.');
                } else {
                    $result[$k] = $v;
                }
            }
        }
        return $result;
    }
    private static function viewRoot() {
        if (! self::$view_root) {
            $ds = DIRECTORY_SEPARATOR;
            self::$view_root = make('config')->get('view_root') ?: (defined('ROOT') ? ROOT : $ds) . 'app' . $ds . 'View';
        }
        return self::$view_root;
    }
    private static function viewSub($text) {
        $arr = explode('include(', $text); // {{ include('sub.view') }}
        foreach ($arr as $i => $txt) {
            if ($i && strpos($txt, '}}')
              && substr(trim($arr[$i - 1]), -2) == '{{'
              && substr(trim($arr[$i - 1]), -3) != '\\{{'
            ) {
                $arr2 = explode('}}', $txt, 2); // 'sub.view'
                if (substr(trim($arr2[0]), -1) == ')') {
                    $arr2[0] = trim(substr(trim($arr2[0]), 0, -1));
                    if ($arr2[0][0] == substr($arr2[0], -1) && in_array($arr2[0][0], ['"', "'"])) {
                        $arr2[0] = trim($arr2[0], " \t\n\r\0\x0B'\")"); // remove white-space ' "
                        $arr2[0] = self::view($arr2[0]); // get sub view
                        $arr[$i] = implode('', $arr2);
                        $arr[$i - 1] = rtrim($arr[$i - 1], '{{ ');
                    }
                }
            }
        }
        return implode('', $arr);
    }

    /*public static function shutdown() {
        return;
        $err = error_get_last();
        $env = self::make('config')->get('app.env');
        if ($env != 'dev') {
            return;
        }
        if ($err !== null) {
            echo '<pre>'; print_r($err); echo '</pre>';
        }
    }*/

    /*public static function arr($fm, $to = ';', $inc = 1, $pref = '', $post = '') {
        // Produce array used for html select options
        // _arr(2000, 2020, 5, 'Year ', ' info') == [2000=>'Year 2000 info', 2005=>'Year 2005 info', ...]
        // _arr('a;b', ';', '', 'pre ', ' post') == ['a'=>'pre a post', 'b'=>'pre b post']
        $arr = [];
        if (is_int($fm) && is_int($to)) {
            for ($i = $fm; $i <= $to; $i += $inc) {
                $arr[$i] = $pref . $i . $post;
            }
            return $arr;
        }

        if (!is_array($fm)) {
            $fm = explode($to, $fm);
        }
        foreach ($fm as $i) {
            $arr[$i] = $pref . $i . $post;
        }
        return $arr;
    }*/

    /*public static function pr($arr = null, $mode = '', $level = 0) {
        // arr is array, object, or string
        // mode = ''  : displayed as nice json format: key : value, no quotes and folded
        // mode = php : use php print_r()
        // mode = dump: use php var_dump()
        // mode = json: displayed as full json format: "key" : "value",
        // mode = arr : displayed as array definition: 'key' =>'value',
        // level is used for internal loop -- no direct call
        // need css.hide and js.show to display nicely
        if (!$level) {
            echo '<pre>';
        }
        $is_OBJ = is_object($arr);
        if ('php' === $mode || 'dump' === $mode || (!is_array($arr) && !$is_OBJ)) {
            if ('dump' === $mode) {
                var_dump($arr);
            } else {
                print_r($arr);
            }
            echo '</pre>';
            return;
        }
        //the following: '' json arr
        $num = (is_array($arr) || $arr instanceof Countable) ? count($arr) : '?';
        if (!$level) {
            echo '<span class="show green"><span class="show">';
            echo $is_OBJ ? 'object' : 'array';
            echo ('arr' === $mode && !$is_OBJ) ? '(' : '{';
            echo '</span><span>' . $num . '</span></span><span class="hide">';
        }
        $tab  = str_repeat('    ', $level + 1);
        $tran = array("\t" => '\t', "\n" => '\n', '\\' => '\\\\');
        if ('arr' === $mode) {
            $tran["'"] = '\\\'';
        } else {
            $tran['"'] = '\"';
        }
        $i = 0;
        foreach ($arr as $k => $v) {
            $is_arr = is_array($v);
            $is_obj = is_object($v);
            if ($v && !$is_arr && !$is_obj) { // check if json
                $v  = trim($v);
                $s1 = substr($v, 0, 1); // do not use $v[0]
                $s2 = substr($v, -1);
                if (($s1 == '[' && $s2 == ']') || ($s1 == '{' && $s2 == '}')) {
                    $json = json_decode($v, 1);
                    if (is_array($json)) {
                        $is_arr = 1;
                        $v = $json;
                    }
                }
            }
            echo "\n" . $tab;
            echo ($is_arr || $is_obj) ? '<span class="show green"><span class="show">' : '';
            if ('arr' === $mode) {
                echo (is_numeric($k) && ceil($k) == $k ? $k : "'$k'") . ' => ';
            } else {
                echo ('json' === $mode) ? '"' . $k . '" : ' : '<b>' . $k . '</b> : ';
            }
            if ($is_arr || $is_obj) {
                echo ('arr' === $mode ? ($is_arr ? 'array(' : 'object{') : '{') . '</span><b>' . count($v) . '</b></span><span class="hide">';
                self::pr($v, $mode, $level + 1);
                echo "\n" . $tab .'</span><span class="green">' . ('arr' === $mode && $is_arr ? ')' : '}') . '</span>';
            } elseif (is_null($v)) {
                echo 'NULL';
            } elseif (is_numeric($v)) {
                echo $v;
            } else {
                $len = strlen($v);
                echo ('arr' === $mode ? "'" : ('json' === $mode ? '"' : ''));
                echo ($len > 50) ? '<span class="show"><span class="show">' : '';
                echo htmlspecialchars(strtr(substr($v, 0, 50), $tran), ENT_QUOTES, 'UTF-8');
                echo ($len > 50) ? '</span><i class="green">...</i></span><span class="hide">' : '';
                echo htmlspecialchars(strtr(substr($v, 50),    $tran), ENT_QUOTES, 'UTF-8');
                echo ($len > 50) ? '</span>' : '';
                echo ('arr' === $mode) ? "'" : ('json' === $mode ? '"' : '');
            }
            if ($mode && ++$i < $num) {
                echo ',';
            }
        }
        if (!$level) {
            echo "\n" . '</span><span class="green">' . ('arr' === $mode && !$is_OBJ ? ')' : '}') . '</span></pre>';
        }
    }*/
}
