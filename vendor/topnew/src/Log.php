<?php
namespace Topnew;

/**
 * Topnew Log v 2018.10.10
 * The MIT License (MIT) Copyright (c) 1998-2018, Topnew Geo, topnew.net
 *
 * Log http refer into log_yyyymm table using Topnew\Db
 */

class Log
{
    public static function save($uid = 0) {
        $cid  = self::getCid(); // cid.os.br.x.geo.x.ip

        $ref  = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
        $keyw = self::cleanUrl($ref, $ref_host, $ref_https);
        $keyw = self::cleanKeyw($keyw, $ref_host);

        $url  = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $url .= '://' . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '') . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '');
        $key2 = self::cleanUrl($url, $host, $https);
        $keyw = $keyw ?: self::cleanKeyw($key2, $host); // this is not referer keyw

        $skips = [
            '/favicon.ico',
            '/css/ico.png',
        ];
        if ($url && in_array($url, $skips)) {
            return;
        }
        $data = [
            'cid' => $cid[0],
            'uid' => ceil($uid), // new table design please use uid instead in the future
            'ip'  => $cid[6],
            'geo' => $cid[4],
            'https'=>$https ?: 0,
            'host'=> $host ? trim(substr($host, 0, 255)) : 0,
            'url' => $url ? trim(substr($url,  0, 255)) : 0,
            'ref_https'=> $ref_https ?: 0,
            'ref_host' => $ref_host ? trim(substr($ref_host, 0, 255)) : 0,
            'ref_url'  => $ref ? trim(substr($ref, 0, 255)) : 0,
            'keyw'=> $keyw ? trim(substr($keyw, 0, 255)) : 0,
            'os'  => $cid[1],
            'br'  => $cid[2],
        ];
        make('db')->insert('log_'. date('Y_m'), $data);
    }

    private static function cleanKeyw($str, $host) {
        $str = ($str) ? str_replace(["'", '"'], '', $str) : $str;
        if (!$str) {
            return;
        }
        $arr = explode('&', str_replace('?', '&', $str));
        $is_yahoo = strpos($host, 'yahoo') !== false ? 1 : 0;
        $is_google= strpos($host,'google') !== false ? 1 : 0;
        $google_para = ['sa', 'rct', 'esrc', 'source', 'cd', 'ved', 'url', 'ei', 'usg', 'sig2'];
        foreach ($arr as $k => $v) {
            $arr2 = explode('=', $v);
            $k2 = trim($arr2[0]);
            $v2 = isset($arr2[1]) ? trim($arr2[1]) : '';
            if ($v2 && ($k2 == 'q' || ($is_yahoo && $k2 == 'p') || $k2 == 'kw')) {
                return $k2 . '=' . $v2;
            } elseif (!$k2 || !$v2 || (strlen($v2) == 32 && !strpos($str, ' '))) {
                unset($arr[$k]); // empty or md5 hash
            } elseif ($is_google && in_array($k2, $google_para)) {
                unset($arr[$k]);
            }
        }
        return implode('&', $arr);
    }
    private static function cleanUrl(&$url = '', &$host = '', &$https = 0) {
        // $url = https://www.abc.com/path/page?query
        if (!$url) {
            return;
        }
        $arr = parse_url($url);
        $https = (isset($arr['scheme']) && $arr['scheme'] == 'https') ? 1 : 0;
        $host = isset($arr['host']) ? $arr['host'] : '';
        $path = isset($arr['path']) ? $arr['path'] : '';
        $url = self::cleanUrlHost($path, $host);
        if ($host == 'www.stumbleupon.com' && (
            $url  == '/refer.php' ||
            (substr($url, 0, 4) == '/su/' && strpos($url, $host) === false)
        )) {
            $url = $arr['query'] = '';
        }
        return isset($arr['query']) ? $arr['query'] : '';
    }
    private static function cleanUrlHost($url, &$host) {
        if (!$url) {
            return;
        } elseif (substr($url, -1) == '/') {
            return self::cleanUrlHost(substr($url, 0, -1), $host);
        } elseif (in_array(substr($url, -10), ['/index.php', '/index.htm'])) {
            return self::cleanUrlHost(substr($url, 0, -10), $host);
        } elseif (substr($url,-11) == '/index.html') {
            return self::cleanUrlHost(substr($url, 0, -11), $host);
        }
        if (substr($host, 0, 4) == 'www.') {
            $host = substr($host, 4);
        }
        if (strpos($host, 'yahoo') !== false) {
            $pos = strpos($url, ';_ylt=');
            if ($pos) {
                return substr($url, 0, $pos);
            }
        }
        return $url;
    }

    private static function getCid() {
        $cid = isset($_COOKIE['CID']) ? explode('.', $_COOKIE['CID'], 7) : []; // cid.os.br.x.geo.x.ipv6
        for ($i = 0; $i < 7; $i++) {
            if (!isset($cid[$i])) {
                $cid[$i] = 0;
            }
        }
        if ($cid[0] < 1 || $cid[0] > 2147483647) {
            //$now = str_pad(date('z') * date('H') + date('H'), 4, 0, STR_PAD_LEFT);
            $now = str_pad(floor((time() - mktime(0,0,0,1,1,date('Y'))) / 3600), 4, 0, STR_PAD_LEFT);
            $cid[0] = substr(date('y'), -1) . $now . rand(1000, 9999); // yd**hrand total 9digit
        }
        $ua = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
        $cid[1] = $cid[1] ?: self::getOs($ua);
        $cid[2] = $cid[2] ?: self::getBr($ua);
        if (!$cid[4] || !$cid[6]) {
            $cid[6] = Data::ip();
            $cid[4] = GeoIp::geo2($cid[6]);
        }
        $cook = implode('.', $cid);
        if (!isset($_COOKIE['CID']) || $cook != $_COOKIE['CID']) {
            setcookie('CID', $cook, time() + 157680000, '/'); // cookie for 5 years
        }
        return $cid;
    }
    private static function getBr($ua = '') {
        /**
         * Modified from https://github.com/cbschuld/Browser.php/blob/master/lib/Browser.php v2.0 20160722
         * 1 Opera must be checked before FireFox due to the odd user agents used in some older Opera ver
         * 2 WebTV is strapped onto Internet Explorer so we must check for WebTV before IE
         * 3 (deprecated) Galeon is based on Firefox and needs to be tested before Firefox is tested
         * 4 OmniWeb is based on Safari so OmniWeb check must occur before Safari
         * 5 Netscape 9+ is based on Firefox so Netscape checks before FireFox are necessary
         */
        $br = [
            100 => 'webtv', 101 => 'IE', 102 => 'MSN', 103 => 'PocketIE',
            110 => 'OperaMini', 111 => 'Opera', 115 => 'galeon',
            120 => 'Netscape',
            130 => 'FF',
            150 => 'Safari', 155 => 'NetPositive', 158 => 'Firebirde',
            160 => 'Konqueror', 165 => 'icab', 168 => 'Phoenix',
            170 => 'Amaya', 172 => 'Lynx', 175 => 'Iceweasel', 176 => 'Shirotoko', 177 => 'IceCat',
            180 => 'AOL',
            200 => 'googlebot',
            210 => 'msnbot',
            220 => 'Slurp',
            230 => 'W3C-checklink', 231 => 'W3C_Validator',
            250 => 'Moz',
            300 => 'Chrome',
            310 => 'omniweb',
            320 => 'Android',
            322 => 'iPhone', 323 => 'iPad', 324 => 'iPod',
            340 => 'BlackBerry',
            350 => 'Norkia'
        ];
        if (stripos($ua, 'webtv') !== false) {
            return 100;
        } elseif (stripos($ua, 'microsoft internet explorer') !== false) {
            return 101; //ie 1/1.5
        } elseif (stripos($ua, 'msie')  !== false && stripos($ua, 'opera') === false) { //ie>1.5
            return stripos($ua, 'msnb') === false ? 101 : 102; //the odd MSN Explorer
        } elseif (stripos($ua, 'mspie') !== false || stripos($ua, 'pocket') !== false) { //Pocket IE
            return 103;
        } elseif (stripos($ua, 'opera mini') !== false) { //opera
            return 110;
        } elseif (stripos($ua, 'opera') !== false) {
            return 111;
        } elseif (stripos($ua, 'galeon') !== false) {
            return 115;
        } elseif (stripos($ua, 'Firefox')!== false && stripos($ua, 'Navigator')!== false) {
            return 120; //nc9
        } elseif (stripos($ua, 'Firefox')=== false && stripos($ua, 'Netscape') !== false) {
            return 120; //nc
        } elseif (stripos($ua, 'safari') === false) {
            return 130;
        } elseif (stripos($ua, 'Chrome') !== false) {
            return 300;
        } elseif (stripos($ua, 'omniweb')!== false) {
            return 310;
        } elseif (stripos($ua, 'Android')!== false) {
            return 320;
        } elseif (stripos($ua, 'iPhone') !== false) {
            return 322;
        } elseif (stripos($ua, 'iPad') !== false) {
            return 323;
        } elseif (stripos($ua, 'iPod') !== false) {
            return 324;
        } elseif (stripos($ua, 'BlackBerry') !== false) {
            return 340;
        } elseif (preg_match('/Nokia([^\/]+)\/([^ SP]+)/i', $ua, $matches)) {
            return 350;
        } elseif (stripos($ua, 'googlebot') !== false) {
            return 200;
        } elseif (stripos($ua, 'msnbot') !==false) {
            return 210;
        } elseif (stripos($ua, 'Slurp') !== false) {
            return 220;
        } elseif (stripos($ua, 'Safari')!== false && stripos($ua, 'iPhone') === false && stripos($ua, 'iPod') === false) {
            return 150;
        } elseif (stripos($ua, 'NetPositive')!==false) {
            return 155;
        } elseif (stripos($ua, 'Firebirde') !== false) {
            return 158;
        } elseif (stripos($ua, 'Konqueror') !== false) {
            return 160;
        } elseif (stripos($ua, 'icab') !== false) {
            return 165;
        } elseif (stripos($ua, 'Phoenix')!==false) {
            return 168;
        } elseif (stripos($ua, 'Amaya') !== false) {
            return 170;
        } elseif (stripos($ua, 'Lynx')  !== false) {
            return 172;
        } elseif (stripos($ua, 'Iceweasel')!==false) {
            return 175;
        } elseif (stripos($ua, 'Mozilla') !== false && preg_match('/Shiretoko\/([^ ]*)/i', $ua, $matches)) {
            return 176;
        } elseif (stripos($ua, 'Mozilla') !== false && preg_match('/IceCat\/([^ ]*)/i', $ua, $matches)) {
            return 177;
        } elseif (stripos($ua, 'AOL')!==false) {
            return 180;
        } elseif (stripos($ua, 'W3C-checklink') !== false) {
            return 230;
        } elseif (stripos($ua, 'W3C_Validator') !== false) {
            return 231;
        } elseif (stripos($ua, 'mozilla') !== false) {
            return 250; //Mozilla is such an open standard that you must check it last
        } else {
            return 1; //NA
        }
    }
    private static function getOs($ua = '') {
        // Modified from https://github.com/cbschuld/Browser.php/blob/master/lib/Browser.php v2.0 20160722
        $os = [
            '', 'NA_OS', 'windows', 'iPad', 'iPod', 'iPhone', 'mac', 'android', 'linux', 'Nokia',
            'BlackBerry', 'FreeBSD', 'OpenBSD', 'NetBSD', 'OpenSolaris', 'SunOS', 'OS/2', 'BeOS', 'win'
        ];
        foreach ($os as $k => $v) {
            if (stripos($ua, $v) !== false) {
                return $k;
            }
        }
        return 1; // NA
    }
}
