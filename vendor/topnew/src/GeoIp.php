<?php
namespace Topnew;

/**
 * Topnew GeoIp v 2019.11.11
 * The MIT License (MIT) Copyright (c) 1998-2019, Topnew Geo, topnew.net
 *
 * Return Country ISO code by IP
 *
    $fm = 480000;
    $tab = '0_maxmind_geo5_aucnus';
    $sql = 'select ip,code from '.$tab.' where del=0 order by ip limit '.$fm.',101000';
    $arr = $this->db->arr($sql);
    $hold = '';
    foreach ($arr as $ip => $geo) {
        $geo2 = substr($geo, 0, 2);
        $geo = in_array($geo2, ['AU', 'CN', 'US']) ? $geo : $geo2;
        if ($hold == $geo) {
            $this->db->where('ip', $ip)->update($tab, ['del' => 1]);
        }
        $hold = $geo;
    }
    echo 'finished: ' . number_format($fm);
    exit;
 */

class GeoIp
{
    //private static $ver = 2019; // downloaded from Maxmind GeoIP 2019-10-23
    private static $ver = '2019aucn'; // downloaded from Maxmind GeoIP 2019-10-23

    public static function geo2($x = 0) {
        if (strpos($x, ':') !== false) {
            return 'NA'; // ipv6 not supported yet
        }
        if (strpos($x, '.')) {
            $x = sprintf('%u', ip2long($x));
        }
        if (!$x) {
            return 'NA';
        } elseif ($x >= 2130706433 && $x <= 2147483647) {
            return 12; // 127.0.0.1 ~ ***************
        } elseif ($x >= 3232235521 && $x <= 3232301055) {
            return 19; // *********** ~ ***************
        }
        //please check to match ip file name in /geoip
        //next line talks to geoip2019
        //$arr = [16777216, 677390000,1049369600, 1539968000, 1737216000, 2448408000, 3047424000, 3165440000, 3268608000, 3392928000];
        //next line takls to geoip2019aucn
        $arr = [16777216, 700594432, 1044744192, 1540338176, 1741690880, 2322992128, 2960334848, 3117470464, 3258102528, 3346323456];
        $file = -1;
        foreach ($arr as $i => $f) {
            if ($x >= $f) {
                $file = $i;
            }
        }
        if ($file < 0) {
            return 'NA';
        }
        include __DIR__ . '/geoip' . self::$ver . '/geo' . $file . '-' . $arr[$file] . '.php';
        $arr = explode("\n", $ip);
        $geo = 'NA';
        foreach ($arr as $line) {
            $arr2 = explode(',', $line);
            $ip = ceil($arr2[0]);
            if ($ip > $x) {
                return $geo;
            } elseif (isset($arr2[1])) {
                $geo = trim($arr2[1]);
            }
        }
        return $geo ?: 'NA';
    }
}
