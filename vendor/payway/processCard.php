<?php
/**
 * This folder is manually download from WestPac Payway API
 * Pls update Qvalent_PayWayAPI() in following file to __construct()
 */
include_once 'Qvalent_PayWayAPI.php';

class Payway {
    private $paywayApi;
    private $response;
    private $config;

    public function __construct() {
        $this->config = make('config');
        $initParams =
            'certificateFile=' . $this->config->get('payway.pem') . '&' .
            'caFile=' . $this->config->get('payway.crt') . '&' .
            'logDirectory=' . $this->config->get('payway.log');
        $this->paywayApi = new Qvalent_PayWayAPI();
        $this->paywayApi->initialise($initParams);
    }

    public function pay($cc_name, $cc_nbr, $cc_y, $cc_m, $cc_cvv, $amt, $inv_id) {
        // Process credit card request
        $requestParameters = [
            'order.type' => 'capture', // default ?
            'customer.username' => $this->config->get('payway.user'),
            'customer.password' => $this->config->get('payway.pass'),
            'customer.merchant' => $this->config->get('payway.merchant'),
            'customer.orderNumber' => $inv_id,
            'customer.originalOrderNumber' => $inv_id,
            'card.PAN' => $cc_nbr,
            'card.CVN' => $cc_cvv,
            'card.expiryYear' => $cc_y,
            'card.expiryMonth' => $cc_m,
            'card.cardHolderName' => $cc_name,
            'card.currency' => 'AUD', // always
            'order.amount' => $amt, // cents
            'order.ECI' => 'SSL', // always
        ];
        $requestText = $this->paywayApi->formatRequestParameters($requestParameters);
        $responseText = $this->paywayApi->processCreditCard($requestText);
        // Parse the response string into an array
        $response = $this->paywayApi->parseResponseParameters($responseText);
        if (!isset($response['response.receiptNo'])) {
            $response['response.receiptNo'] = '';
        }
        if (!isset($response['response.settlementDate'])) {
            $response['response.settlementDate'] = ''; // YYYYmm
        }
        $this->response = $response;
    }

    public function paid() {
        return $this->response['response.receiptNo'] 
            && $this->response['response.summaryCode'] == '0'; // Transaction Approved
    }

    public function getResponse() {
        return $this->response;
    }
}
