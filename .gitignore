
# Site Specific - CMS Configuration Files
config.php
app/News/Cli/Base.php

# Docker Files
docker-compose.yml
Dockerfile

# Server Configuration
www/.well-known/*
www/error_log
www/.htaccess
.htaccess
*.cache

# BackUps
www/aks/*

# Sitemaps
www/sitemap_news.xml
www/sitemap_news.xml.gz

# Front End View / Public Assets
www/favicon.*
www/apple-touch-icon*
www/touch-icon*

# User Uploaded Content
www/files/*

# Include: Admin Templates / Exclude everything else
www/template/*
!www/template/Admin/

# Composer
# composer.lock

# Locally Managed Packages
vendor/*
!vendor/payway/
vendor/payway/*.key
vendor/payway/*.crt
vendor/payway/*.pem
!vendor/topnew/

# Dev
/nbproject
/nbproject/private/
run.sh
