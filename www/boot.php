<?php
// only on production site
$sites = [
    'footprintnews.com.au',
    'shortlist.net.au',
    'ohsalert.com.au',
    'hrdaily.com.au',
    'workplaceexpress.com.au',
];
if (isset($_SERVER) && isset($_SERVER['HTTP_HOST']) && in_array($_SERVER['HTTP_HOST'], $sites)) {
    header('location:https://www.' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']);
    exit;
}//*/

define('DS', DIRECTORY_SEPARATOR);
define('WEB', __DIR__ . DS); // file root for web
define('ROOT', realpath(__DIR__ . '/..') . DS); // file root where app | vendor sits

// Use only either ONE of the following
require 'autoload.php'; // non-composer autoload
//require ROOT . 'vendor' . DS . 'autoload.php'; // composer autoload

//register_shutdown_function('shutdown');

$config = make('config', WEB . 'config.php'); // absolute path also works for CLI

if ($config->get('app.env') == 'dev') {
    ini_set('display_errors', 1);
    error_reporting(E_ALL);
    
    // Set up error logging to a file
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
    ini_set('error_log', ROOT . 'log/php_errors.log');
} else {
    // Production settings
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', ROOT . 'log/php_errors.log');
}

date_default_timezone_set($config->get('app.timezone') ?: 'UTC');

// block any ip exists in table log_ip_block
ipBlock($config->get('blog.site.name') . ' is temporarily closed for maintenance. Please check back soon.');

//Topnew\Cms::no_www($config->get('app.no_www') ?: 1);
//and redirect http to https -- fix later

if (!isset($cms_ses) || $cms_ses > 0) {
    session_start();
    $uid = Topnew\Auth::uid(); // make sure table log_login exist
}

if (!isset($cms_log) || $cms_log > 0) {
    // make sure table log_{yyyy}_{mm} exists
    Topnew\Log::save($uid); // this must run before Cms::run() to avoid cookie over-header
}

if (!isset($cms_run) || $cms_run > 0) {
    Topnew\Cms::run(); // run controller to get view
}

function make($class = '', $params = []) { return Topnew\Cms::make($class, $params); }

function ipBlock($msg = '', $tab = '') {
    $db = make('db');
    $tab = $tab ?: 'log_ip_block';
    $ip = Topnew\Data::ip();
    $id = $db->select('id')->from($tab)->where("'" . $ip . "' like ip")
        ->order('length(ip) DESC')->limit(1)->val();
    if ($id) {
        $db->where('id', $id)->update($tab, 'hit = hit + 1');
        exit($msg);
    }
}

function shutdown() {
    $err = error_get_last();
    $env = make('config')->get('app.env');
    if ($env != 'dev') {
        return;
    }
    if ($err !== null) {
        echo '<pre>'; print_r($err); echo '</pre>';
    }
}
