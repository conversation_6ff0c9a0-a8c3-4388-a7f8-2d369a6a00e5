<?php
/**
 * This page is used for redirect of old url to new
 *
 * And the following are possible parameters:
 * idw = 10 instant, 11 ddaily, 12 weekly, 13 manual
 * id = 55572 [blog_topic.tid]
 * no = 58286 [blog_topic.tid * blog_user.uid]
 * stream =   [blog_cat.stream]
 * utm_source=instant+email
 * utm_medium=email
 * utm_campaign=subscriber+email
 * utm_content=article+headline
 * utm_term=New%20RET%20update%20Five%20more%20carbon...
 *
 * hits_pages.page:
 * 2 nstory
 * 5 jstory
 * 30 nav
 * 31 instant
 * 32 daily
 * 33 weekly
 * 34 manually
 */

parse_str(isset($_SERVER['QUERY_STRING']) ? $_SERVER['QUERY_STRING'] : '', $params);

$id = isset($params['id']) ? ceil($params['id']) : 0;
$no = isset($params['no']) ? ceil($params['no']) : 0;

$uid = $id ? ceil($no / $id) : 0;

$id = $uid < 1 ? '' : 'news/' . $id . '/' . $no; // pass back other params ?

header('location:/' . $id);
