<?php
// This page only used when site newly migrated, can be deleted after 3 months old

// This page is used for redirect of old url to new

parse_str(isset($_SERVER['QUERY_STRING']) ? $_SERVER['QUERY_STRING'] : '', $params);

//nl06_scc_subrenew_cc.php?hd=1b36..3a02&id=11570&selkey=55908
//$md5_hash = md5($selkey + $id); // purchaser plus invoice number

$cid = $params && isset($params['selkey']) ? ceil($params['selkey']) : 0;
$id  = $params && isset($params['id']) ? ceil($params['id']) : 0;
$hd  = $params && isset($params['hd']) ? trim($params['hd']) : '';
$md5 = md5($cid + $id);

if (!$cid || !$id || !$hd || $hd != $md5) {
    exit;
}

$cms_run = 0;
include_once 'boot.php';

$site = $config->get('blog.site.id');

if ($site == 3 && $cid > 400000) {
    $cid -= 200000;
}

$cms = new App\News\Base();

$inv = $cms->db->from('inv_invoice')->where('inv_id', $id)->where('purchaser', $cid)->row();

if (!$inv || $inv['status'] != 'Unpaid') {
    exit;
}

$url = '/pay/' . $inv['inv_id'] . '/' . $cms->payCode($inv);

header('location:' . $url);

exit;
