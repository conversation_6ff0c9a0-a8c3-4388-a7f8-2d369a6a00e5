<?php
// This page is used for redirect of old url to new

parse_str(isset($_SERVER['QUERY_STRING']) ? $_SERVER['QUERY_STRING'] : '', $params);

// Fatal PHP Errors if string given - Is numeric condition added now - 07/08/2024
$tid = $params && isset($params['selkey']) && is_numeric($params['selkey']) ? ceil($params['selkey']) : 0;

$tid = $tid < 1 ? '' : 'news/' . $tid;

header('location:/' . $tid);
