<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirection to HTTPS
    RewriteCond %{SERVER_PORT} ^80$ [OR]
    RewriteCond %{HTTPS} =off
    RewriteCond %{REQUEST_URI} "!/.well-known/acme-challenge/"
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [NC,L,R=307]

    # Payment link - Invoices
    RewriteRule ^pay/(.*)$ /subscribe/payment/$1 [R=301,NC,L]

    # Redirect Trailing Slashes If Not A Folder
    # Caution: this might cause problem for form submit
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Handle Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ boot.php [L]

</IfModule>
