html,body{padding:0;margin:0;}
body{font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:14px;background:#f0f0f0;}

a{text-decoration:none;color:#08c;}
a:hover{text-decoration:underline;}

h1{border-bottom:solid 2px #f5f5f5}
h1,h2,h3{font-family:'Roboto Slab',serif}
h1, h2, h3,h1 a,h2 a,h3 a{color: #0072bc;}
a.acs0 { color: #090; }
a.acs1 { color: #0072bc; }
a.acs3 { color: #c80; }
a.acs7 { color: #666; text-decoration: line-through }
a.acs8 { color: #930; }
a.acs9 { color: #888; text-decoration: line-through}
.fa { width:20px; }
.fa-2x { width:35px; }
article { padding-bottom: 20px}
.muted a{ color:#999}

.form0{ opacity: 0; position: absolute; top: 0; left: 0; height: 0; width: 0; z-index: -1; overflow: none;}

:target { margin-top: -40px; padding-top: 40px; } /* make anchor show after fixed header */

h1,h2{color:#0e4d77;font-size:22.5px;line-height:24px;font-weight:400}
h2{font-size:20.5px;line-height:22px;color:#c60}

div.related h2{position:relative;color:#c60}
div.related h2 span{position:relative;background:#fff;padding-right:10px;}
div.related h2::before{content:'';background:#ddd;display:block;height:1px;position:absolute;left:0;top:50%;width:100%;}
div.related ul{margin-left:0;}
div.related li{list-style:none}

.q{position:relative;padding-left:60px;}
.q:before{
 content:'\201C';
 position:absolute;
 color:rgba(0,0,0,.1);
 font-size:120px;
 top:-30px;/* FP is 30px not sure why*/
 left:10px;
 font-family:sans-serif;
}

.cap {color:#bbb;text-align:center;float:right;width:33%;border:solid 1px rgba(0,0,0,.1);padding:.25rem;margin-left:10px}
.cap img{width:100%}
img.lightbox{cursor:pointer}
#lightbox{position:fixed;top:0;left:0;z-index:1031;background:rgba(0,0,0,.8);height:100%;width:100%;cursor:pointer}
#lightbox > div{position:relative;display:inline-block;top:50%;left:50%;transform:translate(-50%,-50%);border:solid 2px #fff}
#lightbox > div:after {position:absolute;content:'×';top:-16px;right:-16px;color:#fff;font-size:28px;text-shadow:0 1px 1px #000;background:#888;width:32px;height:32px;border-radius:50%;padding:4px 6px;border:solid 2px #fff}

.lazy{max-height:230px;position:relative;overflow:hidden;min-height:50px;}
.lazy .read-more{
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  text-align: center;
  margin: 0; padding: 150px 0 10px;
  background-image: linear-gradient(to bottom, transparent, #fff);
  background-image: -webkit-gradient(
    linear, left top, left bottom,
    color-stop(0, rgba(255, 255, 255, 0)),
    color-stop(1, rgba(255, 255, 255, 100))
  );
  cursor:pointer;
}

#nav{font-size:12px;line-height:20px;position:fixed;z-index:1030;height:31px;background:linear-gradient(to bottom,#222,#111);right:0;left:0;box-shadow:0 1px 10px rgba(0,0,0,.1);border-color:#252525;border-width:0 0 1px;overflow:hidden;}
#nav a{text-decoration:none;color:#999;padding:5px 15px;}
#nav a:hover{color:#fff;background:linear-gradient(to bottom,#006fbf,#003e79)}

#foot{background:#005898;color:rgba(255,255,255,.5)}
#foot a{color:#dce7ec;text-shadow:0 1px 0 rgba(0,0,0,.5);text-decoration: none}
#foot a:hover{color:#fff;border-bottom:1px solid #ec8200;}

.bon{box-shadow:0 10px 20px #aaa;height:40px;margin:10px 0 20px 20px;border-bottom-left-radius:3px}
.bon>*{
  background:linear-gradient(to bottom,#0092e1,#005898);
  padding:10px;margin:0 -10px 0 0;
  height:40px;overflow:hidden;color:#fff;
  border-radius:3px 3px 0 3px;
  font-size:18px;font-family:Georgia,serif;
  display:block;
}
.bon:after{content:'';display:block;width:0;height:0;border-top:10px solid #333;border-right:10px solid transparent;margin-right:-10px;float:right}
.bonRed>*{background:linear-gradient(to bottom,#f7a300,#d45e00)}
.bonRed:after{border-top-color:#b23c00}

.bonNav{margin-left:0}
.bonNav>*{margin-left:-10px;border-bottom-left-radius:0}
.bonNav:before{content:'';display:block;width:0;height:0;border-top:10px solid #333;border-left:10px solid transparent;margin-left:-10px;float:left;margin-top:40px}

.nav{margin-top:-10px;padding:0;}
.nav a {display:inline-block;color:#fff;text-decoration:none;padding:10px 20px}
.nav a.on,.nav a:hover{background:#005491;}

.story{padding-bottom:20px;}
.story h2 a{color:#0e4d77;font-size:20.5px;font-weight:400;font-family:'Roboto Slab',serif}
.cal{width:80px;padding-right:15px}
.cal>div{border:solid 1px #ec8200;border-radius:3px;padding:5px;text-align:center}
.cal>div>b{color:#77838b;font-size:22px}
.cal>div>i{background:#ec8200;color:#fff;font-size:12px;line-height:14px;padding:3px;margin-top:5px;display:block;font-style:normal;}
.story .muted{color:#999}
.story .muted a{color:#999}
.story ul{padding-left:20px}
@media (max-width:767px){
  #bann{display:none}
  #logo{text-align:center}
}

ul.arr{list-style:none;padding:0 20px;}
ul.arr li{border-bottom:solid 1px #eee;margin-left:20px;padding:5px 0;position:relative;}
ul.arr li a{text-decoration:none;color:#08c;}
ul.arr li :before{
  content: '';
  display:block;
  position:absolute;
  width:10px;
  height:10px;
  top:10px;
  margin-left:-20px;
  border-left:solid 5px #ec8200;
  border-top:solid 5px transparent;
  border-bottom:solid 5px transparent;
}

.breadcumb{background:#eee;padding:10px;color:#ccc;}
.breadcumb a{color:#0072bc;}

.related ul{padding-left:0;}
ul li{line-height:20px;}

/*
input[type="text"],input[type="submit"],input[type="date"],input[type="password"],select{height:30px;}
input{padding-left:5px;}

table.table{width:100%;}
.table td{padding:8px;}
*/