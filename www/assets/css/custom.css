/*
html,body{padding:0;margin:0;}
body{font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:14px;line-height:20px;background:#f0f0f0;}

#nav{font-size:12px;position:fixed;z-index:1030;height:30px;background:#333333;right:0;left:0;box-shadow:0 1px 10px rgba(0,0,0,.1);border-color:#252525;border-width:0 0 1px;overflow:hidden;}
#nav a{text-decoration:none;color:#eee;padding:5px 15px;}
#nav a:hover{color:#fff;background:#000000;}
#nav.footprint{background:#0072bc;}
#nav.footprint a:hover{background:#065FB1;}
#nav.shortlist{background:#0092e1;}
#nav.shortlist a:hover{background:#005491;}
#nav.ohsalert a:hover{background:#e87802;}
#nav.hrdaily{background:#842d79;}
#nav.hrdaily a:hover{ background:#5c1f54;}
#nav.workplaceexpress{background:#000000;}
#nav.workplaceexpress a:hover{background:#b94a48;}
*/

.container > header[class*="footprint"], footer[class*="footprint"]{background:#0072bc !important;}
.container > header[class*="shortlist"], footer[class*="shortlist"]{background:#0092e1 !important;}
.container > header[class*="ohsalert"], footer[class*="ohsalert"] {background:#e87802 !important;}
.container > header[class*="hrdaily"], footer[class*="hrdaily"]{background:#842d79 !important;}
.container > header[class*="workplaceexpress"], footer[class*="workplaceexpress"]{background:#000000 !important;}

.container > header .nav-pills .nav-link { border-radius: 0; }

.sticky-offset { top: 65px; }

h1, .h1 { font-size: 2rem; }
h2, .h2 { font-size: 1.75rem; }
h3, .h3 { font-size: 1.5rem; }
h4, .h4 { font-size: 1.25rem; }
h5, .h5 { font-size: 1rem; }
h6, .h6 { font-size: 1rem; }

.floating-toolbar {
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 1;
  display: none;
  }

.back-to-top {
  bottom: 0.5em;
  right: 1em;
  }

button.btn-save { 
  bottom: 1.2em; 
  right: 4.5em;
  }

 /* easyMDE - Preview*/
.editor-preview h2 { font-size:24.5px; line-height: 26px; margin: 20px 0 10px 0;}
.editor-preview h3 { font-size:19.5px; line-height: 22px;}
.editor-preview h4 { font-size:14.5px; line-height: 16px;}
.editor-preview h5 { font-size:12px; line-height: 14px;}
.editor-preview h6 { font-size:11.9px; line-height: 14px;}

/* Dashboard */
.dashboard .btn-light {
  border-color: rgb(108 117 125 / 15%) !important;
  }
