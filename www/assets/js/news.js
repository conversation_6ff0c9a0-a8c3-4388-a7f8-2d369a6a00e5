$(function() {
  /* this is copied from 2018.js */
  $(window).scroll(function(){
    var w = $(window), wt = w.scrollTop(), t = wt + w.height();
    $('div.load-news').each(function(i, e) {
      var o = $(e), ot = o.offset().top, h = ot + o.height();
      if (t >= h && ot >= wt) {
        o.removeClass('load-news'); // have to remove first as ajax might take time to load
        $.get('/news/load/' + $(this).data('lazy'), function(data) {
          o.html(data).removeClass('load-news');
          o.find('img.cap').each(function() { // lazy load need next 3 lines again
            $(this).parent().addClass('cap');
            $(this).after($(this).attr('alt'));
            $(this).removeClass('cap');
          });
        });
      }
    });

    $('div.youtube').each(function() {
      $(this).addClass('video').removeClass('youtube');
      var url = $(this).text();
      url && $(this).html('<iframe src="https://www.youtube.com/embed/' + url + (url.indexOf('?') != -1 ? '' : '?rel=0') + '" allowfullscreen></iframe>');
    });
    $('div.vimeo').each(function() {
      $(this).addClass('video').removeClass('vimeo');
      var url = $(this).text();
      url && $(this).html('<iframe src="https://player.vimeo.com/video/' + url + '" allow="autoplay;fullscreen" allowfullscreen></iframe>');
    });

  });

  /* this is copied from 2018.js */
  $(document).on('click', '.show', function() {
    var id = $(this).data('src');
    if (id == 'next') {
      id = $(this).next();
    } else if (id) {
      id = $(id);
    } else {
      if (id = this.id) { // not ==
        id = id.substr(0, 2) == 'ID' ? $('#' + id.substr(2)) : $('#' + id + 'SH');
      } else {
        id = $(this).next();
      }
    }
    id.toggleClass('hide');
  });

  /* this is copied from 2018.js */
  $('.span').each(function() {
    var w = $(this).data('w');
    var h = $(this).data('h');
    w && $(this).css('width', w + 'px');
    h && $(this).css('height', h + 'px');
  });

  /* this is copied from 2018.js */
  $('.confirm').on('click',function() {
    return confirm($(this).data('confirm'));
  });

  /* this is copied from 2018.js */
  $('.js-kid').each(function() {
      var kid = $(this).data('kid');
      var src = $(this).data('src');
      kid && $(this).children().addClass(kid); // for all kids
      if (src) { // for active kid only
          src = $.isNumeric(src) ? ':nth-child(' + src + ')' : src;
          $(this).children(src).addClass($(this).data('css') || 'on');
      }
  });

  /* this is copied from 2018.js */
  $('.formH').each(function() {
    var box = $(this).data('box');
    var kid = $(this).data('kid');
    var label = $(this).data('label');
    if (kid) {
      var kids = $(this).hasClass('label-0') ? $(this).children() : $(this).children().children(':nth-child(2)').not(':input[type=button], :input[type=submit], :input[type=reset]');
      kids.addClass(kid);
    }
    label && $(this).children().children(':first-child').addClass(label);
    box && $(this).children('div').addClass(box);
  });

  /* this is copied from 2018.js */
  $('.pgno > li:not(.off)').on('click', function() {
    var pgno = $(this).data('pgno') || $(this).html();
    var p = $(this).parent('.pgno');
    var id = p.data('id');
    p.data('url')
      ? window.location.href = p.data('url') + pgno + (p.data('param') ? '?' + p.data('param') : '')
      : $('#' + id).val(pgno).parent('form').submit();
  });

  /* this is copied from 2018.js */
  $('.pop>div:first-child>div:first-child').prepend('<b class="x">×</b>');
  $('.pop .x').on('click', function() {
    $('.pop').addClass('hide');
  });
  /* jo does not like it  -- turn off
  $('.pop').on('click', function(e) {
    (e.target == this) && !$(this).hasClass('x-only') && $('.pop').addClass('hide');
  });*/

  /* this is copied from 2018.js */
  $('select').each(function() {
    !$(this).val() && $(this).addClass('placeholder');
  }).on('change', function() {
    $(this).toggleClass('placeholder', !$(this).val());
  });

  /* this is copied from 2018.js */
  /* <div class=youtube>xxx</div> */
  $('div.youtube').each(function() {
    $(this).addClass('video').removeClass('youtube');
    var url = $(this).text();
    url && $(this).html('<iframe src="https://www.youtube.com/embed/' + url + (url.indexOf('?') != -1 ? '' : '?rel=0') + '" allowfullscreen></iframe>');
  });
  $('div.vimeo').each(function() {
    $(this).addClass('video').removeClass('vimeo');
    var url = $(this).text();
    url && $(this).html('<iframe src="https://player.vimeo.com/video/' + url + '" allow="autoplay;fullscreen" allowfullscreen></iframe>');
  });

  $(document).on('click', '.bookmark', function() {
    if ($(this).hasClass('text-success')) {
      $.get('/myaccount/bookmark/del/' + $(this).data('tid'));
    } else {
      $.get('/myaccount/bookmark/add/' + $(this).data('tid'));
    }
    $(this).toggleClass('icon-star text-success');
    $(this).toggleClass('icon-star-empty muted');
  });

  $(document).on('click', '.news a', function() {
    var href = $(this).attr('href');
    if (href.substring(0, 1) != '#' && href.substring(0, 7) != 'mailto:') {
      $(this).attr('target', '_blank');
    }
  });

  $('img.cap').each(function() {
    $(this).parent().addClass('cap');
    $(this).after($(this).attr('alt'));
    $(this).removeClass('cap');
  });

  $(document).on('click', 'img.lightbox', function() {
    $('#lightbox div img').attr('src', $(this).attr('src'));
    $('#lightbox').removeClass('hide');
  });
  $(document).on('click', '#lightbox', function() {
    $(this).addClass('hide');
  });

  $(document).on('click', '.read-more', function() {
    $(this).parent().removeClass('lazy');
    var id = $(this).data('id');
    $(this).remove();
    $.get('/news/lazy/' + id + '?ref=' + $('#TID').text());
  });

});
