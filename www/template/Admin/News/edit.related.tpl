
  <h3>Related content</h3>

  <div class="row mb-2">
    <div class="col-2">
      <span class="font-weight-bold">Format: </span>
    </div>
    <div class="col-2">
      <span class="font-weight-bold">Article ID: </span>
    </div>
    <div class="col-5">
      <span class="font-weight-bold">Description: </span>
    </div>
    <div class="col">
      <span class="font-weight-bold">URL: </span>
    </div>
  </div>

  {% set arr_fmt = {0:'Rel.Article','1':'Link',9:'Topic',2:'Pdf',3:'Image',4:'Audio',6:'MS Word',7:'MS Excel',8:'Search', 10:'Tag', 11:'Premium Rel.Article'} %}

  {% for x,topic in related %}
    <div class="row mb-2">
      <div class="col-2">
        <select class="custom-select d-block w-100 placeholder" name="related[{{x}}][fmt]">
        {% for k,v in arr_fmt %}
          <option value="{{ k }}"{% if k == topic.fmt %} selected{% endif %}>{{ v }}</option>
        {% endfor %}
        </select>
      </div>
      <div class="col-2">
        <input type="text" class="form-control" name="related[{{ x }}][tid]"  value="{{ topic.tid  }}">
      </div>
      <div class="col-5">
        <input type="text" class="form-control" name="related[{{ x }}][info]" value="{% if topic.info %}{{ topic.info }}{% else %}{{ topic.headline }}{% endif %}">
      </div>
      <div class="col">
        <input type="text" class="form-control" name="related[{{ x }}][url]"  value="{{ topic.url2 }}">
      </div>
    </div>

  {% endfor %}