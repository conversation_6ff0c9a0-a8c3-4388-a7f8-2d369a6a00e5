<h3 class="my-3">Meta: other information</h3>

  <input type="hidden" name="meta[img][0]" value="img">

  <div class="row">
    <div class="col-3">
      <input type="text" class="form-control" value="Summary image" readonly>
    </div>
    <div class="col">
      <input type="text" class="form-control" name="meta[img][1]" value="{{ meta.img }}">
    </div>
  </div>


{% for k,v in meta %}
<div class="row mb-2">

{% if k not in ['img', 'summary_short', 'uid_by'] %}
  <div class="col-3">
  <input type="text" name="meta[{{ k }}][0]" value="{{ k }}" class="form-control"
  {% if k in ['img', 'url', 'event_start', 'event_end', 'presenter', 'Phone', 'Email', 'footer', 'Lazy loading', 'Location', 'Registration', 'Cost', 'Event Type', 'When', 'Featured RHS', 'Layout', 'body_img', 'body_img_caption'] %}
    readonly
  {% else %}
    style=""
  {% endif %}
  >
  </div>

  <div class="col">
    {% if k == 'footer' %}
      <select name="meta[{{ k }}][1]" class="custom-select d-block w-100">
        <option value="">Please select</option>
        {% for f in footer_options %}
          <option value="{{ f }}"{% if v == f %} selected{% endif %}>{{ f }}</option>
        {% endfor %}
      </select>
    
    {% elseif k == 'Lazy loading' %}
      <select name="meta[{{ k }}][1]" class="custom-select d-block w-100">
        <option value="">Latest news</option>
        <option value="first-tag"{% if v == 'first-tag' %} selected{% endif %}>First tag</option>
      </select>
    
    {% elseif k == 'Event Type' %}
      <select name="meta[{{ k }}][1]" class="custom-select d-block w-100">
        <option value="">Please choose</option>
        <option value="Webinar"{% if v == 'Webinar' %} selected{% endif %}>Webinar</option>
        <option value="Webcast"{% if v == 'Webcast' %} selected{% endif %}>Webcast</option>
      </select>
    
    {% elseif k == 'Featured RHS' %}
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="meta[{{ k }}][1]" id="meta[{{ k }}][1]" value="1"{% if v %} checked{% endif %}>
        <label class="form-check-label" for="meta[{{ k }}][1]">Yes</label>
      </div>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="meta[{{ k }}][1]" id="meta[{{ k }}][1]" value="0"{% if not v %} checked{% endif %}>
        <label class="form-check-label" for="meta[{{ k }}][1]">No</label>
      </div>
    
    {% elseif k == 'Layout' %}
      <select name="meta[{{ k }}][1]" class="custom-select d-block w-100">
        <option value="">Please select</option>
        {% for f in layout_options %}
          <option value="{{ f }}"{% if v == f %} selected{% endif %}>{{ f }}</option>
        {% endfor %}
      </select>
    {% else %}
    <input type="{% if k in meta_date %}date{% else %}text{% endif %}" name="meta[{{ k }}][1]" value="{{ v }}" class="form-control"{% if k == 'Cost' %} placeholder="GST inclusive, leave blank if premium only"{% endif %}>
  {% endif %}
  </div>
{% endif %}
</div>
{% endfor %}


  <div class="row mb-2">
    <div class="col-3">
      <input type="text" class="form-control border-success" name="meta[1][0]" value="">
    </div>
    <div class="col">
      <input type="text" class="form-control" name="meta[1][1]" value="">
    </div>
  </div>

  <div class="row mb-2">
    <div class="col-3">
      <input type="text" class="form-control border-success" name="meta[2][0]" value="">
    </div>
    <div class="col">
      <input type="text" class="form-control" name="meta[2][1]" value="">
    </div>
  </div>
