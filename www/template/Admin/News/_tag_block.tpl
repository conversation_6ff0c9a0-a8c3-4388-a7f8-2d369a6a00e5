<div class="my-3" id="comm_tags">
  <h4>Available Tags - click to select / un-select (in order of relevance)</h4>

{% if site.id == 6 %}
<!--Footprint (2) Tag Layout -->

  {% for k,v in comm_tags %}
    {% if k < 2200 %}
      <div><b class="btn my-2 btn{% if sel_tags[k] %}-secondary{% else %}-outline-secondary{% endif %}" data-tag="{{ k }}" id="tag{{ k }}comm">{{ v }}</b></div>
      <div class="mb-3 pl-5">
      {% for k1,v1 in comm_tags %}
        {% if k1 > k and k1|slice(0,4) == k %}
          <b class="btn my-1 btn{% if sel_tags[k1] %}-secondary{% else %}-outline-secondary{% endif %}" data-tag="{{ k1 }}" id="tag{{ k1 }}comm">{{ v1 }}</b>
        {% endif %}
      {% endfor %}
      </div>
    {% endif %}
  {% endfor %}

{% else %}
<!--HR Daily, OHS Alert + DEMO, Workplace Express -->

  {% for k,v in comm_tags %}
    {% if k < 2200 %}
      <div><b class="btn my-2 btn{% if sel_tags[k] %}-secondary{% else %}-outline-secondary{% endif %}" data-tag="{{ k }}" id="tag{{ k }}comm">{{ v }}</b></div>
      {% for k1,v1 in comm_tags %}
        {% if k1 > k and k1|slice(0,4) == k and k1 < 220000 %}
          <div><b class="btn mb-1 ml-5 btn{% if sel_tags[k1] %}-secondary{% else %}-outline-secondary{% endif %}" data-tag="{{ k1 }}" id="tag{{ k1 }}comm">{{ v1 }}</b></div>
          <div class="ml-5 pl-5">
          {% for k2,v2 in comm_tags %}
            {% if k2 > k1 and k2|slice(0,6) == k1 %}
              <b class="btn mb-1 mr-1 btn{% if sel_tags[k2] %}-secondary{% else %}-outline-secondary{% endif %}" data-tag="{{ k2 }}" id="tag{{ k2 }}comm">{{ v2 }}</b>
            {% endif %}
          {% endfor %}
          </div>
        {% endif %}
      {% endfor %}
    {% endif %}
  {% endfor %}

{% endif %}

</div>
