<script>
  $(document).ready(function(){

   $( "#NewsArticle" ).validate( {
    ignore: ":hidden:not(.summaryTextEditor, .bodyTextEditor), .CodeMirror.cm-s-easymde",
    submitHandler: function(form) {
        {% if not tid and site.id != 7 %}
          // Only on first‐ever save: enable & check the mail switch
          var $mail    = $('input.custom-control-input#mail'),
              $wrapper = $('.custom-switch.alert');

          $mail.prop('disabled', false)
               .prop('checked', true);

          // ...and switch the alert styling to “warning”
          $wrapper
            .removeClass('alert-secondary')
            .addClass('alert-warning');
        {% endif %}
      form.submit();
    },

      rules: {
        headline: "required",
        summary_short: "required",
        summary: "required",
        txt: "required"
      },
      messages: {
        headline: "An article headline is required.",
        summary_short: "Please enter a short headline.",
        summary: "A summary is required.",
        txt: "Please enter the main article content."
      },
      
      errorPlacement: function ( error, element ) {
        // Add the `invalid-feedback` class to the error element
        error.addClass( "invalid-feedback" );

        if ( element.prop( "type" ) === "checkbox" ) {
          error.insertAfter( element.next( "label" ) );
        } else {
          error.insertAfter( element );
        }
      },
      highlight: function ( element, errorClass, validClass ) {
        $( element ).addClass( "is-invalid" ).removeClass( "is-valid" );
        $("html, body").animate({ scrollTop: 60 }, 600);
      },
      unhighlight: function (element, errorClass, validClass) {
        $( element ).addClass( "is-valid" ).removeClass( "is-invalid" );
      }
    } );

  });
</script>

<style>
#cats-selected div { background: #999; color: #fff; padding: 2px 8px; display: inline-block; margin: 0 1px 1px 0; border-radius: 5px; cursor: pointer;}
</style>
<script>
$(function() {
  $('#cats').on('change', function() {
    if ($(this).val()) {
      $('#cats-selected').append('<div><input type="hidden" value="' + $(this).val() + '" name="cats[]">' + $('#cats option:selected').text().replace(/^\-+/, '') + '</div>');
    }
  });
  $('#cats-selected').on('click', 'div', function() {
    $(this).remove();
  });

  /*$('#diff').on('click', function() {
    $.post('{{ action }}', {cmd: 'diff', txt: $('#txt').val()}, function (data) {
        var w = window.open('about:blank', 'diff');
        w.document.write(data);
        w.document.close();
    });
  });*/

  $('#comm_tags b').on('click', function() {
    $(this).toggleClass('btn-outline-secondary').toggleClass('btn-secondary');
    if ($(this).hasClass('btn-outline-secondary')) {
      $('#tag' + $(this).data('tag')).remove();
    } else {
      var tag = $(this).data('tag');
      $('#sel_tags').append('<span id="tag' + tag + '"><b class="btn btn-secondary mb-1">' + $(this).text() + '</b><input type="hidden" name="cats[]" value="' + tag + '"></span> ');
    }
  });
  $('#sel_tags').on('click', 'b', function() {
    var p = $(this).parent();
    p.remove();
    $('#' + p.attr('id') + 'comm').toggleClass('btn-outline-secondary').toggleClass('btn-secondary');
  });

  /*$(document).on('click', '#help div b', function(){
    var id = $("#txt");
    var pos = id[0].selectionStart;
    var txt = id.val();
    var str = $(this).data('text') ? $(this).data('text') : $(this).text();
    var cut = txt.substring(0, pos) + str;
    id.val(cut + txt.substring(pos));
    setTimeout(function(){id.focus()}, 0);
    pos = cut.length;
    id[0].setSelectionRange(pos, pos);
  });*/

})

// easyMDE Custom Functions ---------------------------------------------------------
// https://github.com/Ionaru/easy-markdown-editor

function insertMore(editor) {
    var cm = editor.codemirror;
    var output = '<!--MORE-->';
    cm.replaceSelection(output);
}

function insertGoTo(editor) {
    var cm = editor.codemirror;
    var output = '* [GotoA](#a{{ tid }})\n* [GotoB](#b{{ tid }})\n* [GotoC](#c{{ tid }})\n* [GotoD](#d{{ tid }})\n\n## HeadlineA { #a{{ tid }}}\n\n## HeadlineB { #b{{ tid }}}\n\n## HeadlineC { #c{{ tid }}}\n\n## HeadlineD { #d{{ tid }}}\n\n';
    cm.replaceSelection(output);
}

function insertQuote(editor) {
    var cm = editor.codemirror;
    var output = '\n> Quote quote quote quote quote quote quote.\n> \n> Quote quote quote quote quote quote quote quote quote quote quote quote quote.\n>\n> <cite>Author name</cite>\n';
    cm.replaceSelection(output);
}

function insertImage30(editor) {
    var cm = editor.codemirror;
    var output = '\n![Caption](/files/placeholder.jpg){.third.cap}\n';
    //var selectedText = cm.getSelection();
    //var text = selectedText || 'placeholder';
    //output = '!!' + text + '!!';
    cm.replaceSelection(output);
}

function insertImage50(editor) {
    var cm = editor.codemirror;
    var output = '\n![Caption](/files/placeholder-full.jpg){.half.cap}\n';
    cm.replaceSelection(output);
}

function insertImage100(editor) {
    var cm = editor.codemirror;
    var output = '\n![Caption](/files/placeholder-full.jpg){.full.cap}\n';
    cm.replaceSelection(output);
}

function insertYouTube(editor) {
    var cm = editor.codemirror;
    var output = '\n<div class="youtube">xxx</div>\n';
    cm.replaceSelection(output);
}

function insertVimeo(editor) {
    var cm = editor.codemirror;
    var output = '\n<div class="vimeo">xxx</div>\n';
    cm.replaceSelection(output);
}
</script>