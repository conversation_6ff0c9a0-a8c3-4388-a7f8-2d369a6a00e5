
<h3 class="my-3">Tags Selected</h4>
<div class="mb-3" id="sel_tags">
  {% for k,v in sel_tags %}
    <span id="tag{{ k }}">
      <b class="btn btn-secondary mb-1">{{ v }}</b>
      <input type="hidden" name="cats[]" value="{{ k }}">
    </span>
  {% endfor %}
</div>


{% if user.uid == 301 or user.uid == 333 or user.uid == 801 %}
<div class="mb-4">
  <label for="tags">Add a new tag <span class="text-muted">(Note: this is added to the group with the highest ID value)</span></label>
  <input type="text" class="form-control" id="tags" name="tags" placeholder="eg tag-name, tag-b">
</div>
{% endif %}

{% include '/Admin/News/_tag_block.tpl' with {'site': site, 'comm_tags': comm_tags, 'sel_tags': sel_tags} %}

<div class="clear"></div>
