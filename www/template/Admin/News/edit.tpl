{{ include('/Admin/News/edit.cssjs.tpl') }}
<!--
<div id="messageBox1" class="alert alert-danger"><ul></ul></div>
<div id="messageBox2"><ul></ul></div>
-->

<script>
$(function() {
  // cache the Save buttons
  var $saveBtns = $('button[name="cmd"][value="Save"].btn-primary');

  function updateSaveText() {
    // grab the selected option’s text
    var label = $('#acs option:selected').text() || '';
    // update each Save button
    $saveBtns.text('Save ' + label + ' Article');
  }

  // bind to change on the #acs select
  $('#acs').on('change', updateSaveText);

  // set initial text on page load
  updateSaveText();
});
</script>

<form name="NewsArticle" id="NewsArticle" action="{{ action }}" method="post" enctype="multipart/form-data">

  <div class="float-right">
    {% if tid %}
      <a href="/admin/news/del/{{ tid }}" class="btn btn-danger confirm" data-confirm="Are you sure you wish to delete this article? This action cannot be undone">Delete</a>
      <!--{#<a href="/news/{{ tid }}" target="news" class="btn btn-outline-primary">Preview #{{ tid }}</a>#}-->

      <!-- Button group with modal‑launch and “open in new window” -->
      <div class="btn-group" role="group">
        <!-- Launche iframe modal preview -->
        <button type="button" class="btn btn-outline-primary btn-preview" data-url="/news/{{ tid }}"> Preview #{{ tid }} </button>
        <!-- Keeps the original button behaviour -->
        <a href="/news/{{ tid }}" target="_blank" class="btn btn-outline-primary" title="Open in new window"> <i class="bi bi-box-arrow-up-right"></i></a>
      </div>

      <!-- Modal Preview for Article (and possibly sent email -->
      <div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog" role="document"
            style="max-width:95vw; max-height:95vh; margin:2.5vh auto;">
          <div class="modal-content" style="height:95vh;">
            <div class="modal-header">
              <h5 class="modal-title">Preview</h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body p-0" style="height:calc(100% - 56px);">
              <iframe src="" frameborder="0" style="width:100%; height:100%;"></iframe>
            </div>
          </div>
        </div>
      </div>
    {% endif %}
    <!--{{ edit_link|raw }}
    <b class="btn btn-outline-secondary" id="diff">Show Diff</b>-->
    <button type="submit" name="cmd" value="Save" class="btn btn-primary">Save article</button>
    {# Do not show publish button if site is HR Daily - site.id = 7 #}
    {% if not published and site.id != 7 %} 
      <button type="submit" name="cmd" value="Publish" class="btn btn-success" id="scheduleEmailBtn" onclick="return confirm('Have you checked the preview? This article will be emailed to all \'instant\' subscribers (if email box is ticked).')">Send email</button>
      {% elseif tid and site.id != 7 %}
      <button type="button" class="btn btn-success btn-preview"  data-toggle="tooltip" data-placement="bottom" data-original-title="Article published and email sent" disabled> <i class="bi bi-envelope-check"></i></button>
      <!--{#<button type="button" class="btn btn-success btn-preview"  data-url="/admin/camp-preview/20/1"  data-toggle="tooltip" data-placement="bottom" data-original-title="Preview email sent"> <i class="bi bi-envelope-check"></i></button>#}-->
    {% endif %}

    <script>
      // When the “Preview” button is clicked…
      $('.btn-preview').on('click', function(e){
        e.preventDefault();
        var url = $(this).data('url');
        // load it into the iframe & show the modal
        $('#previewModal iframe').attr('src', url);
        $('#previewModal').modal('show');
      });

      // optional: clear iFrame on close
      $('#previewModal').on('hidden.bs.modal', function(){
        $(this).find('iframe').attr('src', '');
      });
    </script>

  </div>

<button type="submit" name="cmd" value="Save" class="btn btn-primary btn-save floating-toolbar">Save article</button>

  <h2>{{ head }}</h2>

  {{ msg|raw }}

  <div class="mb-3">
    <!--i class="show badge btn-default fr" data-src="#summary-short">Short Headline</i-->
    <label class="font-weight-bold" for="">Headline: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
    <input type="text" class="form-control" id="headline" name="headline" value="{{ headline }}" placeholder="Headline" required>
  </div>

  <div class="mb-3">
    <label class="font-weight-bold" for="">Short headline: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
    <input type="text" class="form-control" id="summary-short" name="summary_short" value="{{ summary_short }}" placeholder="Short headline" required>
  </div>


  <div class="row">
    <div class="col-md-7">
      <span class="d-block font-weight-bold mb-2">Published Date: <i class="bi bi-question-circle text-muted ml-1" data-container="body" data-toggle="popover" data-placement="top" data-html="true" data-content="
      <p><strong>Website:</strong> Open, Basic, Freetrial, Premium articles become visable on the website after this date.</p>
      <p><strong>Email:</strong> If checked article will be included in automated Instant, Daily & Weekly emails.</p>
      "></i></span>

    <select class="custom-select col-2 mr-1" name="dt_d">
      {% for d in 1..31 %}
        <option value="{{ d }}"{% if dt_d == d %} selected{% endif %}>{{ '%02d'|format(d) }}</option>
      {% endfor %}
    </select>
    <select class="custom-select col-2 mr-1" name="dt_m">
      {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
      {% if i %}
        <option value="{{ i }}"{% if dt_m == i %} selected{% endif %}>{{ m }}</option>
      {% endif %}
      {% endfor %}
    </select>
    <select class="custom-select col-3 mr-1" name="dt_y">
      {% for y in 1999..2030 %}
        <option value="{{ y }}"{% if dt_y == y %} selected{% endif %}>{{ y }}</option>
      {% endfor %}
    </select>
    <select class="custom-select col-2 mr-1" name="dt_h">
      {% for h in 0..23 %}
        <option value="{{ h }}"{% if dt_h == h %} selected{% endif %}>{{ '%02d'|format(h) }}</option>
      {% endfor %}
    </select>
    <select class="custom-select col-2" name="dt_i">
      {% for i in 0..59 %}
        <option value="{{ i }}"{% if dt_i == i %} selected{% endif %}>{{ '%02d'|format(i) }}</option>
      {% endfor %}
    </select>

    </div>
    <div class="col-md-5 mt-sm-3 mt-md-0">
      <div class="custom-control custom-switch alert {% if published or tid %}alert-secondary{% else %}alert-warning{% endif %} p-2 ">
        <span class="d-block font-weight-bold mb-2">Include article in automated emails: <i class="bi bi-question-circle text-muted ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Have you checked the preview? This article will be emailed to all 'instant' subscribers (if email box is ticked)."></i></span>
        <input type="checkbox" class="custom-control-input" id="mail" name="mail" value="1" {% if tid is empty %} disabled{% endif %}{% if tid and mail %} checked{% endif %}>
        <label class="custom-control-label ml-5" for="mail">Email all 'instant' subscribers
        <div id="scheduleTimer" class="text-muted" style="display: none;"><strong>Next send:</strong></div>
        </label>
        
      </div>
    </div>
  </div>

<!--<pre>
    {{ dump() }}
</pre>-->

  <div class="">
    <label class="font-weight-bold" for="summary">Summary: <i class="bi bi-question-circle text-muted ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Use &lt;!--MORE--&gt; to increase the summary preview size."></i> <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
    <textarea class="form-control summaryTextEditor" id="summary" name="summary" required>{{ summary }}</textarea>
  </div>

<script>
// easyMDE Summary ---------------------------------------------------------
const easyMDEsummary = new EasyMDE({
  element: document.getElementById('summary'),
  autoDownloadFontAwesome: false,
  spellChecker: false,
  minHeight: "50px",
  //maxHeight: "75px",
  //lineNumbers: false,

  toolbar: [
    //'bold', 
    //'italic', 
    //'|', 
    //'unordered-list', 
    //'link', 
    //'|', 
    {
        name: "insertSumMore",
        action: insertMore,
        className: "more",
        text: "MORE",
        title: "Insert <!--MORE--> increases the summary preview size"
    },
    '|', 
    'preview' 
    ],

  placeholder: "Type here... <!--MORE--> increases the summary preview size.",
});
</script>

  <div class="row">
    <div class="col-md-auto mb-3">
      <label class="d-block font-weight-bold" for="uid">Published by: </label>
      {{ author | raw }}
    </div>
    <div class="col-md-3 mb-3">
      <label class="font-weight-bold" for="acs">Access minimum: </label>
      {{ acs | raw }}
    </div>
    <div class="col-md-3 mb-3">
      <label class="font-weight-bold" for="acs_only">Visible to:  </label>
      {{ acs_only | raw }}
    </div>
  </div>

  <div class="row">
    <div class="col">
      <label class="font-weight-bold" for="txt">Body content:  <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
      <textarea class="bodyTextEditor" name="txt" id="txt" required>{{ txt }}</textarea>
    </div>
  </div>

<script>
// easyMDE Main Content ---------------------------------------------------------
const easyMDEtxt = new EasyMDE({
  element: document.getElementById('txt'),
  autoDownloadFontAwesome: false,
  spellChecker: false,
  minHeight: "200px",
  maxHeight: "500px",
  //showIcons: ['strikethrough', 'code', 'table', 'redo', 'heading', 'undo', 'heading-bigger', 'heading-smaller', 'heading-1', 'heading-2', 'heading-3', 'clean-block', 'horizontal-rule'],

  placeholder: "Type here...",

  blockStyles: {
      bold: "**",
      italic: "_",
  },
  unorderedListStyle: "*",

  //promptURLs: true,

  insertTexts: {
      horizontalRule: ["", "\n\n-----\n\n"],
      image: ["\n![Caption](/files/placeholder-full.jpg", "){.full.cap}\n"],
      //link: ["[", "](https://)"],
  },

  toolbar: [
    "heading-2", 
    "heading-3", 
    '|', // Separator
    "bold",
    "italic",
    "unordered-list",
    "ordered-list",
    "link", 
    '|',
    {
        name: "insertGoTo",
        action: insertGoTo,
        className: "fa fa-list-alt",
        text: "",
        title: "Briefs - Go To list"
    },
      {
          name: "image30",
          action: insertImage30,
          className: "far fa-address-card",
          text: "",
          title: "Insert Image - 30% width"
      },
      {
          name: "insertQuote",
          action: insertQuote,
          className: "fa fa-quote-right",
          text: "",
          title: "Quote"
      },
    '|',
    "image", 
      {
          name: "image30",
          action: insertImage30,
          className: "far fa-image",
          text: "⅓ ",
          title: "Insert Image - 30% width"
      },
      {
          name: "image50",
          action: insertImage50,
          className: "far fa-image",
          text: "½ ",
          title: "Insert Image - 50% width"
      },
    "|",
      {
          name: "youtube",
          action: insertYouTube,
          className: "fab fa-youtube",
          text: "",
          title: "Insert YouTube"
      },
      {
          name: "vimeo",
          action: insertVimeo,
          className: "fab fa-vimeo-square",
          text: "",
          title: "Insert Vimeo"
      },
    "|",
    "preview", 
    // "fullscreen", 
     "|"
  ]

});
</script>

  {{ include('/Admin/News/edit.related.tpl') }}

  <div class="row mt-4">
    <div class="col">
      <label class="font-weight-bold" for="url">Article URL: </label>
      <input type="text" class="form-control" id="url" name="url" value="{{ url }}">
    </div>
    <div class="col">
      <div class="cat">
        <label class="font-weight-bold d-block" for="cats">Categories: </label>
        <select id="cats" class="form-control xcustom-select">
          {{ cats_options|raw }}
        </select>
        <div class="mt-2" id="cats-selected" title="Click to remove">{{ cats_selected|raw }}</div>
      </div>
    </div>
  </div>


  <!--{#<div class="row">
    <div class="col">
      <span class="d-block font-weight-bold mb-2">Upload image / file: </span>
      <div class="custom-file">
        <input type="file" class="custom-file-input" name="file[]">
        <label class="custom-file-label" for="file[]">Choose file</label>
      </div>
    </div>
  </div>#}-->

  <!--{#<input type="submit" name="cmd" value="Save" class="cell-2 rc bg-green">#}-->

  {{ include('/Admin/News/edit.meta.tpl') }}
  {{ include('/Admin/News/edit.tag.tpl') }}
</form>


<script>

$(function(){
  // 1) Helper: round a Date up to the next 5‑minute mark
  function roundUp5(dt) {
    var ms = 5 * 60 * 1000;
    return new Date(Math.ceil(dt.getTime() / ms) * ms);
  }

  // 2) Helper: format a Date as "18 Apr 2025 at 13:25"
  function formatDate(dt) {
    var day    = dt.getDate(),
        month  = ["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][dt.getMonth()],
        year   = dt.getFullYear(),
        hh     = String(dt.getHours()).padStart(2,"0"),
        mm     = String(dt.getMinutes()).padStart(2,"0");
    return day + " " + month + " " + year + " at " + hh + ":" + mm;
  }

  function updateScheduleUI() {
    var $btn       = $('#scheduleEmailBtn'),
        $timer     = $('#scheduleTimer'),
        $mailInput = $('input.custom-control-input#mail'),
        acs        = $('#acs').val(),
        $wrapper   = $('.custom-switch.alert');

    // 3) If the schedule button isn't in DOM → scheduling closed
    if (!$btn.length) {
      $mailInput.prop('disabled', true);
      $wrapper.removeClass('alert-success').addClass('alert-secondary');
      $timer.hide();
      return;
    } else {
      // re‑enable mail switch if button exists
      $mailInput.prop('disabled', false);
    }

    // 4) Update wrapper style: green when scheduling active, warning otherwise
    if ($mailInput.is(':checked') && acs !== '7' && acs !== '8') {
      $wrapper.removeClass('alert-warning').addClass('alert-success');
    } else {
      $wrapper.removeClass('alert-success').addClass('alert-warning');
    }

    // 5) Show/hide button & timer based on mail switch
    if (!$mailInput.is(':checked')) {
      $btn.hide();
      $timer.hide();
      return;
    }
    $btn.show();
    $timer.show();

    // 6) Build publish Date & compare to now
    var year   = +$('select[name="dt_y"]').val(),
        month  = +$('select[name="dt_m"]').val() - 1,
        day    = +$('select[name="dt_d"]').val(),
        hour   = +$('select[name="dt_h"]').val(),
        minute = +$('select[name="dt_i"]').val(),
        pubDt  = new Date(year, month, day, hour, minute),
        now    = new Date();

    // 7) Disable & label the button for Unpublished (7) or Draft (8)
    if (acs === '7' || acs === '8') {
      $btn.prop('disabled', true)
          .text('Send Email');
    } else {
      $btn.prop('disabled', false)
          .text('Send Email');
      if (now >= pubDt) {
        $btn.text('Publish & Send Email');
      } else {
        $btn.text('Schedule Article & Email');
      }
    }

    // 8) Update the timer text
    if (pubDt > now) {
      // Future publish: round pubDt up
      var pubNext     = roundUp5(pubDt),
          pubFormatted = formatDate(pubNext);
      $timer.html(
        '<i class="bi bi-calendar-check"></i> <strong>Scheduled:</strong> ' + pubFormatted
      );
    } else {
      // Now/past: next run based on current time
      var nextRun   = roundUp5(now),
          formatted = formatDate(nextRun);
      $timer.html(
        '<i class="bi bi-stopwatch"></i> <strong>Immediately:</strong> ' + formatted
      );
    }
  }

  // 9) Bind to all relevant inputs
  $(
    'input.custom-control-input#mail, ' +
    '#acs, ' +
    'select[name="dt_d"], select[name="dt_m"], ' +
    'select[name="dt_y"], select[name="dt_h"], select[name="dt_i"]'
  ).on('change', updateScheduleUI);

  // 10) Init + live 30s refresh
  updateScheduleUI();
  setInterval(updateScheduleUI, 30000);
});

</script>
