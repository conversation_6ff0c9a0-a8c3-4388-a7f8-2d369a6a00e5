<h1>Page hits report</h1>

<form action="/admin/stat/tag" method="post">
<div class="line-m">
  <div class="line-s cell-6">
    <b class="cell-2 fm" title="This year">From</b>
    <select class="cell-2" name="d1">
      <option value="">Day</option>
      {% for d in 1..31 %}
        <option value="{{ d }}"{% if form.d1 == d %} selected{% endif %}>{{ '%02d'|format(d) }}</option>
      {% endfor %}
    </select>
    <select class="cell-2" name="m1">
      <option value="">Month</option>
      {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
      {% if i %}
        <option value="{{ i }}"{% if form.m1 == i %} selected{% endif %}>{{ m }}</option>
      {% endif %}
      {% endfor %}
    </select>
    <select class="cell-2" name="y1">
      <option value="">Year</option>
      {% for y in 2000..2030 %}
        <option value="{{ y }}"{% if form.y1 == y %} selected{% endif %}>{{ y }}</option>
      {% endfor %}
    </select>
    <select class="cell-2" name="h1">
      <option value="">Hour</option>
      {% for h in 0..23 %}
        <option value="{{ h }}"{% if form.h1 == h %} selected{% endif %}>{{ '%02d'|format(h) }}</option>
      {% endfor %}
    </select>
    <select class="cell-2" name="i1">
      <option value="">Minute</option>
      {% for i in 0..59 %}
        <option value="{{ i }}"{% if form.i1 == i %} selected{% endif %}>{{ '%02d'|format(i) }}</option>
      {% endfor %}
    </select>

    <b class="cell-2 to" title="This month">To</b>
    <select class="cell-2" name="d2">
      <option value="">Day</option>
      {% for d in 1..31 %}
        <option value="{{ d }}"{% if form.d2 == d %} selected{% endif %}>{{ '%02d'|format(d) }}</option>
      {% endfor %}
    </select>
    <select class="cell-2" name="m2">
      <option value="">Month</option>
      {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
      {% if i %}
        <option value="{{ i }}"{% if form.m2 == i %} selected{% endif %}>{{ m }}</option>
      {% endif %}
      {% endfor %}
    </select>
    <select class="cell-2" name="y2">
      <option value="">Year</option>
      {% for y in 2000..2030 %}
        <option value="{{ y }}"{% if form.y2 == y %} selected{% endif %}>{{ y }}</option>
      {% endfor %}
    </select>
    <select class="cell-2" name="h2">
      <option value="">Hour</option>
      {% for h in 0..23 %}
        <option value="{{ h }}"{% if form.h2 == h %} selected{% endif %}>{{ '%02d'|format(h) }}</option>
      {% endfor %}
    </select>
    <select class="cell-2" name="i2">
      <option value="">Minute</option>
      {% for i in 0..59 %}
        <option value="{{ i }}"{% if form.i2 == i %} selected{% endif %}>{{ '%02d'|format(i) }}</option>
      {% endfor %}
    </select>
  </div>
  <div class="line-s cell-2 mx-2">
    <b class="cell-6">Reader</b>
    <input type="text" name="uid" value="{{ form.uid }}" class="cell-6">
    <b class="cell-6">Article</b>
    <input type="text" name="tid" value="{{ form.tid }}" class="cell-6">
  </div>
  <div class="line-s cell-4">
    <b class="cell-2 pb-2">Tag</b>
    <b class="cell-10 show" data-src="#tags">
      <input type="hidden" name="tag" value="{{ form.tag }}" id="tag">
      <b class="rc bg">{% if form.tag %}{{ form.tag }}{% else %}All tags{% endif %}</b></b>
    <b class="cell-2">Top</b>
    <input type="text" name="num" value="{% if form.num %}{{ form.num }}{% else %}50{% endif %}" class="cell-2">
    <input type="submit" name="cmd" value="Article" class="rc bg-green">
    <input type="submit" name="cmd" value="User" class="rc bg-blue">
  </div>
</div>
</form>

<p id="tags" class="hide">
{% for t in tags %}
  <b class="rc ib mb-1 bg{% if form.tag == t.name %}-grey{% endif %}" data-tag="{{ t.name }}">{% if t.name %}{{ t.name }}{% else %}{{ t.info }}{% endif %}</b>
{% endfor %}
</p>

<script>
$(function() {
  $('#tags b').on('click', function() {
    $('#tag').val($(this).data('tag'));
    $('#tag').next().html($(this).text());
    $('#tags b').removeClass('bg-grey');
    $(this).addClass('bg-grey');
  });
})
</script>

<table class="table">
<tr class="bg">
  <td>{% if form.cmd == 'User' %}User{% else %}Article{% endif %}</td>
  <td class="ar">Hits</td>
  <td>{% if form.cmd == 'User' %}Last Visited{% else %}Published{% endif %}</td>
  <td>{% if form.cmd == 'User' %}Name</td><td>Email{% else %}Headline{% endif %}</td></tr>

{% set acs = ['Guest', '<i class="green">Basic</i>', '<i class="blue">Freetrial</i>', '<b class="gold">Premium</b>', 'NA', 'NA', 'Staff', '<b>Support</b>', '<b class="grey">Editor</b>', '<b class="red">Admin</b>'] %}

{% set ttl = 0 %}
{% set last_week = 'now'|date_modify('-7 days')|date('Y-m-d') %}
{% for r in hits %}
{% set ttl = ttl + r.hit %}
  <tr>
    <td>{% if form.cmd == 'User' %}<a href="?uid={{ r.uid }}&cmd=User&fm={{ form.fm }}&to={{ form.to }}">{{ r.uid }}</a>{% else %}<a href="?tid={{ r.tid }}&fm={{ form.fm }}&to={{ form.to }}">{{ r.tid }}</a>{% endif %}</td>
    <td class="ar">{{ r.hit }}</td>
    <td>{{ r.created | date('d/n/Y') }}</td>
    <td>{% if form.cmd == 'User' %}<a href="/admin/user/{{ r.uid }}" target="_blank">{{ r.fname }} {{ r.lname }}</a> ({{ acs[r.acs] | raw }})</td><td>{{ r.email }}{% else %}<a href="/news/{{ r.tid }}" target="_blank">{{ r.headline }}</a>{% endif %}</td>
  </tr>
{% endfor %}

</table>

<p>Total hits: {{ ttl }}</p>

{% if users %}
  <h1>Recent readers of this article</h1>
  <table class="table">
  <tr class="bg"><td>UID</td><td>Visited at</td><td>Name</td><td>Email</td><td>IP</td><td>Source</td></tr>

  {% for n in users %}
    <tr>
      <td><a href="?uid={{ n.uid }}&cmd=User&fm={{ form.fm }}&to={{ form.to }}">{{ n.uid }}</td>
      <td>{{ n.created | date('d/n/Y H:i') }}</td>
      <td><a href="/admin/user/{{ n.uid }}" target="_blank">{{ names[n.uid]['fname'] }} {{ names[n.uid]['lname'] }}</a></td>
      <td>{{ names[n.uid]['email'] }}</td>
      <td>{{ n.ip }}</td>
      <td>{% if n.log_ref %}
        {% if n.log_type[1:] == 1 %}
          News: <a href="/news/{{ n.log_ref }}" target="_blank">{{ n.log_ref }}</a>
        {% elseif n.log_type[1:] == 2 %}
          Topics: {{ n.log_ref }}
        {% elseif n.log_type[1:] == 3 %}
          <a href="/tag/{{ tags[n.log_ref]['name'] }}" target="_blank">/tag/{{ tags[n.log_ref]['name'] }}</a>
        {% elseif n.log_type[1:] == 4 %}
          {% if n.log_ref[0:1] == 1 %}
            <i class="red">Instant</i>
          {% elseif n.log_ref[0:1] == 2 %}
            <i class="blue">Daily</i>
          {% elseif n.log_ref[0:1] == 3 %}
            <i class="green">Weekly</i>
          {% elseif n.log_ref[0:1] == 4 %}
            <i class="gold">Manual</i>
          {% endif %}
          Email
          {% if n.log_ref[1:] == 1 %}
            <i class="red">headline</i>
          {% elseif n.log_ref[1:] == 2 %}
            <i class="blue">summary</i>
          {% elseif n.log_ref[1:] == 3 %}
            <i class="green">more</i>
          {% endif %}
        {% endif %}
      {% endif %}</td>
    </tr>
  {% endfor %}

  </table>
{% endif %}

{% if news %}
  <h1>Recent articles read by this user</h1>
  <table class="table">
  <tr class="bg"><td>TID</td><td>Read at</td><td>Headline</td><td>IP</td><td>Source</td></tr>

  {% for n in news %}
    <tr>
      <td><a href="?tid={{ n.tid }}&fm={{ form.fm }}&to={{ form.to }}">{{ n.tid }}</a></td>
      <td>{{ n.created | date('d/n/Y H:i') }}</td>
      <td><a href="/news/{{ n.tid }}" target="_blank">{{ n.headline }}</a></td>
      <td>{{ n.ip }}</td>
      <td>{% if n.log_ref %}
        {% if n.log_type[1:] == 1 %}
          News: <a href="/news/{{ n.log_ref }}" target="_blank">{{ n.log_ref }}</a>
        {% elseif n.log_type[1:] == 2 %}
          Topics: {{ n.log_ref }}
        {% elseif n.log_type[1:] == 3 %}
          <a href="/tag/{{ tags[n.log_ref]['name'] }}" target="_blank">/tag/{{ tags[n.log_ref]['name'] }}</a>
        {% elseif n.log_type[1:] == 4 %}
          {% if n.log_ref[0:1] == 1 %}
            <i class="red">Instant</i>
          {% elseif n.log_ref[0:1] == 2 %}
            <i class="blue">Daily</i>
          {% elseif n.log_ref[0:1] == 3 %}
            <i class="green">Weekly</i>
          {% elseif n.log_ref[0:1] == 4 %}
            <i class="gold">Manual</i>
          {% endif %}
          Email
          {% if n.log_ref[1:] == 1 %}
            <i class="red">headline</i>
          {% elseif n.log_ref[1:] == 2 %}
            <i class="blue">summary</i>
          {% elseif n.log_ref[1:] == 3 %}
            <i class="green">more</i>
          {% endif %}
        {% endif %}
      {% endif %}</td>
    </tr>
  {% endfor %}

  </table>
{% endif %}

{% if logs %}
  <h1>Recent web log of this {% if form.cmd == 'User' %}user{% else %}article{% endif %}</h1>
  <table class="table">
  <tr class="bg"><td>UID</td><td>Visited at</td><td>Name</td><td>Email</td><td>IP</td><td>Source</td></tr>

  {% for u in logs %}
    <tr>
      <td><a href="?uid={{ u.uid }}&cmd=User&fm={{ form.fm }}&to={{ form.to }}">{{ u.uid }}</td>
      <td>{{ u.ts | date('d/n/Y H:i') }}</td>
      <td><a href="/admin/user/{{ u.uid }}" target="_blank">{{ names[u.uid]['fname'] }} {{ names[u.uid]['lname'] }}</a></td>
      <td>{{ names[u.uid]['email'] }}</td>
      <td>{{ u.ip }} ({{ u.geo }})</td>
      <td>{{ u.ref_host }}{{ u.ref_url }}</td>
    </tr>
  {% endfor %}

  </table>
{% endif %}
