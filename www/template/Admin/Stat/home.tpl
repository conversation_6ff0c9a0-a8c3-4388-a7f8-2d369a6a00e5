<h1>Stats Dashboard</h1>

<div class="line">
  <div class="cell-9">
    {{ stat.daily | raw }}
    {{ stat.hourly | raw }}
    {{ stat.monthly | raw }}
    {{ stat.hourlyPop | raw }}
  </div>
  <div class="cell-3">
    {{ stat.geo | raw }}
    {{ stat.os | raw }}
    {{ stat.br | raw }}
  </div>
</div>

<br>
<div class="line">
  <div class="cell-6">
    <h1>Top 20 Articles -- <a href="/admin/stat/tag">past 7 days ...</a></h1>
    <ul>
    {% for n in top.news.all7 %}
      <li>
        <a href="{{ n.url }}">{{ n.headline }}</a>
        <i class="grey">{{ n.created | date('Y-m-d') }}</i> -
        <i class="green">{{ n.num }}</i>
        {% if n.rank and n.rank < 20190101 %}
        <i class="fa fa-star gold small"></i><i class="grey">{{ n.rank }}</i>
        {% endif %}
      </li>
    {% endfor %}
    </ul>
  </div>
  <div  class="cell-6">
    <h1>Top 20 Articles -- trending</h1>
    <ul>
    {% for n in top.news.trend %}
      <li>
        <a href="{{ n.url }}">{{ n.headline }}</a>
        <i class="grey">{{ n.created | date('Y-m-d') }}</i> -
        <i class="green">{{ n.hit }}</i>
        {% if n.rank and n.rank < 20190101 %}
        <i class="fa fa-star gold small"></i><i class="grey">{{ n.rank }}</i>
        {% endif %}
      </li>
    {% endfor %}
    </ul>
  </div>
</div>

{% set acs = ['Guest', '<i class="green">Basic</i>', '<i class="grey">Freetrial</i>', '<b class="gold">Premium</b>', 'NA', 'NA', 'Staff', '<b>Support</b>', '<b class="green">Editor</b>', '<b class="red">Admin</b>'] %}

<div class="line">
  <div class="cell-6">
    <h1>Current Online Users</h1>
    <ul>
    {% for u in users_online %}
      <li><span{% if u.status != 'active' %} class="grey"{% endif %}>{{ u.fname }} {{ u.lname}}</span> ({{ acs[u.acs] | raw}}) <i class="grey">{{ u.ts | date('d M H:i') }}</i> - <i class="green">{{ u.num }}</i></li>
    {% endfor %}
    </ul>
  </div>
  <div class="cell-6">
    <h1>Most active readers -- last 2 months</h1>
    <ul>
    {% for u in users_top %}
      <li><span{% if u.status != 'active' %} class="grey"{% endif %}>{{ u.fname }} {{ u.lname}}</span> ({{ acs[u.acs] | raw}}) <i class="grey">{{ u.ts | date('d M H:i') }}</i> - <i class="green">{{ u.num }}</i></li>
    {% endfor %}
    </ul>
  </div>
</div>

<h1>TODO -- other stats:</h1>

<pre>
* most traffic via email camp
* most traffic via email parts
* most popular keyw this month
* list of spam email / domain -- last seen / hits most
* list of ip blocked -- last check / most
* top referrer external -- last 2 months
* top referrer internal from story/cat/tag to story
</pre>
