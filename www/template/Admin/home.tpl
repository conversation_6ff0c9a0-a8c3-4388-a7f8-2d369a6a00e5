<h1>Admin Dashboard</h1>
<div class="row dashboard">
  <div class="col col-sm-2 my-2">
    <a href="/admin/news/post" class="btn btn-success btn-block"><i class="bi bi-pencil h1"></i><br>Article</a>
  </div>
  <div class="col col-sm-2 my-2">
    <a href="/?acs=8" class="btn btn-secondary btn-block"><i class="bi bi-search h1"></i><br>Drafts</a>
  </div>
  <div class="col col-sm-2 my-2">
    <a href="/?acs=1" class="btn btn-secondary btn-block"><i class="bi bi-search h1"></i><br>Freetrial</a>
  </div>
  <div class="col col-sm-2 my-2">
    <a href="/admin/file" class="btn btn-primary btn-block"><i class="bi bi-folder h1"></i><br>File</a>
  </div>
  <div class="col col-sm-2 my-2">
    <a href="/admin/scheduling" class="btn btn-info btn-block"><i class="bi bi-clock h1"></i><br>Scheduling</a>
  </div>
  <div class="col col-sm-2 my-2">
    <a href="/admin/email-temp" class="btn btn-dark btn-block"><i class="bi bi-envelope-paper h1"></i><br>Campaigns</a>
  </div>
  <div class="col col-sm-2 my-2">
    <a href="/admin/freetrial" class="btn btn-primary btn-block"><i class="bi bi-person-plus h1"></i><br>Freetrial</a>
  </div>
  <div class="col my-2">
    <a href="/admin/user" class="btn btn-light border-secondary btn-block"><i class="bi bi-people h1"></i><br>Users</a>
  </div>
  <div class="col my-2">
    <a href="/admin/company" class="btn btn-light btn-block"><i class="bi bi-building h1"></i><br>Company</a>
  </div>
  {% if user.acs == 9 %}
  <div class="col my-2">
    <a href="{% if user.acs == 9 %}/admin/acs{% else %}#{% endif %}" class="btn btn-light btn-block"><i class="bi bi-key h1"></i><br>Staff</a>
  </div>
  {% endif %}
  <div class="col my-2">
    <a href="/admin/inv" class="btn btn-light btn-block"><i class="bi bi-receipt-cutoff h1"></i><br>Invoice</a>
  </div>
  <div class="col my-2">
    <a href="/admin/price" class="btn btn-light btn-block"><i class="bi bi-coin h1"></i><br>Price</a>
  </div>
  <div class="col my-2">
    <a href="/admin/news/post?cats[]={{ cat_event }}" class="btn btn-light btn-block"><i class="bi bi-calendar2-event h1"></i><br>Event</a>
  </div>
  <div class="col my-2">
    <a href="/admin/attendee" class="btn btn-light btn-block"><i class="bi bi-person-badge h1"></i><br>Attendees</a>
  </div>
  <div class="col my-2">
    <a href="/admin/stat" class="btn btn-light btn-block"><i class="bi bi-pie-chart h1"></i><br>Top Articles</a>
  </div>
  <div class="col my-2">
    <a href="/admin/stat/tag" class="btn btn-light btn-block"><i class="bi bi-bar-chart-line h1"></i><br>Page hits</a>
  </div>

</div>

<h2 class="my-4">Latest Email Campaigns</h2>

<table class="table">
  <thead class="thead-light">
    <tr>
      <th>Subject</th>
      <th>Date Time</th>
      <th>Emails</th>
      <th>Sent</th>
      <th>Stop</th>
      <th>Error</th>
      <!--th >Bounced</th-->
      <th>Opened</th>
      <th>Clicked</th>
      <th>unsub</th>
    </tr>
  </thead>
  {% for c in camp %}
  <tr>
    <td>
      <!--<b class="mono">{{ c.typ }}</b>-->
      <a href="/admin/camp-preview/{{ c.id }}" target="_blank" title="{{ c.subject }}"> {#<!-- data-toggle="modal" data-target="#cp{{ c.id }}{{ c.typ }}"-->#} 
      {% if c.typ == 'D' %}
        <i class="bi bi-clock text-dark float-left mr-2 mb-2" data-toggle="tooltip" data-placement="top" title="Daily Email"></i>
      {% elseif c.typ == 'W' %}
        <i class="bi bi-calendar2-week text-dark float-left mr-2 mb-2" data-toggle="tooltip" data-placement="top" title="Weekly Email"></i>
      {% elseif c.typ == 'M' %}
        <i class="bi bi-envelope-paper text-dark float-left mr-2 mb-2" data-toggle="tooltip" data-placement="top" title="Manual Email"></i>
      {% else %}
        <i class="bi bi-alarm text-dark float-left mr-2 mb-2" data-toggle="tooltip" data-placement="top" title="Instant Email"></i>
      {% endif %}
      {{ c.subject }}</a>
    </td>
    <td class="text-nowrap">{{ c.ts | date('d-M-Y H:i') }}{% if c.num and c.ts != c.ts_send %}<div class="small text-black-50" title="Send on">{{ c.ts_send | date('d-M-Y H:i') }}</div>{% endif %}</td>
    <td>{{ c.num | number_format(0, '.', ',') }}</td>
    <td>{{ c.sent | number_format(0, '.', ',') }}{% if c.num %}<div class="small text-black-50">{{ (c.sent / c.num * 100) | round(1) }}%</div>{% endif %}</td>
    <td class="text-danger">{% if c.stop %}{{ c.stop | number_format(0, '.', ',') }}{% endif %}</td>
    <td>{{ c.err }}{% if c.num %}<div class="small text-black-50">{{ (c.err / c.num * 100) | round(1) }}%</div>{% endif %}</td>
    <!--td>{{ c.bounced }}{% if c.num %}<div class="small text-muted">{{ (c.bounced / c.num * 100) | round(1) }}%</div>{% endif %}</td-->
    <td>{{ c.opened | number_format(0, '.', ',') }}{% if c.num %}<div class="small text-black-50">{{ (c.opened / c.num * 100) | round(1) }}%</div>{% endif %}</td>
    <td>{{ c.clicked | number_format(0, '.', ',') }}{% if c.num %}<div class="small text-black-50">{{ (c.clicked / c.num * 100) | round(1) }}%</div>{% endif %}</td>
    <td>{{ c.unsub }}{% if c.num %}<div class="small text-black-50">{{ (c.unsub / c.num * 100) | round(1) }}%</div>{% endif %}</td>
  </tr>
  {% endfor %}
</table>

<script>
$('a[href^="/admin/camp-preview/"]').on('click', function(e){
  e.preventDefault();
  var url = $(this).attr('href');
  var modalTitle = $(this).attr('title') || 'Campaign Preview';

  // Update modal title with the link's title attribute
  $('#campaignModalLabel').text(modalTitle);
  
  // Insert an iframe into the modal body that loads the external URL
  $('#campaignModal .modal-body').html(
    '<iframe src="' + url + '" frameborder="0" style="width: 100%; height: 80vh"></iframe>'
  );
  
  // Show the modal
  $('#campaignModal').modal('show');
});
</script>

<!-- Campaign Preview Modal -->
<div class="modal fade" id="campaignModal" tabindex="-1" role="dialog" aria-labelledby="campaignModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header pt-2 pb-2">
        <h5 class="modal-title" id="campaignModalLabel">Campaign Preview</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body p-0">
        <!-- Loaded content will appear here -->
      </div>
    </div>
  </div>
</div>
