{# editor.tpl #}

<!-- H1 Title -->
<h1 class="mb-4">Campaign Editor / Builder</h1>

<!-- Sub-Navigation -->
{% include '/Admin/Campaign/inc-nav.tpl' %}

<!-- Main Page Content -->

<!-- Optional custom styles -->
<style>
  /* Compact layout adjustments */
  .form-group {
    margin-bottom: 0.75rem;
  }
  .template-region {
    min-height: 150px;
    background-color: #f8f9fa;
    border: 2px dashed #ccc;
    padding: 1rem;
    margin-top: 1rem;
  }
  .draggable-element {
    border: 1px solid #ccc;
    padding: 0.5rem 1rem;
    margin-bottom: 0.5rem;
    background-color: #fff;
    cursor: move;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .draggable-element .block-content {
    flex: 1;
    margin-right: 0.5rem;
  }
  .draggable-element .block-actions {
    white-space: nowrap;
  }
  /* Preview Modal styles */
  .preview-content {
    background: #fff;
    border: 1px solid #ddd;
    padding: 1rem;
    max-height: 400px;
    overflow-y: auto;
  }
</style>

<!-- Main form for campaign creation -->
<form id="campaignForm" action="#" method="post">
  <div class="row">
    <!-- Left Column -->
    <div class="col-md-6">
      <!-- Choose Template -->
      <div class="form-group">
        <label for="templateSelect">Choose Template</label>
        <select class="form-control form-control-sm" id="templateSelect" name="templateSelect">
          <option value="">-- Select a template --</option>
          <option value="template1">Template 1</option>
          <option value="template2">Template 2</option>
        </select>
      </div>

      <!-- Choose Audience -->
      <div class="form-group">
        <label for="audienceSelect">Choose Audience</label>
        <select class="form-control form-control-sm" id="audienceSelect" name="audienceSelect">
          <option value="">-- Select an audience --</option>
          <option value="audience1">Audience 1 (123 subscribers)</option>
          <option value="audience2">Audience 2 (456 subscribers)</option>
        </select>
        <small class="form-text text-muted">
          Approx. <span id="audienceSize">0</span> subscribers will receive this campaign.
        </small>
      </div>
    </div>

    <!-- Right Column -->
    <div class="col-md-6">
      <!-- Campaign Name -->
      <div class="form-group">
        <label for="campaignName">Campaign Name</label>
        <input 
          type="text" 
          class="form-control form-control-sm" 
          id="campaignName" 
          name="campaignName" 
          placeholder="Enter campaign name"
          required
        >
      </div>

      <!-- Email Subject -->
      <div class="form-group">
        <label for="emailSubject">Email Subject</label>
        <input 
          type="text" 
          class="form-control form-control-sm" 
          id="emailSubject" 
          name="emailSubject" 
          placeholder="Enter email subject"
          required
        >
      </div>
    </div>
  </div>

  <!-- Preview Content & Delivery Options -->
  <div class="row">
    <div class="col-12 col-md-8">
      <div class="form-group">
        <label for="emailPreviewContent">Email Preview Content</label>
        <textarea 
          class="form-control form-control-sm" 
          id="emailPreviewContent" 
          name="emailPreviewContent" 
          rows="2"
          placeholder="Enter a short preview text"
        ></textarea>
        <small class="form-text text-muted">
          This text will appear as a snippet in some email clients (e.g., inbox preview).
        </small>
      </div>
    </div>

    <div class="col-12 col-md-4">
      <div class="form-group mb-0">
        <label>Delivery Options</label>
        <div class="form-check">
          <input 
            class="form-check-input" 
            type="radio" 
            name="deliveryOption" 
            id="sendNow" 
            value="now" 
            checked
          >
          <label class="form-check-label" for="sendNow">Send Immediately</label>
        </div>
        <div class="form-check">
          <input 
            class="form-check-input" 
            type="radio" 
            name="deliveryOption" 
            id="scheduleLater" 
            value="later"
          >
          <label class="form-check-label" for="scheduleLater">Schedule for Later</label>
        </div>
      </div>
    </div>
  </div>

  <!-- Drag and Drop Email Builder -->
  <div class="row mt-3">
    <div class="col-12">
      <h5>Drag &amp; Drop Email Builder</h5>
      <p class="small text-muted mb-2">
        Use the buttons below to add content blocks. Drag them to reorder. 
        Click <strong>Edit</strong> to modify block content (with markdown support) or <strong>Remove</strong> to delete.
      </p>
      <div class="builder-tools mb-2">
        <button 
          type="button" 
          class="btn btn-outline-primary btn-sm mr-2"
          id="addSectionBtn"
        >
          Add Section
        </button>
        <button 
          type="button" 
          class="btn btn-outline-primary btn-sm mr-2"
          id="addArticleBtn"
        >
          Add News Article
        </button>
        <button 
          type="button" 
          class="btn btn-outline-primary btn-sm mr-2"
          id="insertSnippetBtn"
        >
          Insert Layout Snippet
        </button>
        <button 
          type="button" 
          class="btn btn-outline-info btn-sm"
          id="previewEmailBtn"
        >
          Preview Email
        </button>
      </div>
      <div id="templateRegion" class="template-region"></div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="row mt-3">
    <div class="col-12">
      <button 
        type="button" 
        class="btn btn-primary mr-2" 
        data-toggle="modal" 
        data-target="#scheduleModal"
        id="openScheduleModalBtn"
        disabled
      >
        Schedule
      </button>
      <button 
        type="submit" 
        class="btn btn-success"
        id="saveCampaignBtn"
      >
        Save Campaign
      </button>
    </div>
  </div>
</form>

<!-- =================================== -->
<!-- Modal: Schedule Campaign -->
<!-- =================================== -->
<div 
  class="modal fade" 
  id="scheduleModal" 
  tabindex="-1" 
  aria-labelledby="scheduleModalLabel" 
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="scheduleModalLabel">Schedule Campaign</h5>
        <button 
          type="button" 
          class="close" 
          data-dismiss="modal" 
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <!-- Fields for scheduling date/time -->
        <div class="form-group">
          <label for="scheduleDate">Date</label>
          <input 
            type="date" 
            class="form-control" 
            id="scheduleDate" 
            name="scheduleDate"
          >
        </div>
        <div class="form-group">
          <label for="scheduleTime">Time</label>
          <input 
            type="time" 
            class="form-control" 
            id="scheduleTime" 
            name="scheduleTime"
          >
        </div>
      </div>
      <div class="modal-footer">
        <button 
          type="button" 
          class="btn btn-secondary" 
          data-dismiss="modal"
        >
          Close
        </button>
        <button 
          type="button" 
          class="btn btn-primary" 
          id="confirmScheduleBtn"
        >
          Confirm Schedule
        </button>
      </div>
    </div>
  </div>
</div>

<!-- =================================== -->
<!-- Modal: Edit Block Content (Markdown Editor) -->
<!-- =================================== -->
<div 
  class="modal fade" 
  id="editBlockModal" 
  tabindex="-1" 
  aria-labelledby="editBlockModalLabel" 
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editBlockModalLabel">Edit Block Content (Markdown)</h5>
        <button 
          type="button" 
          class="close" 
          data-dismiss="modal" 
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <!-- The textarea will be enhanced with EasyMDE -->
        <textarea 
          class="form-control" 
          id="blockContentTextarea" 
          rows="5"
        ></textarea>
      </div>
      <div class="modal-footer">
        <button 
          type="button" 
          class="btn btn-secondary" 
          data-dismiss="modal"
        >
          Cancel
        </button>
        <button 
          type="button" 
          class="btn btn-primary" 
          id="saveBlockContentBtn"
        >
          Save
        </button>
      </div>
    </div>
  </div>
</div>

<!-- =================================== -->
<!-- Modal: Insert Layout Snippet -->
<!-- =================================== -->
<div 
  class="modal fade" 
  id="insertSnippetModal" 
  tabindex="-1" 
  aria-labelledby="insertSnippetModalLabel" 
  aria-hidden="true"
>
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="insertSnippetModalLabel">Insert Layout Snippet</h5>
        <button 
          type="button" 
          class="close" 
          data-dismiss="modal" 
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="list-group">
          <button type="button" class="list-group-item list-group-item-action snippet-option" data-snippet="two_column">
            Two Column Layout
          </button>
          <button type="button" class="list-group-item list-group-item-action snippet-option" data-snippet="image_text">
            Image with Text
          </button>
          <button type="button" class="list-group-item list-group-item-action snippet-option" data-snippet="full_width">
            Full Width Section
          </button>
          <button type="button" class="list-group-item list-group-item-action snippet-option" data-snippet="call_to_action">
            Call to Action Block
          </button>
        </div>
      </div>
      <div class="modal-footer">
        <button 
          type="button" 
          class="btn btn-secondary" 
          data-dismiss="modal"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</div>

<!-- =================================== -->
<!-- Modal: Preview Email -->
<!-- =================================== -->
<div 
  class="modal fade" 
  id="previewEmailModal" 
  tabindex="-1" 
  aria-labelledby="previewEmailModalLabel" 
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="previewEmailModalLabel">Email Preview</h5>
        <button 
          type="button" 
          class="close" 
          data-dismiss="modal" 
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body preview-content">
        <!-- Email preview content will be injected here -->
      </div>
      <div class="modal-footer">
        <button 
          type="button" 
          class="btn btn-secondary" 
          data-dismiss="modal"
        >
          Close Preview
        </button>
      </div>
    </div>
  </div>
</div>

<!-- =================================== -->
<!-- External Libraries -->
<!-- =================================== -->
<!-- Select2 (for searchable dropdowns) -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- Marked (for Markdown to HTML conversion) -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<!-- EasyMDE (Markdown editor) - already included in header, but ensure it's loaded -->
<!--
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/easymde/dist/easymde.min.css">
<script src="https://cdn.jsdelivr.net/npm/easymde/dist/easymde.min.js"></script>
-->

<script>
/* 
  JavaScript to enhance the email builder:
  - Initialize Select2 on dropdowns.
  - Toggle scheduling.
  - Add new content blocks (with markdown support).
  - Insert pre-defined layout snippets.
  - Provide a preview modal for the email.
  
  A Markdown editor (EasyMDE) is initialized in the edit block modal,
  and the Marked library converts markdown to HTML.
*/
(function($) {
  let currentEditBlock = null;
  let mde = null; // EasyMDE instance

  $(document).ready(function() {

    // Initialize Select2 for searchable dropdowns
    $('#templateSelect, #audienceSelect').select2({
      placeholder: 'Type to search or select...'
    });

    // Toggle Schedule button based on radio selection
    $('input[name="deliveryOption"]').on('change', function() {
      $('#openScheduleModalBtn').prop('disabled', !$('#scheduleLater').is(':checked'));
    });

    // Update audience subscriber count
    $('#audienceSelect').on('change', function() {
      var selected = $(this).find('option:selected').text();
      var countMatch = selected.match(/\((\d+)\s+subscribers\)/);
      var count = countMatch ? countMatch[1] : 0;
      $('#audienceSize').text(count);
    });

    // Confirm schedule
    $('#confirmScheduleBtn').on('click', function() {
      $('#scheduleModal').modal('hide');
    });

    // Add new content blocks via buttons
    $('#addSectionBtn').on('click', function() {
      // For plain text blocks, markdown equals the text
      addBlock('<p>New Section</p>', 'New Section');
    });
    $('#addArticleBtn').on('click', function() {
      addBlock('<p>New Article</p>', 'New Article');
    });

    // Insert Layout Snippet button opens the snippet modal
    $('#insertSnippetBtn').on('click', function() {
      $('#insertSnippetModal').modal('show');
    });

    // Handle snippet option selection
    $(document).on('click', '.snippet-option', function() {
      let snippetType = $(this).data('snippet');
      let snippet = getSnippetContent(snippetType);
      addBlock(snippet.html, snippet.markdown);
      $('#insertSnippetModal').modal('hide');
    });

    // Preview Email button
    $('#previewEmailBtn').on('click', function() {
      let previewHtml = $('#templateRegion').html();
      $('#previewEmailModal .preview-content').html(previewHtml);
      $('#previewEmailModal').modal('show');
    });

    // Function to add a new block.
    // content: HTML to display, markdownContent: raw markdown to store.
    function addBlock(content, markdownContent) {
      let blockId = 'block_' + Date.now();
      let blockHtml = `
        <div class="draggable-element" data-block-id="${blockId}" data-markdown="${encodeURIComponent(markdownContent)}">
          <div class="block-content">${content}</div>
          <div class="block-actions">
            <button type="button" class="btn btn-sm btn-outline-secondary mr-1 edit-block-btn">Edit</button>
            <button type="button" class="btn btn-sm btn-outline-danger remove-block-btn">Remove</button>
          </div>
        </div>
      `;
      $('#templateRegion').append(blockHtml);
    }

    // Pre-defined layout snippets
    function getSnippetContent(snippetType) {
      switch(snippetType) {
        case 'two_column':
          return {
            html: '<div class="row"><div class="col-md-6">Column 1 content...</div><div class="col-md-6">Column 2 content...</div></div>',
            markdown: 'Column 1 content... | Column 2 content...'
          };
        case 'image_text':
          return {
            html: '<div class="row"><div class="col-md-4"><img src="https://via.placeholder.com/150" alt="Image" class="img-fluid"></div><div class="col-md-8">Your text here...</div></div>',
            markdown: '![Image](https://via.placeholder.com/150)\n\nYour text here...'
          };
        case 'full_width':
          return {
            html: '<div class="container-fluid"><p>Full width content here...</p></div>',
            markdown: 'Full width content here...'
          };
        case 'call_to_action':
          return {
            html: '<div class="text-center p-3 bg-primary text-white"><h4>Call to Action</h4><p>Click here!</p></div>',
            markdown: '## Call to Action\n\nClick here!'
          };
        default:
          return {
            html: '<p>Snippet not found</p>',
            markdown: 'Snippet not found'
          };
      }
    }

    // Handle block editing: initialize EasyMDE for markdown editing.
    $(document).on('click', '.edit-block-btn', function() {
      let $block = $(this).closest('.draggable-element');
      currentEditBlock = $block;
      // Retrieve stored markdown (decoded)
      let markdownContent = decodeURIComponent($block.attr('data-markdown'));
      // Initialize EasyMDE if not already done
      if (!mde) {
        mde = new EasyMDE({ 
          element: document.getElementById('blockContentTextarea'),
          autoDownloadFontAwesome: false,
          spellChecker: false,
        });
      }
      mde.value(markdownContent);
      $('#editBlockModal').modal('show');
    });

    // Save updated block content from the markdown editor
    $('#saveBlockContentBtn').on('click', function() {
      if (currentEditBlock && mde) {
        let updatedMarkdown = mde.value();
        let updatedHtml = marked.parse(updatedMarkdown);
        currentEditBlock.find('.block-content').html(updatedHtml);
        currentEditBlock.attr('data-markdown', encodeURIComponent(updatedMarkdown));
      }
      $('#editBlockModal').modal('hide');
    });

    // Remove block
    $(document).on('click', '.remove-block-btn', function() {
      $(this).closest('.draggable-element').remove();
    });

    // Basic drag and drop reordering (very simple approach)
    let isDragging = false, dragSrcEl = null;
    $(document).on('mousedown', '.draggable-element', function() {
      isDragging = true;
      dragSrcEl = $(this);
    });
    $(document).on('mouseup', function() {
      isDragging = false;
      dragSrcEl = null;
    });
    $(document).on('mousemove', '.draggable-element', function() {
      if (isDragging && dragSrcEl) {
        let $hoverEl = $(this);
        if ($hoverEl.is(dragSrcEl)) return;
        let dragSrcIndex = dragSrcEl.index();
        let hoverIndex = $hoverEl.index();
        if (dragSrcIndex < hoverIndex) {
          $hoverEl.after(dragSrcEl);
        } else {
          $hoverEl.before(dragSrcEl);
        }
      }
    });

  });
})(jQuery);
</script>
