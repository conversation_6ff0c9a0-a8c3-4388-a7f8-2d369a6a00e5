<!-- ASSETS * ASSETS * ASSETS * ASSETS * ASSETS * ASSETS * ASSETS -->
<!-- ------------------------------------------------------------ -->

<!-- Bootstrap Icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/2.2.2/js/dataTables.js"></script>

<!-- DataTables BS4.6 JS + CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/2.2.2/css/dataTables.bootstrap4.css">
<script src="https://cdn.datatables.net/2.2.2/js/dataTables.bootstrap4.js"></script>

<!-- ------------------------------------------------------------ -->

<style>
  .nowrap { white-space: nowrap; }
  table.dataTable td { padding: 4px 8px; }
</style>

<!-- inc-nav.tpl -->
<!-- Sub-Navigation -->
<style>
  /* Optional custom CSS for sub-navigation styling */
  .nav-tabs .nav-link {
    padding: 0.75rem 1.25rem;
  }
  .nav-tabs .nav-link.active {
    font-weight: bold;
    color: #495057;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
  }
</style>

<ul class="nav nav-tabs mb-4">
  <li class="nav-item">
    <a class="nav-link" href="/admin/preview?tpl=/Admin/Campaign/scheduled">
      <i class="bi bi-calendar-check"></i> Scheduled
    </a>
  </li>
  <li class="nav-item">
    <a class="nav-link" href="/admin/preview?tpl=/Admin/Campaign/sent">
      <i class="bi bi-send"></i> Sent
    </a>
  </li>
  <li class="nav-item">
    <a class="nav-link" href="/admin/preview?tpl=/Admin/Campaign/editor">
      <i class="bi bi-pencil-square"></i> Campaign Editor
    </a>
  </li>
  <li class="nav-item">
    <a class="nav-link" href="/admin/preview?tpl=/Admin/Campaign/templates">
      <i class="bi bi-layout-text-window-reverse"></i> Templates
    </a>
  </li>
  <li class="nav-item">
    <a class="nav-link" href="/admin/preview?tpl=/Admin/Campaign/recipients">
      <i class="bi bi-people"></i> Recipients
    </a>
  </li>
  <!-- Right-aligned Filter Toggle Button -->
  <li class="nav-item ml-auto">
    <button type="button" class="btn btn-outline-secondary" data-toggle="collapse" data-target="#filterForm" aria-expanded="false" aria-controls="filterForm">
      <i class="bi bi-filter-circle"></i> Filter
    </button>
  </li>
</ul>

<script>
  // jQuery script to highlight the active tab based on the current URL
  $(document).ready(function() {
    var currentUrl = window.location.href;
    $('.nav-tabs .nav-link').each(function() {
      var linkUrl = $(this).attr('href');
      if (currentUrl.indexOf(linkUrl) !== -1) {
        $('.nav-tabs .nav-link').removeClass('active');
        $(this).addClass('active');
      }
    });
  });
</script>
