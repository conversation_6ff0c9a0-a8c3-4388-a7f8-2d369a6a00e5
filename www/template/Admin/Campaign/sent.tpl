{# sent.tpl #}

<h1 class="mb-4">Sent Email Campaigns</h1>
{% include '/Admin/Campaign/inc-nav.tpl' %}

<form id="filterForm" class="card card-body mb-5 bg-light collapse">
  <div class="form-row">
    <div class="form-group col-md-3">
      <label for="dateFrom" class="font-weight-bold">Date From</label>
      <input type="date" class="form-control" id="dateFrom" name="dateFrom">
    </div>
    <div class="form-group col-md-3">
      <label for="dateTo" class="font-weight-bold">Date To</label>
      <input type="date" class="form-control" id="dateTo" name="dateTo">
    </div>
    <div class="form-group col-md-3">
      <label for="type" class="font-weight-bold">Type</label>
      <select id="type" class="form-control" name="type">
        <option value="">All</option>
        <option value="News">News</option>
        <option value="Invoice">Invoice</option>
        <option value="Free Trial">Free Trial</option>
      </select>
    </div>
    <div class="form-group col-md-3">
      <label for="status" class="font-weight-bold">Status</label>
      <select id="status" class="form-control" name="status">
        <option value="">All</option>
        <option value="Sent">Sent</option>
        <option value="Error">Error</option>
      </select>
    </div>
  </div>
  <div class="d-flex justify-content-end">
    <button type="submit" class="btn btn-primary mr-2">Apply Filters</button>
    <button type="reset" class="btn btn-secondary">Reset Filters</button>
  </div>
</form>

<table id="campaignTable" class="table table-striped table-bordered my-2">
  <thead>
    <tr>
      <th></th>
      <th>Email Subject</th>
      <th>Campaign Sent</th>
      <th class="text-center"><i class="bi bi-send" data-toggle="tooltip" title="Sent"></i></th>
      <th class="text-center"><i class="bi bi-exclamation-circle" data-toggle="tooltip" title="Error"></i></th>
      <th class="text-center"><i class="bi bi-envelope-open" data-toggle="tooltip" title="Open"></i></th>
      <th class="text-center"><i class="bi bi-link-45deg" data-toggle="tooltip" title="Click"></i></th>
      <th class="text-center"><i class="bi bi-person-dash" data-toggle="tooltip" title="Unsub"></i></th>
      <th>Status</th>
    </tr>
  </thead>
  <tbody>
    {# Data will be loaded via DataTables AJAX #}
  </tbody>
</table>

<!-- Developer Notes -->
<div id="devNotes" class="card card-body bg-light mt-4">
  <h5 class="mb-3 text-muted">Notes</h5>
  <p>See: "Latest Email Campaigns" - /admin and "Email Campaigns using this template"</p>
  <ul class="mb-0">
    <li>Instant: /admin/email-temp/1</li>
    <li>Daily: /admin/email-temp/2</li>
    <li>Weekly: /admin/email-temp/3</li>
  </ul>
</div>

<!-- Modal for Campaign Preview -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="previewModalLabel">Campaign Preview</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" id="campaignContent">
        {# Preview details will be injected here #}
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<script>
// Helper function to parse the start_time string ("11-Feb-2025 06:19")
function parseDate(str) {
  if (!str) return null;
  var parts = str.split(' ');
  if (parts.length < 1) return null;
  var datePart = parts[0]; // e.g., "11-Feb-2025"
  var dateParts = datePart.split('-');
  if (dateParts.length !== 3) return null;
  var day = parseInt(dateParts[0], 10);
  var monthStr = dateParts[1];
  var year = parseInt(dateParts[2], 10);
  var monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
  var month = monthNames.findIndex(function(m) { return m.toLowerCase() === monthStr.toLowerCase(); });
  if (month < 0) return null;
  return new Date(year, month, day);
}

// Custom filtering function for DataTables
$.fn.dataTable.ext.search.push(function(settings, data, dataIndex, rowData) {
  var dateFrom = $('#dateFrom').val();
  var dateTo = $('#dateTo').val();
  var filterEmailType = $('#type').val();
  var filterStatus = $('#status').val();
  
  var startTimeStr = rowData.start_time;
  var rowDate = parseDate(startTimeStr);
  
  if (dateFrom) {
    var filterFrom = new Date(dateFrom);
    if (rowDate && rowDate < filterFrom) {
      return false;
    }
  }
  
  if (dateTo) {
    var filterTo = new Date(dateTo);
    if (rowDate && rowDate > filterTo) {
      return false;
    }
  }
  
  if (filterEmailType && rowData.email_type.toLowerCase() !== filterEmailType.toLowerCase()) {
    return false;
  }
  
  if (filterStatus && rowData.status.toLowerCase() !== filterStatus.toLowerCase()) {
    return false;
  }
  
  return true;
});

$(document).ready(function() {
  var table = $('#campaignTable').DataTable({
    ajax: {
      url: '/files/campaign.json',
      dataSrc: ''
    },
    order: [[2, 'desc']], // Default sort on "Campaign Sent" (start_time) descending
    columns: [
      // Column 1: Email type as icon with tooltip (no header text)
      {
        data: 'email_type',
        className: 'text-center',
        render: function(data, type, row) {
          if (typeof data !== 'string') {
            return '';
          }
          var lowerType = data.toLowerCase();
          var tooltip = data;
          var icon = '';
          switch (lowerType) {
            case 'news':
              icon = '<i class="bi bi-newspaper" title="' + tooltip + '" data-toggle="tooltip"></i>';
              break;
            case 'invoice':
              icon = '<i class="bi bi-file-earmark-text" title="' + tooltip + '" data-toggle="tooltip"></i>';
              break;
            case 'free trial':
            case 'free_trial':
              icon = '<i class="bi bi-gift" title="' + tooltip + '" data-toggle="tooltip"></i>';
              break;
            default:
              icon = '<i class="bi" title="' + tooltip + '" data-toggle="tooltip"></i>';
          }
          return icon;
        }
      },
      // Column 2: Email Subject with Campaign info underneath
      {
        data: null,
        render: function(data, type, row) {
          return '<a href="#" class="preview-link" data-id="' + row.id + '">' + row.subject + '</a>' +
                 '<br><small class="text-muted">' + row.campaign + '</small>';
        }
      },
      // Column 3: Campaign Sent column: start_time with custom sort
      {
        data: 'start_time',
        className: 'text-nowrap',
        render: function(data, type, row) {
          if(type === 'sort'){
            var d = parseDate(data);
            return d ? d.getTime() : 0;
          }
          return data;
        }
      },
      // Column 4: Sent column: display sent value; no sorting.
      {
        data: 'sent',
        className: 'text-center',
        orderable: false
      },
      // Column 5: Error column (using "stop"), center aligned, no sorting.
      {
        data: 'stop',
        className: 'text-center',
        orderable: false
      },
      // Column 6: Open column, center aligned, no sorting.
      {
        data: 'opened',
        className: 'text-center',
        orderable: false
      },
      // Column 7: Click column, center aligned, no sorting.
      {
        data: 'clicked',
        className: 'text-center',
        orderable: false
      },
      // Column 8: Unsub column, center aligned, no sorting.
      {
        data: 'unsub',
        className: 'text-center',
        orderable: false
      },
      // Column 9: Status column with badge classes, center aligned.
      {
        data: 'status',
        className: 'text-center',
        render: function(data, type, row) {
          var badgeClass = 'badge ';
          if (data.toLowerCase() === 'sent') {
            badgeClass += 'badge-success';
          } else if (data.toLowerCase() === 'error') {
            badgeClass += 'badge-danger';
          } else {
            badgeClass += 'badge-secondary';
          }
          return '<span class="' + badgeClass + '">' + data + '</span>';
        }
      }
    ]
  });
  
  // Reinitialize tooltips on every table draw for dynamically added elements.
  table.on('draw', function() {
    $('[data-toggle="tooltip"]').tooltip();
  });
  
  // Filter form submission: redraw table on submit.
  $('#filterForm').on('submit', function(e) {
    e.preventDefault();
    table.draw();
  });
  
  // Reset filters: clear form values and redraw the table.
  $('#filterForm').on('reset', function(e) {
    setTimeout(function() {
      table.draw();
    }, 0);
  });
  
  // Preview modal: load campaign details when a preview link is clicked.
  $('#campaignTable tbody').on('click', '.preview-link', function(e) {
    e.preventDefault();
    var tr = $(this).closest('tr');
    var row = table.row(tr).data();
    var previewHtml = '<div class="row">';
    previewHtml += '  <div class="col-sm-6">';
    previewHtml += '    <dl class="row">';
    previewHtml += '      <dt class="col-sm-5">Start Time:</dt><dd class="col-sm-7">' + row.start_time + '</dd>';
    previewHtml += '      <dt class="col-sm-5">End Time:</dt><dd class="col-sm-7">' + row.end_time + '</dd>';
    previewHtml += '      <dt class="col-sm-5">Sent:</dt><dd class="col-sm-7">' + row.sent + ' (' + row.sent_pct + ')</dd>';
    previewHtml += '      <dt class="col-sm-5">Error:</dt><dd class="col-sm-7">' + row.stop + ' (' + row.stop_pct + ')</dd>';
    previewHtml += '    </dl>';
    previewHtml += '  </div>';
    previewHtml += '  <div class="col-sm-6">';
    previewHtml += '    <dl class="row">';
    previewHtml += '      <dt class="col-sm-5">Open:</dt><dd class="col-sm-7">' + row.opened + ' (' + row.opened_pct + ')</dd>';
    previewHtml += '      <dt class="col-sm-5">Click:</dt><dd class="col-sm-7">' + row.clicked + ' (' + row.clicked_pct + ')</dd>';
    previewHtml += '      <dt class="col-sm-5">Unsub:</dt><dd class="col-sm-7">' + row.unsub + ' (' + row.unsub_pct + ')</dd>';
    previewHtml += '      <dt class="col-sm-5">Status:</dt><dd class="col-sm-7">' + row.status + '</dd>';
    previewHtml += '    </dl>';
    previewHtml += '  </div>';
    previewHtml += '</div>';
    previewHtml += '<hr>';
    previewHtml += '<p class="mb-0"><strong>Email Body:</strong></p>';
    previewHtml += '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>';
    $('#previewModalLabel').text(row.subject);
    $('#campaignContent').html(previewHtml);
    $('#previewModal').modal('show');
  });
});
</script>
