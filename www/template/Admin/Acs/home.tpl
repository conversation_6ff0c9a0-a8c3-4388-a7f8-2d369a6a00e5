<h1>Staff access dashboard</h1>

{% if msg %}
<p class="b-gold rc">{{ msg | raw }}</p>
{% endif %}

{% if grps %}

<table class="table box">
<tr class="bg"><td></td>
{% for gid,grp in grps %}
  <td>{{ grp }}</td>
{% endfor %}
</tr>
{% for u in users %}
<tr data-uid="{{ u.uid }}">
  <td><a href="/admin/user/{{ u.uid }}">{{ u.fname }} {{ u.lname }}</a></td>
{% for gid,grp in grps %}
  <td class="ac"><input type="checkbox" {% if gid in u.acs_staff %} checked{% endif %} data-gid="{{ gid }}" class="acs"></td>
{% endfor %}
</tr>
{% endfor %}
</table>

{% endif %}

<script>
$(function() {
  $('input.acs').on('click', function() {
    var uid = $(this).parents('tr').data('uid');
    var gid = $(this).data('gid');
    var acs = $(this).is(':checked') ? 1 : 0;
    $.get('/admin/acs/upd?uid=' + uid + '&gid=' + gid + '&acs=' + acs);
  });
})
</script>

<h2>Who can access which pages</h2>

<ol>
  <li>/admin/acs<br>This page can only be updated by: <b>SYS Admin</b> or <b>Director</b></li>
  <li>/admin/user: search index page: Download CSV
    <br><b>Director</b> can download any purchaser's users
    <br><b>Download Customer</b> can download all users of ONE purchaser at a time -- however no front access</li>
  <li>/admin/company: Download CSV
    <br><b>Director</b> can download any purchaser's users</li>
  <li>/admin/inv/profile/xxx: Purchaser profile
    <br><b>Director</b> or <b>Download Customer</b> can download all users of the purchaser</li>
</ol>
