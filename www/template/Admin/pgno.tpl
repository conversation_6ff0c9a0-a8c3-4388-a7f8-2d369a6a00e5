{% if pgno_max > 1 %}
<ol class="pgno bg rc" data-id="pgno" data-url="{{ pgno_url }}" data-param="{{ pgno_param }}">
  {% if pgno > 4 %}
    <li data-pgno="1">« First</li>
  {% endif %}
  {% set fm = max(min(pgno - 3, pgno_max - 6), 1) %}
  {% set to = min(max(pgno + 3, 7), pgno_max) %}
  {% for i in fm..to %}
    <li{% if i == pgno %} class="bg-grey"{% endif %}>{{ i }}</li>
  {% endfor %}
  {% if pgno < pgno_max - 3 %}
    <li data-pgno="{{ pgno_max }}">Last »</li>
  {% endif %}
</ol>
{% endif %}
