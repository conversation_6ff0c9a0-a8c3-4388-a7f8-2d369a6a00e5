<h1>User sessions{% if pgno_uid %} for UID {{ pgno_uid }}{% endif %}</h1>

{% if stat %}
  <table class="table">
    <tr class="bg"><td>UID</td><td>Name</td><td>IP</td><td>PCID</td><td>Date time</td><td></td><td></td></tr>
  {% for s in stat %}
    <tr>
      <td><a href="/admin/session?uid={{ s.uid }}">{{ s.uid }}</td>
      <td><a href="/admin/user/{{ s.uid }}">{{ names[s.uid]['fname'] }} {{ names[s.uid]['lname'] }}</a> ({{ names[s.uid]['email'] }})</td>
      <td>{{ s.ip }}</td>
      <td>{{ s.pcid }}</td>
      <td>{{ s.ts | date('d-M-Y H:i') }}</td>
      <td><a href="/admin/session?uid={{ pgno_uid }}&del={{ s.id }}" class="confirm" data-confirm="Are you sure to delete this session of the user?">Delete</a></td>
      <td><a href="/admin/stat/tag?uid={{ s.uid }}&cmd=User">Stat</a></td>
    </tr>
  {% endfor %}
  </table>
{% else %}
  <p class="rc b-green">No session found during last 30 days</p><br>
{% endif %}

{{ include('/Admin/pgno.tpl') }}
