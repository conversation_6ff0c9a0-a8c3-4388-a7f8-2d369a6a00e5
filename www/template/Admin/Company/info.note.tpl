<div class="fr">
  <a href="?" class="rc bg{% if note == '' %} b{% endif %}" title="Notes only">Notes</a>
  <a href="?note=log" class="rc bg{% if note == 'log' %} b{% endif %}" title="Notes and Logs">Logs</a>
  <a href="?note=all" class="rc bg{% if note == 'all' %} b{% endif %}" title="All msg incl. deleted and hidden">All</a>
</div>

<h2>All Notes for this Organisation</h2>

<form action="/admin/company/{{ company.id }}?cmd=note_add" class="line-s" method="post">
  <input type="text" name="note" placeholder="leave a note" class="cell">
  <input type="submit" value="Add" class="bg-green">
</form>

{% for n in notes %}
  <p>
    <i class="grey">{{ n.created | date('d-M-y H:i') }}
    {% if n.inv_id %}
      <a href="/admin/inv/{{ n.inv_id }}">#{{ n.inv_id }}</a>
    {% endif %}
    - <u>{{ n.by_name }}</u>:</i>
    {% if n.uid %}
      » <a href="/admin/user/{{ n.uid }}">{{ n.name }}</a>
    {% endif %}
    {% if n.status in ['Note', 'Top'] %}
      <a href="/admin/company/{{ company.id }}?{% if n.status == 'Top' %}un{% endif %}pin={{ n.id }}" class="{% if n.status == 'Top' %}green{% else %}grey{% endif %}"><i class="fa fa-thumbtack"></i></a>
    {% endif %}
    <br><i class="{% if n.status in ['Note', 'Top'] %}green{% elseif n.status != 'Sys' %}grey{% endif %}">{{ n.txt | raw }}</i>
  </p>
{% endfor %}
