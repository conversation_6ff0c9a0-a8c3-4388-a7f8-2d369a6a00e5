{{ include('/Admin/Company/search.tpl') }}

<div class="fr"><a href="/admin/company/add" class="rc bg-green"><i class="fa fa-plus"></i>New Company</a></div>

<h1>Company, purchaser, organisation</h1>

{% if msg %}
  <p class="b-gold rc">{{ msg | raw }}</p>
{% endif %}

{% if keyw %}
  <p class="b-green rc">Search Terms: {{ keyw|replace({ 'Is Organisation = 1;' : 'Manage Subscribers = 1;' }) }}</p>
{% endif %}


<table class="table">
  <tr class="bg b">
    <td>ID</td>
    <td>Company</td>
    <td>Admin Name</td>
    <td>Email</td>
    <td>Status</td>
    <td class="ar">Users</td>
  </tr>
{% set status = {
  'active' : '<b class="grey">Active</b>',
  'new'  : '<i class="green">Pending</i>',
  'cancelled' : '<s class="red">Cancelled</s>'
} %}
{% for c in company %}
  <tr>
    <td><a href="/admin/company/{{ c.id }}">{{ c.id }}</a></td>
    <td>{{ c.company }}</td>
    <td>{{ c.fname }} {{ c.lname }}</td>
    <td>{{ c.email }}</td>
    <td>{{ status[c.status] | raw }}</td>
    <td class="ar"><a href="/admin/user?company={{ c.id }}">{{ num_users[c.id] }}</a></td>
  </tr>
{% endfor %}
</table>

{{ include('/Admin/pgno.tpl') }}
