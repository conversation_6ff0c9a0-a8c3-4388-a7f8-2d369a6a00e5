
<script>
function enableValidation(){
        if ($('#enable_subscriber_management').prop('checked') == true) {
      var email = $('#companyEmail').val();
      var splitedEmail = email.split("@");
      var valid_domain = $('#valid_domain').val();
      valid_domain = valid_domain.replace(/\s+/g, '');
      var splitedValidDomain = valid_domain.split(",");
      var emailAddress = splitedEmail[1] ? splitedEmail[1] : '';

      if(jQuery.inArray(emailAddress, splitedValidDomain) == -1 || valid_domain == ''){

          if(valid_domain && emailAddress){
            var temp = emailAddress+','+valid_domain;
          }else if(valid_domain) {
            var temp = valid_domain;
          }else if(emailAddress){
            var temp = emailAddress;
          }

      $('#valid_domain').val(temp);
      }
      }
}
function enableSubscriberManagement(){
enableValidation();
} 


$(document).ready(function(){
  setTimeout(enableValidation, 500);

  $("#company").validate({
    submitHandler: function(form) {
        enableValidation();
    
      // do other things for a valid form
          form.submit();
    },
    rules: {
      fname: "required",
//      organisation_limit: {
//                    remote: {
//                            param: {
 //                             url: '/admin/user/userCount',
 //                             data: {
 //                               companyid : $('#organisation_limit').attr('companyid-data'),
 //                             }
 //                           },
 //                             depends: function() {
 //                               return $('#enable_subscriber_management').prop('checked');
 //                             }
 //                         }
 //                     },
    },
    messages: {
      fname: "Please enter your first name",
      organisation_limit: "Please enter valid subscriber limit",
    },
    errorElement: "em",
    errorPlacement: function ( error, element ) {
      // Add the `invalid-feedback` class to the error element
      error.addClass( "invalid-feedback" );

      if ( element.prop( "type" ) === "checkbox" ) {
        error.insertAfter( element.next( "label" ) );
      } else {
        error.insertAfter( element );
      }
    },
    highlight: function ( element, errorClass, validClass ) {
      $( element ).addClass( "is-invalid" ).removeClass( "is-valid" );
    },
    unhighlight: function (element, errorClass, validClass) {
      $( element ).addClass( "is-valid" ).removeClass( "is-invalid" );
    }
  });

});
</script>

<form id="company" action="/admin/company/edit/{{ company.id }}" method="post">
<input type="hidden" name="cmd" value="save_info">
<div class="pop{% if company.company != 'New Company' %} hide{% endif %}" id="edit_info">
  <div style="margin-top:30px;max-width:500px;width:90%">
    <div class="bg-green tab b">Update Organisation Profile</div>
    <div class="bg tabB">
      <p>
        <b>Company Name</b><br>
        <input type="text" placeholder="Company" class="cell-12" name="company" value="{{ company.company }}" required>
      </p>
      <div class="line-s">
        <p class="cell-6">
          <b>ABN</b><br>
          <input type="text" placeholder="ABN" class="cell-12" name="abn" value="{{ company.abn }}">
        </p>
        <p class="cell-6">
          <b>Subscriber Limit</b><br>
          <input type="number" placeholder="Subscriber Limit" id="organisation_limit" companyid-data="{{company.id}}" class="cell-12" name="organisation_limit" value="{{ company.organisation_limit  }}" oninput="this.value=this.value.slice(0,this.maxLength)" maxlength="10">
        </p>
      </div>
      <div class="line-s form-check">
        <p class="cell-6">
          <input type="checkbox" {% if company.enable_subscriber_management %} checked{% endif %} onchange="enableSubscriberManagement()" id="enable_subscriber_management" name="enable_subscriber_management" value="1" >
          <label class="form-check-label" for="enable_subscriber_management"><b>Enable Subscriber Management<b></label>
        </p><br>
      </div>
        <p>
          <b>Allowed Email Domains</b><br>
          <textarea id="valid_domain" placeholder="A comma separated list of allowed email domains eg. example.com, specialistnews.com.au etc" name="valid_domain" data-value="{{ company.valid_domain }}" rows="4" cols="50">{{ company.valid_domain }}</textarea>
        </p>
      <p class="bg-green rc b">Default Admin Contact Details</p>

      <div class="line-s">
        <p class="cell-6">
          <b>First Name</b><br>
          <input type="text" class="cell-12" value="{{ company.fname }}" name="fname" required>
        </p>
        <p class="cell-6">
          <b>Last Name</b><br>
          <input type="text" class="cell-12" value="{{ company.lname }}" name="lname" required>
        </p>
      </div>
      <p>
        <b>Position</b><br>
        <input type="text" class="cell-12" name="position" value="{{ company.position }}" required>
      </p>
      <p>
        <b>Email</b><br>
        <input type="text" class="cell-12" name="email" id="companyEmail" value="{{ company.email }}" required>
      </p>
      <div class="line-s">
        <p class="cell-6">
          <b>Phone</b><br>
          <input type="text" class="cell-12" value="{{ company.phone }}" name="phone" required>
        </p>
        <p class="cell-6">
          <b>Mobile</b><br>
          <input type="text" class="cell-12" value="{{ company.mobile }}" name="mobile">
        </p>
      </div>
      <p>
        <b>Address</b><br>
        <input type="text" class="cell-12" name="addr" value="{{ company.addr }}" required>
      </p>
      <div class="line-s">
        <p class="cell-6">
          <b>City</b><br>
          <input type="text" class="cell-12" value="{{ company.city }}" name="city" required>
        </p>
        <p class="cell-6">
          <b>State</b><br>
          <select class="cell-12" name="state">
            {{ company.state_options | raw }}
          </select>
        </p>
      </div>
      <div class="line-s">
        <p class="cell-6">
          <b>Postcode</b><br>
          <input type="text" class="cell-12" value="{{ company.zip }}" name="zip" required>
        </p>
        <p class="cell-6">
          <b>Country</b><br>
          <select class="cell-12" name="country">
            {{ company.country_options | raw }}
          </select>
        </p>
      </div>
      <p><input type="submit" value="Save" class="rc bg-green"></p>
    </div>
  </div>
</div>
</form>
