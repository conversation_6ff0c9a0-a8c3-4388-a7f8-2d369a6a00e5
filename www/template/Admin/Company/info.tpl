{{ include('/Admin/Company/search.tpl') }}

<a href="/admin/inv/new/{{ company.id }}" class="fr rc bg-green"><i class="fa fa-plus"></i>Add Invoice</a>
<a href="/admin/company/{{ company.id }}/del" class="fr rc bg-red confirm mr-1" data-confirm="Are you sure to delete this orgnisation?"><i class="fa fa-times"></i>Delete Org</a>

<h1>Organisation Profile</h1>

{% if msg %}
  <p class="b-gold rc">{{ msg | raw }}</p>
{% endif %}

{% if err %}
  <p class="err" markdown="1">{{ err }}</p>
{% endif %}

{% set status = {
  'active' : '<b class="grey">Active</b>',
  'new'  : '<i class="green">Pending</i>',
  'cancelled' : '<s class="red">Cancelled</s>'
} %}

<table class="table">
  <tr>
    <td class="cell-3">ID</td>
    <td class="cell-5"><b>{{ company.id }}</b></td>
    <td class="cell-4"><a href="/admin/inv/profile/{{ company.id }}" target="_blank" class="rc bg-blue">Invoice profile</a></td>
  </tr>
  <tr>
    <td>Created</td>
    <td>{{ company.created | date('j-M-Y') }}</td>
  </tr>
  <tr>
    <td>Company</td>
    <td>{{ company.company }}</td>
    <td><a href="/admin/user?company={{ company.id }}" target="_blank" class="rc bg-blue">List Users</a></td>
  </tr>
  <tr>
    <td>Status</td>
    <td>{{ status[company.status] | raw }}</td>
    <td>
    {% if company.status != 'active' %}
      <a href="{{ url_edit }}active" class="b-green rc">» Set as Active</a>
    {% endif %}
    {% if company.status != 'cancelled' %}
      <a href="{{ url_edit }}cancelled" class="b-red rc confirm" data-confirm="Are you sure to cancel this orgnisation?{% if user_active %} There are {{ user_active }} active users will not be able to read full story.{% endif %}{% if unpaid_inv %} There are {{ unpaid_inv }} unpaid invoices.{% endif %} And this org won't receive renewal invoice any more.">» Set as Cancelled</a>
    {% endif %}
    </td>
  </tr>
  <tr><td>Admin Contact</td><td>
    {{ company.fname }} {{ company.lname}}<br>
    <b>{{ company.position }}</b><br>
    {{ company.addr }}<br>
    {{ company.city }} {{ company.state }} {{ company.zip }}<br>
    {{ company.country }}<br>
    {{ company.email }}<br>
    Ph : {{ company.phone }} / Mob : {{ company.mobile }}<br>
    {% if company.abn %}ABN : {{ company.abn }}{% endif %}
  </td><td class="show blue" data-src="#edit_info"><i class="fa fa-pen"></i>Edit</td></tr>

  <tr><td>{{ include('/Admin/Company/info.edit.tpl') }}</td></tr>

{% set acs = ['Guest', '<i class="grey">Basic</i>', 'Freetrial', '<b class="gold">Premium</b>', 'NA', 'NA', 'Staff', '<b>Support</b>', '<b class="green">Editor</b>', '<b class="red">Admin</b>'] %}
{% set status = {
  'active' : '<b class="green">Active</b>',
  'unsub'  : '<i class="blue">Unsub</i>',
  'bounced': '<i class="gold">Bounced</i>',
  'pending': '<i class="grey">Pending</i>',
  'expired': '<b class="red">Expired</b>',
  'closed' : '<s class="red">Closed</s>'
} %}

  <tr><td>Users <i class="show green ib" data-src=".u-inactive">(All {{ user_num }} / {{ pgno_ttl }} )</i></td><td colspan="2">
    <table class="table" id="user_tab">
    {% for u in users %}
      <tr class="{% if u.status in ['closed', 'pending', 'expired'] %}grey bg u-inactive{% endif%}">
        <td>{{ acs[u.acs] | raw }}</td>
        <td>{{ status[u.status] | raw }}</td>
        <td><a href="/admin/user/{{ u.uid }}">{% if u.is_admin %}<i class="fa fa-user" title="Company Admin User"></i>{% endif %}{{ u.fname }} {{ u.lname }}</a></td>
        <td><i class="fa fa-{% if u.status in ['closed', 'pending'] %}times{% else %}check{% endif %}"></i>{{ u.email }}</td>
        <td title="Last visited"{% if u.visited < 'now' | date_modify('- 10 day') | date('Y-m-d H:i:s') %} class="grey"{% elseif u.visited > 'now' | date_modify('-1 day') | date('Y-m-d H:i:s') %} class="green"{% endif %}>{% if u.visited and u.visited > '0000-00-00 00:00:00' %}{{ u.visited | date('d-M-Y H:i') }}{% endif %}</td>
      </tr>
    {% endfor %}
    </table>
    {{ include('/Admin/pgno.tpl') }}
  </td></tr>
</table>

{{ include('/Admin/Company/info.note.tpl') }}
