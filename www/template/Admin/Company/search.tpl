
<form class="card card-body mb-5 bg-light" action="/admin/company" method="post" id="formS">

  <div class="row">
    <div class="col-md-4 mb-3">
      <label class="font-weight-bold" for="company">Company Name</label>
      <input type="text" class="form-control" id="company" name="company" placeholder="Company name" value="{{ form.company }}">
    </div>
    <div class="col-md-2 mb-3">
      <label class="font-weight-bold" for="status">Status</label>
      <select class="custom-select d-block w-100" id="status" name="status">
        <option value="">Status</option>
        <option value="active">Active</option>
        <option value="cancelled">Cancelled</option>
      </select>
    </div>
    <div class="col-md-2 mb-3">
      <label class="font-weight-bold" for="state">State</label>
      <select class="custom-select d-block w-100" id="state" id="name">
        {{ state_options | raw }}
      </select>
    </div>
    <div class="col-md-2 mb-3">
      <label class="font-weight-bold" for="id">ID</label>
      <input type="text" class="form-control" id="id" name="id" placeholder="" value="{{ form.id }}">
    </div>  
    <div class="col-md-2 mb-3">
      <label class="font-weight-bold" for="inv_status">Last Invoice</label>
      <select class="custom-select d-block w-100" id="inv_status" name="inv_status">
        <option value="">Status</option>
        {{ inv_status_options | raw }}
      </select>
    </div>  
  </div>

  <div class="row">
    <div class="col-md-6 mb-3">
      <label class="d-none" for="email">Email</label>
      <input type="text" class="form-control" id="email" name="email" placeholder="Admin contact email" value="{{ form.email }}">
    </div>
    <div class="col-md-6 mb-3">
      <label class="d-none" for="name">Name</label>
      <input type="text" class="form-control" id="name" name="name" placeholder="Admin contact name" value="{{ form.name }}">
    </div>
  </div>

  <div class="row">
    <div class="col-md-6">
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="checkbox" name="no_user" value="1"{% if form.no_user %} checked{% endif %}>
        <label class="form-check-label" for="no_user">No Users</label>
      </div>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="checkbox" name="no_inv" value="1"{% if form.no_inv %} checked{% endif %}>
        <label class="form-check-label" for="no_inv">No Invoice</label>
      </div>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="checkbox" name="is_organisation" value="1">
        <label class="form-check-label" for="is_organisation">Manage Subscribers enabled</label>
      </div>
    </div>

    {% if can_download_csv %}
    <div class="col-md-3">
      <input type="submit" value="Download CSV" class="btn btn-outline-secondary w-100" id="csv">
    </div>
    {% endif %}
    <div class="col-md-3">
      <input type="submit" name="cmd" value="Search" class="btn btn-primary w-100">
    </div>
  </div>

  <input type="hidden" id="pgno" name="pgno">
</form>

{% if can_download_csv %}
<script>
  $(function() {
    $('#csv').on('click', function() {
      var data = $("#formS :input")
        .filter(function(i, el) { return $(el).val() != ''; })
        .serialize();
      window.location.href = '/admin/company/?cmd=csv&' + data;
      return false;
    });
  });
</script>
{% endif %}
