<h1>Latest 100 honeypot spam</h1>

<p class="rc b-green">You can also search via eg ? name=Jo & email=<EMAIL></p>
<p class="rc b-gold">Supported search fields: ip, page, name, fname, lname, email, company, phone</p>
<table class="table">
  <tr class="bg b">
    <td>Date</td>
    <td>IP</td>
    <td>Page</td>
    <td>Detail</td>
    <td></td>
  </tr>
{% for s in spam %}
  <tr>
    <td>{{ s.ts | date('j-M-y H:i') }}</td>
    <td><a href="?ip={{ s.ip }}">{{ s.ip }}</td>
    <td><a href="?page={{ s.page }}">{{ s.page }}</td>
    <td class="show" data-src="#id_{{ s.id }}">{{ s.txt | slice(0,50) }} ...</td>
    <td><a href="/admin/spam/del/{{ s.id }}" class="confirm red" data-confirm="Are you sure to delete this record?">Del</td>
  </tr>
  <tr id="id_{{ s.id }}" class="hide"><td></td><td colspan="4" class="code" style="white-space:pre-wrap;max-width:500px;overflow:auto">{{ s.txt }}</td></tr>
{% endfor %}
</table>

