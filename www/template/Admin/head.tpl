{% set v = '220914' %}
<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  	<title>{{ page.title }} | {{ site.name }}</title>
  	<meta name="description" content="{{ page.desc }}">
  	<meta name="keywords" content="{{ page.keyw }}">
  	<meta name="author" content="{{ page.author }}">

  	<link rel="shortcut icon" href="/favicon.ico">

    <!--{# grid-color.css & news.css original code > migrating to BS4 #}-->
    <link rel="stylesheet" href="/assets/css/grid-color.css?v={{ v }}">
    <!--<link rel="stylesheet" href="/assets/css/news.css?v=20200422c">-->

    <!--{# FontAwesome 5.15.14 #}-->
    <link href="/assets/css/all.css?v=51514" rel="stylesheet">

  	<!--{# Bootstrap core CSS #}-->
  	<link href="/assets/css/bootstrap.min.css?v=462" rel="stylesheet">
  	<link href="/assets/css/bootstrap-icons.css?v=191" rel="stylesheet">
    
    <!-- Admin CSS styles now in custom.css -->
    <!-- <link href="/css/admin.css?v={{ v }}" rel="stylesheet"> -->

  	<!--{# jQuery Library #}-->
  	<script src="/assets/js/jquery.min.js?v=351"></script>
  	<script src="/assets/js/bootstrap.bundle.min.js?v=462"></script>
  	<script src="/assets/js/jquery.validate.min.js?v=1195"></script>

    <link href="/assets/css/easymde.min.css?v=2170" rel="stylesheet">
    <script src="/assets/js/easymde.min.js?v=2170"></script>

    <!--{# news.js original code #}-->
    <script src="/assets/js/news.js?v={{ v }}"></script>

    <script src="/assets/js/notify.min.js?v={{ v }}"></script>
    {{ page.css|raw }}{{ page.style|raw }}
    {{ page.js|raw }}{{ page.script|raw }}

    <script src="/assets/js/jquery.init.js?v={{ v }}"></script>
    <link href="/assets/css/custom.css?v={{ v }}" rel="stylesheet">

</head>
<body class="bg-light pt-4">

{{ include('/Admin/nav.tpl') }}

<!--<div class="page p-2" style="background:#fff">-->
<!--main-->
<div class="container-xl bg-white py-5">
