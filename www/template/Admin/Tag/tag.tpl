
{% block content %}
<div class="float-right">
<button id="createTag" class="btn btn-primary" data-toggle="tooltip" data-placement="bottom" title="" data-original-title="Tag will be created at the top-level.">Create New Tag</button>
</div>
<h1>Tag Manager</h1>

<!-- Load Sortable.js -->
<script src="/assets/js/sortable.min.js"></script>

{% if msg %}
<p class="b-gold rc">{{ msg | raw }}</p>
{% endif %}

  <div style="height: calc(100vh - 200px); overflow: hidden;">
    <p class="text-muted">You can create a new tag within an existing one, or nest tags by dragging them onto other tags below. <strong>Note:</strong> changing a tag's position affects its order in the admin on OHS Alert and Workplace Express, but not on the front-end "Topics" page. On HR Daily, tags are always displayed alphabetically.</p>


    <div id="tag-hierarchy" class="sortable-tree" style="height: calc(100% - 65px); overflow-y: auto; border: 1px solid #eee; padding: 10px;">
      <div id="comm_tags">
        <ul class="sortable list-unstyled">
          {% for key, value in comm_tags %}
            {% if key|slice(0, 2) == '21' and key|length == 4 %}
              <li class="tag-item" data-id="{{ key }}">
                <div class="tag-content d-flex align-items-center p-2 mb-2 bg-light rounded">
                  <i class="bi bi-grip-vertical handle mr-2"></i>
                  <span class="tag-name">{{ value }}</span>
                  <div class="ml-auto">
                    <button class="btn btn-sm btn-outline-primary add-child-tag" data-id="{{ key }}" data-name="{{ value }}" title="Add Child Tag">
                      <i class="bi bi-plus"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info view-articles" data-id="{{ key }}" data-name="{{ value }}" title="View Articles">
                      <i class="bi bi-list-ul"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary edit-tag" data-id="{{ key }}" data-name="{{ value }}">
                      <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger delete-tag" data-id="{{ key }}" data-name="{{ value }}">
                      <i class="bi bi-trash"></i>
                    </button>
                  </div>
                </div>
                <!-- Render children recursively -->
                {% set parent_id = key %}
                {% include 'Admin/Tag/tag_children.tpl' with {'parent_id': parent_id, 'comm_tags': comm_tags} %}
              </li>
            {% endif %}
          {% endfor %}
        </ul>
      </div>
    </div>
  </div>

<!-- Edit Tag Modal -->
<div class="modal fade" id="editTagModal" tabindex="-1" role="dialog" aria-labelledby="editTagModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editTagModalLabel">Edit Tag</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form id="editTagForm">
        <div class="modal-body">
          <input type="hidden" id="edit-tag-id">
          <div class="form-group">
            <label for="edit-tag-name">Tag Name</label>
            <input type="text" class="form-control" id="edit-tag-name" required>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Save Changes</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Create Tag Modal -->
<div class="modal fade" id="createTagModal" tabindex="-1" role="dialog" aria-labelledby="createTagModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="createTagModalLabel">Create New Tag</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form id="createTagForm">
        <div class="modal-body">
          <div class="form-group">
            <label for="new-tag-name">Tag Name</label>
            <input type="text" class="form-control" id="new-tag-name" required>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Create Tag</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Add Child Tag Modal -->
<div class="modal fade" id="addChildTagModal" tabindex="-1" role="dialog" aria-labelledby="addChildTagModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addChildTagModalLabel">Add Child Tag</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <form id="addChildTagForm">
        <div class="modal-body">
          <p>Adding child tag to: <strong id="parent-tag-name"></strong></p>
          <input type="hidden" id="parent-tag-id">
          <div class="form-group">
            <label for="child-tag-name">Child Tag Name</label>
            <input type="text" class="form-control" id="child-tag-name" required>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <button type="submit" class="btn btn-primary">Add Child Tag</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Articles Using Tag Modal -->
<div class="modal fade" id="articlesUsingTagModal" tabindex="-1" role="dialog" aria-labelledby="articlesUsingTagModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="articlesUsingTagModalLabel">Articles Using Tag: <span id="tag-name-in-use"></span></h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="alert alert-warning">
          <strong>Warning:</strong> Deleting this tag will remove it from all these articles.
          <span id="article-count-message"></span>
        </div>
        
        <div class="form-group">
          <input type="text" class="form-control" id="article-search" placeholder="Filter article list below...">
        </div>
        
        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
          <table class="table table-striped table-hover" id="articles-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>Title</th>
                <th>Date</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="articles-list">
              <!-- Articles will be populated here -->
            </tbody>
          </table>
        </div>
        
        <div id="loading-articles" class="text-center py-3">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <p>Loading articles...</p>
        </div>
        
        <div id="no-articles-found" class="alert alert-info d-none">
          No articles found matching your search.
        </div>
      </div>
      <div class="modal-footer">
        <input type="hidden" id="tag-to-delete">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirm-delete-with-articles">Delete Tag & Remove References</button>
      </div>
    </div>
  </div>
</div>

<!-- View Articles Using Tag Modal -->
<div class="modal fade" id="viewArticlesModal" tabindex="-1" role="dialog" aria-labelledby="viewArticlesModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="viewArticlesModalLabel">Articles Using Tag: <span id="view-tag-name"></span></h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <!-- Add new article link form -->
        <div class="form-group mb-4">
          <label for="link-article-search"><strong>Link a new article to this tag:</strong></label>
          <div class="input-group">
            <input type="text" class="form-control" id="link-article-search" placeholder="Search by article ID or title...">
            <div class="input-group-append">
              <button class="btn btn-outline-primary" type="button" id="search-articles-to-link">
                <i class="bi bi-search"></i> Search
              </button>
            </div>
          </div>
          <div id="article-search-results" class="mt-2 d-none">
            <div class="list-group" id="article-search-results-list">
              <!-- Search results will appear here -->
            </div>
            <div id="no-search-results" class="alert alert-info d-none mt-2">
              No articles found matching your search.
            </div>
            <div id="search-loading" class="text-center py-2 d-none">
              <div class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="sr-only">Searching...</span>
              </div>
              <span class="ml-2">Searching...</span>
            </div>
          </div>
        </div>
        
        <hr>
        
        <div class="form-group">
          <label for="view-article-search"><strong>Filter linked articles:</strong></label>
          <input type="text" class="form-control" id="view-article-search" placeholder="Filter by ID, title, or comma-separated list (e.g. 123, 456, 789)">
        </div>

        <!-- Unlink All button container -->
        <div id="unlink-all-container" class="mb-3 d-none">
          <div class="d-flex justify-content-between align-items-center bg-light p-2 rounded">
            <div>
              <strong id="linked-articles-count">0 articles linked to this tag</strong>
            </div>
            <button class="btn btn-outline-danger" id="unlink-all-articles" title="Unlink All Articles">
              <i class="bi bi-x-circle"></i> Unlink All
            </button>
          </div>
        </div>

        <!-- Table with fixed header -->
        <div class="table-container">
          <table class="table table-striped table-hover" id="view-articles-table">
            <thead>
              <tr>
                <th width="60">ID</th>
                <th>Title</th>
                <th width="100">Date</th>
                <th width="90">Status</th>
                <th width="140" class="text-nowrap">Actions</th>
              </tr>
            </thead>
          </table>
          
          <div class="table-body-container">
            <table class="table table-striped table-hover">
              <tbody id="view-articles-list">
                <!-- Articles will be populated here -->
              </tbody>
            </table>
          </div>
        </div>

        <style>
          .table-container {
            position: relative;
            max-height: 400px;
          }
          
          .table-container table {
            margin-bottom: 0;
          }
          
          .table-body-container {
            max-height: 360px;
            overflow-y: auto;
            width: 100%;
          }
          
          .table-body-container table {
            margin-top: -1px; /* Adjust for border overlap */
          }
          
          /* Ensure column widths match between header and body tables */
          .table-container th:nth-child(1),
          .table-body-container td:nth-child(1) {
            width: 60px;
          }
          
          .table-container th:nth-child(3),
          .table-body-container td:nth-child(3) {
            width: 100px;
          }
          
          .table-container th:nth-child(4),
          .table-body-container td:nth-child(4) {
            width: 90px;
          }
          
          .table-container th:nth-child(5),
          .table-body-container td:nth-child(5) {
            width: 140px;
          }
        </style>
        
        <div id="view-loading-articles" class="text-center py-3">
          <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
          </div>
          <p>Loading articles...</p>
        </div>
        
        <div id="view-no-articles-found" class="alert alert-info d-none">
          No articles found using this tag.
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="download-articles-csv" data-toggle="tooltip" data-placement="top" title="" data-original-title="Click to download a CSV of all articles currently linked to this tag (filters do not apply).">
          <i class="bi bi-download"></i> Download CSV
        </button>
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<script>
// Global function to link all articles (accessible directly from inline onclick)
function linkAllArticles(buttonElement, tagId, articleIds) {
  var $button = $(buttonElement);
  
  // Prevent multiple clicks
  if ($button.prop('disabled')) {
    return;
  }
  
  // Disable the button and show loading state
  $button.prop('disabled', true).html('<i class="bi bi-hourglass-split"></i> Linking...');
  
  console.log('Linking articles to tag', tagId, articleIds);
  
  // Call the backend to link all articles at once
  $.ajax({
    url: '/admin/tag/linkMultipleArticles',
    type: 'POST',
    dataType: 'json',
    headers: {
      'X-Requested-With': 'XMLHttpRequest',
      'X-Auth-Token': '{{ auth_token }}'
    },
    data: {
      articleIds: articleIds,
      tagId: tagId
    },
    xhrFields: {
      withCredentials: true
    },
    success: function(response) {
      // Always reset the button state regardless of success or failure
      $button.prop('disabled', false).html('<i class="bi bi-link"></i> Link All');
      
      if (response.success) {
        // Show success message
        var message = response.message;
        
        // Add details about already linked and not found articles if available
        if (response.alreadyLinkedCount > 0 || response.notFoundCount > 0) {
          var details = [];
          if (response.alreadyLinkedCount > 0) {
            details.push(response.alreadyLinkedCount + ' already linked');
          }
          if (response.notFoundCount > 0) {
            details.push(response.notFoundCount + ' not found');
          }
          if (details.length > 0) {
            message += ' (' + details.join(', ') + ')';
          }
        }
        
        showArticleLinkNotification(message, 'success');
        
        // Refresh the articles list
        viewArticlesUsingTag(tagId, $('#view-tag-name').text());
        
        // Clear the search input and results
        $('#link-article-search').val('');
        $('#article-search-results').addClass('d-none');
        $('#article-search-results-list').empty();
      } else {
        // Show error message
        showArticleLinkNotification('Error: ' + (response.message || 'Failed to link articles'), 'error');
      }
    },
    error: function(xhr, status, error) {
      // Always reset the button state on error
      $button.prop('disabled', false).html('<i class="bi bi-link"></i> Link All');
      
      // Try to parse the response as JSON
      let errorMessage = 'An error occurred while linking articles';
      try {
        if (xhr.responseText) {
          const jsonResponse = JSON.parse(xhr.responseText);
          if (jsonResponse.message) {
            errorMessage = jsonResponse.message;
          }
        }
      } catch (e) {
        console.error('Error parsing error response:', e);
      }
      
      // Show error message
      showArticleLinkNotification('Error: ' + errorMessage, 'error');
      console.error('AJAX error:', status, error, xhr.responseText);
    }
  });
}

// Global function to unlink all articles (accessible directly from inline onclick)
function unlinkAllArticles(buttonElement, tagId, articleIds) {
  var $button = $(buttonElement);

  // Prevent multiple clicks
  if ($button.prop('disabled')) {
    return;
  }

  // Show confirmation dialog
  if (!confirm('Are you sure you want to unlink all ' + articleIds.length + ' articles from this tag?')) {
    return;
  }

  // Disable the button and show loading state
  $button.prop('disabled', true).html('<i class="bi bi-hourglass-split"></i> Unlinking...');

  console.log('Unlinking articles from tag', tagId, articleIds);

  // Call the backend to unlink all articles at once
  $.ajax({
    url: '/admin/tag/unlinkMultipleArticles',
    type: 'POST',
    dataType: 'json',
    headers: {
      'X-Requested-With': 'XMLHttpRequest',
      'X-Auth-Token': '{{ auth_token }}'
    },
    data: {
      articleIds: articleIds,
      tagId: tagId
    },
    xhrFields: {
      withCredentials: true
    },
    success: function(response) {
      // Always reset the button state regardless of success or failure
      $button.prop('disabled', false).html('<i class="bi bi-x-circle"></i> Unlink All');

      console.log('Unlink all response:', response); // Debug logging

      if (response.success) {
        // Show success message
        var message = response.message;

        // Add details about errors and not linked articles if available
        if (response.notLinkedCount > 0) {
          message += ' (' + response.notLinkedCount + ' were already unlinked)';
        }
        if (response.errorCount > 0) {
          message += ' (' + response.errorCount + ' failed)';
        }

        showArticleLinkNotification(message, 'success');

        // Immediately clear the UI since all articles were unlinked
        console.log('Clearing UI immediately after unlink all success'); // Debug logging
        $('#view-articles-list').empty();
        $('#unlink-all-container').addClass('d-none');
        $('#view-no-articles-found').removeClass('d-none').text('No articles found using this tag.');
        $('.table-body-container').hide();

        // Also refresh from backend to ensure consistency (with delay)
        console.log('Refreshing articles list after unlink all with delay'); // Debug logging
        setTimeout(function() {
          viewArticlesUsingTag(tagId, $('#view-tag-name').text());
        }, 500); // 500ms delay
      } else {
        // Show error message
        console.log('Unlink all failed:', response); // Debug logging
        showArticleLinkNotification('Error: ' + (response.message || 'Failed to unlink articles'), 'error');
      }
    },
    error: function(xhr, status, error) {
      // Always reset the button state on error
      $button.prop('disabled', false).html('<i class="bi bi-x-circle"></i> Unlink All');

      // Try to parse the response as JSON
      let errorMessage = 'An error occurred while unlinking articles';
      try {
        if (xhr.responseText) {
          const jsonResponse = JSON.parse(xhr.responseText);
          if (jsonResponse.message) {
            errorMessage = jsonResponse.message;
          }
        }
      } catch (e) {
        console.error('Error parsing error response:', e);
      }

      // Show error message
      showArticleLinkNotification('Error: ' + errorMessage, 'error');
      console.error('AJAX error:', status, error, xhr.responseText);
    }
  });
}

// Notification timing constants
var NOTIFICATION_TIMINGS = {
  SUCCESS: 3000, // 3 seconds for success messages
  ERROR: 5000    // 5 seconds for error messages
};

// Arrays to track ALL notification timers for different contexts
var notificationTimers = {
  modal: [],
  main: [],
  articleLink: []
};

// Generic notification manager function
function manageNotificationTiming(notification, type, context, containerSelector) {
  var timeout = type === 'success' ? NOTIFICATION_TIMINGS.SUCCESS : NOTIFICATION_TIMINGS.ERROR;

  // Clear ALL existing timers for this context (both success and error)
  notificationTimers[context].forEach(function(timerId) {
    clearTimeout(timerId);
  });
  notificationTimers[context] = []; // Clear the array

  // Reset timers for ALL visible notifications in this container
  $(containerSelector + ' .alert[data-notification-type]').each(function() {
    var existingNotification = $(this);
    var existingType = existingNotification.data('notification-type');
    var existingTimeout = existingType === 'success' ? NOTIFICATION_TIMINGS.SUCCESS : NOTIFICATION_TIMINGS.ERROR;

    var timerId = setTimeout(function() {
      // Special handling for success notifications in main context
      if (existingType === 'success' && context === 'main') {
        sessionStorage.setItem('tagHierarchyScrollPosition', $('#tag-hierarchy').scrollTop());
      }
      existingNotification.alert('close');
    }, existingTimeout);

    notificationTimers[context].push(timerId);
  });
}

// Function to show notification in the modal
function showArticleLinkNotification(message, type) {
  // Create a Bootstrap alert
  var notification = $('<div class="alert alert-' + (type === 'success' ? 'success' : 'danger') + ' alert-dismissible fade show" role="alert" data-notification-type="' + type + '">' +
    message +
    '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
    '<span aria-hidden="true">&times;</span>' +
    '</button>' +
    '</div>');

  // Add the notification to the modal
  $('#viewArticlesModal .modal-body').prepend(notification);

  // Use the generic timing manager
  manageNotificationTiming(notification, type, 'modal', '#viewArticlesModal');
}

$(function() {
  // Restore scroll position if available
  if (sessionStorage.getItem('tagHierarchyScrollPosition')) {
    $('#tag-hierarchy').scrollTop(parseInt(sessionStorage.getItem('tagHierarchyScrollPosition')));
    
    // Optional: If you need to scroll to a specific tag that was being edited/moved
    if (sessionStorage.getItem('lastModifiedTagId')) {
      const tagId = sessionStorage.getItem('lastModifiedTagId');
      const $tag = $(`li.tag-item[data-id="${tagId}"]`);
      if ($tag.length) {
        // Calculate a better scroll position that doesn't move the tag too far up
        // Use a smaller offset (50px instead of 100px) and ensure tag is visible
        const tagPosition = $tag.position().top;
        const containerHeight = $('#tag-hierarchy').height();
        const tagHeight = $tag.outerHeight();
        
        // Only adjust scroll if the tag isn't already fully visible
        const currentScroll = $('#tag-hierarchy').scrollTop();
        const tagTop = currentScroll + tagPosition;
        const tagBottom = tagTop + tagHeight;
        const viewportBottom = currentScroll + containerHeight;
        
        if (tagTop < currentScroll || tagBottom > viewportBottom) {
          // Center the tag in the viewport with a slight upward bias
          const newScrollTop = currentScroll + tagPosition - (containerHeight / 2) + (tagHeight / 2);
          $('#tag-hierarchy').animate({
            scrollTop: newScrollTop
          }, 200);
        }
        
        // Highlight the tag
        $tag.find('.tag-content').addClass('highlight-tag');
        setTimeout(() => $tag.find('.tag-content').removeClass('highlight-tag'), 2000);
      }
      // Clear the stored tag ID after using it
      sessionStorage.removeItem('lastModifiedTagId');
    }
  }

  // Initialize the initial hierarchy after the page loads
  initialHierarchy = JSON.stringify(serializeHierarchy($('#tag-hierarchy > #comm_tags > .sortable > li')));
  
  // Initialize Sortable on all sortable lists
  var sortables = [];
  $('.sortable').each(function() {
    var sortable = new Sortable(this, {
      group: 'nested',
      animation: 150,
      fallbackOnBody: true,
      swapThreshold: 0.65,
      handle: '.handle',
      scrollSpeed: 40,
      scrollSensitivity: 80,
      scroll: true,
      scrollFn: function(offsetX, offsetY, originalEvent, touchEvt, hoverEl) {
        // Custom scroll function if needed
        var parentEl = hoverEl.closest('#tag-hierarchy');
        if (parentEl) {
          parentEl.scrollTop += offsetY;
        }
      },
      onStart: function(evt) {
        // Store the moved tag ID when drag starts
        window.movedTagId = evt.item.dataset.id;
        console.log('Started dragging tag:', window.movedTagId);
      },
      onEnd: function(evt) {
        // Update delete button states after drag/drop
        setTimeout(updateDeleteButtonStates, 100);
        // Save the hierarchy immediately when a tag is moved
        saveHierarchy(evt);
      }
    });
    sortables.push(sortable);
  });
  
  // Function to save the hierarchy
  function saveHierarchy(evt) {
    // Get the moved tag ID
    var movedTagId = window.movedTagId || null;
    
    // Save the moved tag ID for highlighting after reload
    if (movedTagId) {
      sessionStorage.setItem('lastModifiedTagId', movedTagId);
    }
    
    // Get the target parent ID
    var targetParentId = '21'; // Default to root level
    var parentEl = evt.to.closest('li.tag-item');
    if (parentEl && parentEl.dataset.id) {
      targetParentId = parentEl.dataset.id;
    }
    
    // Get the target position (index within the parent)
    var targetPosition = Array.from(evt.to.children).indexOf(evt.item);
    
    // Get the tag name for the confirmation dialog
    var movedTagName = $(evt.item).find('> .tag-content .tag-name').text().trim();
    var targetParentName = parentEl ? $(parentEl).find('> .tag-content .tag-name').text().trim() : 'Root';
    
    console.log('Preparing to move:', {
      movedTagId: movedTagId,
      movedTagName: movedTagName,
      targetParentId: targetParentId,
      targetParentName: targetParentName,
      targetPosition: targetPosition
    });
    
    // Show confirmation dialog
    if (confirm('Are you sure you want to move "' + movedTagName + '" under "' + targetParentName + '" at position ' + (targetPosition + 1) + '?')) {
      // Send to server
      $.ajax({
        url: '/admin/tag/updateHierarchy',
        type: 'POST',
        dataType: 'json',
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'X-Auth-Token': '{{ auth_token }}'
        },
        data: {
          movedTagId: movedTagId,
          targetParentId: targetParentId,
          targetPosition: targetPosition
        },
        xhrFields: {
          withCredentials: true
        },
        success: function(response) {
          if (response.success) {
            // Show a brief success message
            showNotification('Tag position updated successfully', 'success');
          } else {
            showNotification('Error: ' + response.message, 'error');
            // Reload the page to reset the UI
            setTimeout(function() {
              window.location.reload();
            }, 500);
          }
        },
        error: function(xhr, status, error) {
          showNotification('Error updating tag position', 'error');
          // Reload the page to reset the UI
          setTimeout(function() {
            window.location.reload();
          }, 1000);
        }
      });
    } else {
      // User canceled, reload the page to reset the UI
      window.location.reload();
    }
  }

  // Function to serialize the hierarchy for saving
  function serializeHierarchy(items) {
    var result = [];
    
    items.each(function() {
      var $item = $(this);
      var id = $item.data('id');
      var name = $item.find('> .tag-content .tag-name').text().trim();
      
      var itemData = {
        id: id,
        name: name
      };
      
      // Check if this item has children
      var $children = $item.find('> ul.sortable > li');
      if ($children.length > 0) {
        itemData.children = serializeHierarchy($children);
      }
      
      result.push(itemData);
    });
    
    return result;
  }

  // Function to show a notification
  function showNotification(message, type) {
    var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    var notification = $('<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert" style="pointer-events: none;" data-notification-type="' + type + '">' +
      message +
      '<button type="button" class="close" data-dismiss="alert" aria-label="Close" style="pointer-events: auto;">' +
      '<span aria-hidden="true">&times;</span>' +
      '</button>' +
      '</div>');

    // Add the notification to the page
    $('#tag-hierarchy').before(notification);

    // Set up event handler for when the alert is closed
    notification.on('closed.bs.alert', function() {
      if (type === 'success') {
        // Save scroll position before reload
        sessionStorage.setItem('tagHierarchyScrollPosition', $('#tag-hierarchy').scrollTop());

        // Refresh the page when a success notification is closed
        window.location.reload();
      }
    });

    // Use the generic timing manager for all notifications
    manageNotificationTiming(notification, type, 'main', 'body');
  }
  
  // Edit tag modal
  $('.edit-tag').on('click', function() {
    var id = $(this).data('id');
    var name = $(this).data('name');
    
    $('#edit-tag-id').val(id);
    $('#edit-tag-name').val(name);
    $('#editTagModal').modal('show');
  });
  
  // Add form submission handler for the edit tag modal
  $('#editTagForm').on('submit', function(e) {
    e.preventDefault();
    
    var id = $('#edit-tag-id').val();
    var name = $('#edit-tag-name').val();
    
    $.ajax({
      url: '/admin/tag/update',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-Auth-Token': '{{ auth_token }}'
      },
      data: {
        id: id,
        name: name
      },
      // Include session ID if your system uses it
      xhrFields: {
        withCredentials: true
      },
      success: function(response) {
        if (response.success) {
          // Update the tag name in the UI
          $('[data-id="' + id + '"] > .tag-content .tag-name').text(name);
          
          // Also update the data-name attribute for future edits
          $('[data-id="' + id + '"] .edit-tag').data('name', name);
          
          // Close the modal
          $('#editTagModal').modal('hide');
          
          // do not show message just get back to the updated tree
          // alert('Tag updated successfully');
        } else {
          alert('Error: ' + (response.message || 'Unknown error'));
        }
      },
      error: function(xhr, status, error) {
        console.error('AJAX error:', status, error);
        alert('An error occurred while updating the tag: ' + error);
      }
    });
  });
  
  // Function to format date
  function formatDate(dateString) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    
    // Check if date is valid
    if (isNaN(date.getTime())) return dateString;
    
    // Format as "4 Jun, 2025"
    const day = date.getDate();
    const month = date.toLocaleString('en-US', { month: 'short' });
    const year = date.getFullYear();
    
    return `${day} ${month}, ${year}`;
  }
  
  // Function to get status badge
  function getStatusBadge(status) {
    if (!status) return '<span class="badge badge-light">Unknown</span>';
    
    const statusMap = {
      'published': '<span class="badge badge-success">Published</span>',
      'unpublished': '<span class="badge badge-danger">Unpublished</span>',
      'draft': '<span class="badge badge-warning">Draft</span>',
      'deleted': '<span class="badge badge-dark">Deleted</span>'
    };
    
    return statusMap[status.toLowerCase()] || '<span class="badge badge-light">' + status + '</span>';
  }
  
  // Function to load articles using a tag
  function loadArticlesUsingTag(tagId, tagName) {
    $('#tag-name-in-use').text(tagName);
    $('#tag-to-delete').val(tagId);
    $('#articles-list').empty();
    $('#loading-articles').show();
    $('#no-articles-found').addClass('d-none');
    $('#articles-table').hide();
    
    $.ajax({
      url: '/admin/tag/delete',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-Auth-Token': '{{ auth_token }}'
      },
      data: {
        id: tagId,
        getArticles: true
      },
      xhrFields: {
        withCredentials: true
      },
      success: function(response) {
        $('#loading-articles').hide();
        
        if (response.success && response.articles && response.articles.length > 0) {
          $('#article-count-message').text('Found ' + response.totalCount + ' articles using this tag' + 
            (response.totalCount > response.articles.length ? ' (showing first ' + response.articles.length + ')' : '') + '.');
          
          // Populate the table
          response.articles.forEach(function(article) {
            const row = `
              <tr>
                <td>${article.id}</td>
                <td>${article.title}</td>
                <td>${formatDate(article.date_pub)}</td>
                <td>${getStatusBadge(article.status)}</td>
                <td>
                  <a href="/news/${article.id}" class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="bi bi-eye"></i> View
                  </a>
                </td>
              </tr>
            `;
            $('#articles-list').append(row);
          });
          
          $('#articles-table').show();
        } else {
          $('#no-articles-found').removeClass('d-none');
          $('#article-count-message').text(response.message || 'No articles found using this tag.');
        }
      },
      error: function(xhr, status, error) {
        $('#loading-articles').hide();
        
        // Try to parse the response as JSON
        let errorMessage = error;
        try {
          if (xhr.responseText) {
            // If it starts with HTML, extract just the text
            if (xhr.responseText.trim().startsWith('<')) {
              const tempDiv = document.createElement('div');
              tempDiv.innerHTML = xhr.responseText;
              errorMessage = 'Server error: ' + tempDiv.textContent.trim().substring(0, 100) + '...';
            } else {
              const jsonResponse = JSON.parse(xhr.responseText);
              if (jsonResponse.message) {
                errorMessage = jsonResponse.message;
              }
            }
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
        
        $('#no-articles-found').removeClass('d-none').text('Error loading articles: ' + errorMessage);
        console.error('AJAX error:', status, error, xhr.responseText);
      }
    });
    
    $('#articlesUsingTagModal').modal('show');
  }
  
  // Delete tag functionality - update to skip confirmation if tag has articles
  $(document).on('click', '.delete-tag', function(e) {
    // Prevent default action and stop event propagation
    e.preventDefault();
    e.stopPropagation();
    
    var id = $(this).data('id');
    var name = $(this).data('name');
    
    // First check if the tag has articles by making an AJAX request
    $.ajax({
      url: '/admin/tag/delete',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-Auth-Token': '{{ auth_token }}'
      },
      data: {
        id: id,
        checkOnly: true
      },
      xhrFields: {
        withCredentials: true
      },
      success: function(response) {
        if (response.hasArticles) {
          // If tag has articles, skip confirmation and show articles dialog directly
          loadArticlesUsingTag(id, name);
        } else {
          // If tag has no articles, show confirmation dialog
          if (confirm('Are you sure you want to delete this tag?\n(no linked articles to it)')) {
            // Then call our delete function
            deleteTag(id, name);
          }
        }
      },
      error: function(xhr, status, error) {
        console.error('AJAX error:', status, error);
        showNotification('An error occurred while checking tag usage', 'error');
      }
    });
  });
  
  // Modify the delete tag function
  function deleteTag(tagId, tagName) {
    $.ajax({
      url: '/admin/tag/delete',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-Auth-Token': '{{ auth_token }}'
      },
      data: {
        id: tagId,
        forceDelete: false
      },
      xhrFields: {
        withCredentials: true
      },
      success: function(response) {
        if (response.success) {
          // Use the returned tag name and affected articles count
          showNotification(response.message || (response.tagName + ' tag removed from ' + response.affectedArticles + ' articles'), 'success');
          // Remove the tag from the UI
          $('li.tag-item[data-id="' + tagId + '"]').remove();
          // Update delete button states since hierarchy has changed
          setTimeout(updateDeleteButtonStates, 100);
        } else {
          if (response.requireConfirmation) {
            // Show the articles using this tag
            loadArticlesUsingTag(tagId, tagName);
          } else {
            showNotification('Error: ' + response.message, 'error');
          }
        }
      },
      error: function(xhr, status, error) {
        console.error('AJAX error:', status, error);
        showNotification('An error occurred while deleting the tag', 'error');
      }
    });
  }
  
  // Confirm delete with articles
  $('#confirm-delete-with-articles').on('click', function() {
    var tagId = $('#tag-to-delete').val();
    var tagName = $('#tag-name-in-use').text();
    
    $.ajax({
      url: '/admin/tag/delete',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-Auth-Token': '{{ auth_token }}'
      },
      data: {
        id: tagId,
        forceDelete: true
      },
      xhrFields: {
        withCredentials: true
      },
      success: function(response) {
        $('#articlesUsingTagModal').modal('hide');
        
        if (response.success) {
          // Use the returned tag name and affected articles count
          showNotification(response.message || (response.tagName + ' tag removed from ' + response.affectedArticles + ' articles'), 'success');
          // Remove the tag from the UI
          $('li.tag-item[data-id="' + tagId + '"]').remove();
          // Update delete button states since hierarchy has changed
          setTimeout(updateDeleteButtonStates, 100);
        } else {
          showNotification('Error: ' + response.message, 'error');
        }
      },
      error: function(xhr, status, error) {
        $('#articlesUsingTagModal').modal('hide');
        console.error('AJAX error:', status, error);
        showNotification('An error occurred while deleting the tag', 'error');
      }
    });
  });
  
  // Search functionality for articles
  $('#article-search').on('keyup', function() {
    var searchText = $(this).val().toLowerCase();
    var found = false;
    
    $('#articles-list tr').each(function() {
      var title = $(this).find('td:first').text().toLowerCase();
      if (title.includes(searchText)) {
        $(this).show();
        found = true;
      } else {
        $(this).hide();
      }
    });
    
    if (found) {
      $('#no-articles-found').addClass('d-none');
    } else {
      $('#no-articles-found').removeClass('d-none').text('No articles found matching "' + searchText + '"');
    }
  });
  
  // Create tag button
  $('#createTag').on('click', function() {
    $('#new-tag-name').val('');
    $('#createTagModal').modal('show');
  });

  // Add form submission handler for the create tag modal
  $('#createTagForm').on('submit', function(e) {
    e.preventDefault();
    
    var name = $('#new-tag-name').val();
    
    if (!name) {
      alert('Please enter a tag name');
      return;
    }
    
    console.log('Submitting create tag form with name:', name);
    
    $.ajax({
      url: '/admin/tag/create',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-Auth-Token': '{{ auth_token }}'
      },
      data: {
        name: name
      },
      xhrFields: {
        withCredentials: true
      },
      success: function(response) {
        console.log('Create tag response:', response);
        
        if (response.success) {
          console.log('Tag created successfully with ID:', response.tagId);
          
          // Add the new tag to the top level of the hierarchy
          var newTagHtml = '<li class="tag-item" data-id="' + response.tagId + '">' +
            '<div class="tag-content d-flex align-items-center p-2 mb-2 bg-light rounded">' +
            '<i class="bi bi-grip-vertical handle mr-2"></i>' +
            '<span class="tag-name">' + name + '</span>' +
            '<div class="ml-auto">' +
            '<button class="btn btn-sm btn-outline-primary add-child-tag" data-id="' + response.tagId + '" data-name="' + name + '" title="Add Child Tag">' +
            '<i class="bi bi-plus"></i>' +
            '</button>' +
            '<button class="btn btn-sm btn-outline-secondary edit-tag" data-id="' + response.tagId + '" data-name="' + name + '" title="Edit Tag">' +
            '<i class="bi bi-pencil"></i>' +
            '</button>' +
            '<button class="btn btn-sm btn-outline-danger delete-tag" data-id="' + response.tagId + '" data-name="' + name + '" title="Delete Tag">' +
            '<i class="bi bi-trash"></i>' +
            '</button>' +
            '</div>' +
            '</div>' +
            '<ul class="sortable list-unstyled ml-4"></ul>' +
            '</li>';
          
          // Add the new tag to the top of the list
          $('#tag-hierarchy > #comm_tags > .sortable').prepend(newTagHtml);
          
          // Close the modal
          $('#createTagModal').modal('hide');

          // Update delete button states since hierarchy may have changed
          setTimeout(updateDeleteButtonStates, 100);

          // Show success notification
          showNotification('Tag created successfully', 'success');
        } else {
          console.error('Error creating tag:', response.message);
          // Show error notification
          showNotification('Error: ' + (response.message || 'Unknown error'), 'error');
          
          // Keep the modal open so the user can try again or cancel
        }
      },
      error: function(xhr, status, error) {
        console.error('AJAX error:', status, error);
        console.error('Response text:', xhr.responseText);
        
        // Try to parse the response as JSON
        let errorMessage = 'An error occurred while creating the tag';
        try {
          if (xhr.responseText) {
            const jsonResponse = JSON.parse(xhr.responseText);
            console.log('Parsed error response:', jsonResponse);
            if (jsonResponse.message) {
              errorMessage = jsonResponse.message;
            }
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
        
        // Show error notification
        showNotification('Error: ' + errorMessage, 'error');
      }
    });
  });

  // Add Child Tag button click handler
  $(document).on('click', '.add-child-tag', function() {
    var id = $(this).data('id');
    var name = $(this).data('name');
    
    console.log('Add child tag clicked for parent ID:', id, 'Name:', name);
    
    $('#parent-tag-id').val(id);
    $('#parent-tag-name').text(name);
    $('#child-tag-name').val('');
    $('#addChildTagModal').modal('show');
  });

  // Add Child Tag form submission
  $('#addChildTagForm').on('submit', function(e) {
    e.preventDefault();
    
    var parentId = $('#parent-tag-id').val();
    var name = $('#child-tag-name').val();
    
    console.log('Submitting child tag form with parentId:', parentId, 'name:', name);
    
    if (!name) {
      alert('Please enter a tag name');
      return;
    }
    
    $.ajax({
      url: '/admin/tag/createChild',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-Auth-Token': '{{ auth_token }}'
      },
      data: {
        parentId: parentId,
        name: name
      },
      xhrFields: {
        withCredentials: true
      },
      success: function(response) {
        if (response.success) {
          // Add the new child tag to the parent
          var newChildHtml = '<li class="tag-item" data-id="' + response.tagId + '">' +
            '<div class="tag-content d-flex align-items-center p-2 mb-2 bg-light rounded">' +
            '<i class="bi bi-grip-vertical handle mr-2"></i>' +
            '<span class="tag-name">' + name + '</span>' +
            '<div class="ml-auto">' +
            '<button class="btn btn-sm btn-outline-primary add-child-tag" data-id="' + response.tagId + '" data-name="' + name + '" title="Add Child Tag">' +
            '<i class="bi bi-plus"></i>' +
            '</button>' +
            '<button class="btn btn-sm btn-outline-secondary edit-tag" data-id="' + response.tagId + '" data-name="' + name + '" title="Edit Tag">' +
            '<i class="bi bi-pencil"></i>' +
            '</button>' +
            '<button class="btn btn-sm btn-outline-danger delete-tag" data-id="' + response.tagId + '" data-name="' + name + '" title="Delete Tag">' +
            '<i class="bi bi-trash"></i>' +
            '</button>' +
            '</div>' +
            '</div>' +
            '<ul class="sortable list-unstyled ml-4"></ul>' +
            '</li>';
          
          // Find the parent tag and add the child to its sortable list
          $('li.tag-item[data-id="' + parentId + '"] > ul.sortable').append(newChildHtml);
          
          // Initialize Sortable on the new list if needed
          var newSortable = document.querySelector('li.tag-item[data-id="' + response.tagId + '"] > ul.sortable');
          if (newSortable) {
            sortables.push(new Sortable(newSortable, {
              group: 'nested',
              animation: 150,
              fallbackOnBody: true,
              swapThreshold: 0.65,
              handle: '.handle',
              onEnd: function(evt) {
                // Update delete button states after drag/drop
                setTimeout(updateDeleteButtonStates, 100);
                saveHierarchy();
              }
            }));
          }
          
          // Close the modal
          $('#addChildTagModal').modal('hide');

          // Update delete button states since hierarchy has changed
          setTimeout(updateDeleteButtonStates, 100);

          // Show success notification
          showNotification('Child tag added successfully', 'success');
        } else {
          // Show error notification
          showNotification('Error: ' + (response.message || 'Unknown error'), 'error');
          
          // Keep the modal open so the user can try again or cancel
        }
      },
      error: function(xhr, status, error) {
        console.error('AJAX error:', status, error);
        
        // Try to parse the response as JSON
        let errorMessage = 'An error occurred while creating the child tag';
        try {
          if (xhr.responseText) {
            const jsonResponse = JSON.parse(xhr.responseText);
            if (jsonResponse.message) {
              errorMessage = jsonResponse.message;
            }
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
        
        // Show error notification
        showNotification('Error: ' + errorMessage, 'error');
      }
    });
  });

  // Also save scroll position before any direct page reloads
  $(window).on('beforeunload', function() {
    sessionStorage.setItem('tagHierarchyScrollPosition', $('#tag-hierarchy').scrollTop());
  });

  // Add CSS for highlighting the modified tag
  $('<style>.highlight-tag { background-color: #ffffd0 !important; transition: background-color 1s; }</style>').appendTo('head');

  // Download CSV button click handler
  $('#download-articles-csv').on('click', function() {
    var tagId = $('#view-tag-name').data('tag-id');
    var tagName = $('#view-tag-name').text();
    
    if (!tagId) {
      showNotification('No tag selected for CSV download', 'error');
      return;
    }
    
    // Show loading state
    var $btn = $(this);
    var originalHtml = $btn.html();
    $btn.html('<i class="bi bi-hourglass-split"></i> Generating CSV...').prop('disabled', true);
    
    // Create a form and submit it to download the CSV
    var $form = $('<form>', {
      'method': 'POST',
      'action': '/admin/tag/exportCsv',
      'target': '_blank'
    });
    
    $form.append($('<input>', {
      'type': 'hidden',
      'name': 'id',
      'value': tagId
    }));
    
    $form.append($('<input>', {
      'type': 'hidden',
      'name': 'auth_token',
      'value': '{{ auth_token }}'
    }));
    
    $('body').append($form);
    $form.submit();
    $form.remove();
    
    // Reset button after a short delay
    setTimeout(function() {
      $btn.html(originalHtml).prop('disabled', false);
    }, 1000);
  });

  // Function to load articles using a tag (for view only)
  function viewArticlesUsingTag(tagId, tagName) {
    console.log('viewArticlesUsingTag called with tagId:', tagId, 'tagName:', tagName); // Debug logging

    // Clear the search input and results when opening the modal
    $('#link-article-search').val('');
    $('#article-search-results').addClass('d-none');
    $('#article-search-results-list').empty();
    $('#no-search-results').addClass('d-none');

    // Clear the articles list to ensure fresh data
    $('#view-articles-list').empty();
    
    $('#view-tag-name').text(tagName).data('tag-id', tagId);
    $('#view-articles-list').empty();
    $('#view-loading-articles').show();
    $('#view-no-articles-found').addClass('d-none');
    $('.table-body-container').hide();
    
    $.ajax({
      url: '/admin/tag/viewArticles',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-Auth-Token': '{{ auth_token }}'
      },
      data: {
        id: tagId
      },
      xhrFields: {
        withCredentials: true
      },
      success: function(response) {
        $('#view-loading-articles').hide();
        console.log('viewArticles response:', response); // Debug logging

        if (response.success && response.articles && response.articles.length > 0) {
          $('#view-article-count-message').text('Found ' + response.totalCount + ' articles using this tag' +
            (response.totalCount > response.articles.length ? ' (showing first ' + response.articles.length + ')' : '') + '.');

          // Show the Unlink All section
          $('#linked-articles-count').text(response.totalCount + ' article' + (response.totalCount === 1 ? '' : 's') + ' linked to this tag');
          $('#unlink-all-container').removeClass('d-none');

          // Extract all article IDs for the Unlink All button
          var allArticleIds = response.articles.map(function(article) {
            return article.id;
          });

          // Update the Unlink All button with onclick handler
          $('#unlink-all-articles').off('click').on('click', function() {
            unlinkAllArticles(this, tagId, allArticleIds);
          });

          // Populate the table with buttons in a btn-group
          response.articles.forEach(function(article) {
            const row = `
              <tr data-article-id="${article.id}">
                <td>${article.id}</td>
                <td>${article.title}</td>
                <td>${formatDate(article.date_pub)}</td>
                <td>${getStatusBadge(article.status)}</td>
                <td class="text-nowrap">
                  <div class="btn-group btn-group-sm">
                    <a href="/news/${article.id}" class="btn btn-outline-primary" target="_blank" title="View Article">
                      <i class="bi bi-eye"></i>
                    </a>
                    <a href="/admin/news/post/${article.id}" class="btn btn-outline-secondary" target="_blank" title="Edit Article">
                      <i class="bi bi-pencil"></i>
                    </a>
                    <button class="btn btn-outline-danger unlink-article" data-article-id="${article.id}" data-tag-id="${tagId}" title="Unlink Article">
                      <i class="bi bi-x-circle"></i>
                    </button>
                  </div>
                </td>
              </tr>
            `;
            $('#view-articles-list').append(row);
          });

          $('.table-body-container').show();
        } else {
          // Hide the Unlink All section
          $('#unlink-all-container').addClass('d-none');
          $('#view-no-articles-found').removeClass('d-none');
          $('#view-article-count-message').text(response.message || 'No articles found using this tag.');
        }
      },
      error: function(xhr, status, error) {
        $('#view-loading-articles').hide();
        
        // Try to parse the response as JSON
        let errorMessage = 'An error occurred while loading articles';
        try {
          if (xhr.responseText) {
            const jsonResponse = JSON.parse(xhr.responseText);
            if (jsonResponse.message) {
              errorMessage = jsonResponse.message;
            }
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
        
        $('#view-no-articles-found').removeClass('d-none').text('Error loading articles: ' + errorMessage);
        console.error('AJAX error:', status, error, xhr.responseText);
      }
    });

    // Only show the modal if it's not already open
    if (!$('#viewArticlesModal').hasClass('show')) {
      $('#viewArticlesModal').modal('show');
    }
  }

  // View articles button click handler
  $(document).on('click', '.view-articles', function(e) {
    // Prevent default action and stop event propagation
    e.preventDefault();
    e.stopPropagation();
    
    var id = $(this).data('id');
    var name = $(this).data('name');
    
    // Call our view function
    viewArticlesUsingTag(id, name);
  });

  // Search functionality for view articles
  $('#view-article-search').on('keyup', function() {
    var searchText = $(this).val().trim();
    var found = false;

    // Check if the search is a comma-separated list of IDs (must contain at least one comma)
    // Allow trailing commas to avoid "no articles found" while typing
    var isCSVSearch = searchText.indexOf(',') !== -1 && /^(\d+\s*,\s*)*\d+\s*,?\s*$/.test(searchText);

    if (isCSVSearch) {
      // Parse the comma-separated list of IDs, filtering out empty values from trailing commas
      var searchIds = searchText.split(',').map(function(id) {
        return id.trim();
      }).filter(function(id) {
        return id !== '';
      });

      console.log('CSV search detected with IDs:', searchIds); // Debug logging

      // Filter articles by the list of IDs
      $('#view-articles-list tr').each(function() {
        var articleId = $(this).find('td:nth-child(1)').text().trim();

        // Check if this article ID is in the search list
        if (searchIds.includes(articleId)) {
          $(this).show();
          found = true;
        } else {
          $(this).hide();
        }
      });

      // Update the message to reflect CSV search
      if (found) {
        $('#view-no-articles-found').addClass('d-none');
      } else {
        $('#view-no-articles-found').removeClass('d-none').text('No articles found with IDs: ' + searchText);
      }
    } else {
      // Regular search by ID or title
      var searchTextLower = searchText.toLowerCase();

      $('#view-articles-list tr').each(function() {
        var id = $(this).find('td:nth-child(1)').text().toLowerCase();
        var title = $(this).find('td:nth-child(2)').text().toLowerCase();

        // Check if either ID or title contains the search text
        if (id.includes(searchTextLower) || title.includes(searchTextLower)) {
          $(this).show();
          found = true;
        } else {
          $(this).hide();
        }
      });

      // Update the message for regular search
      if (found) {
        $('#view-no-articles-found').addClass('d-none');
      } else {
        $('#view-no-articles-found').removeClass('d-none').text('No articles found matching "' + searchText + '"');
      }
    }
  });

  // Search for articles to link
  $('#search-articles-to-link').on('click', function() {
    searchArticlesToLink();
  });

  // Also trigger search on Enter key
  $('#link-article-search').on('keypress', function(e) {
    if (e.which === 13) {
      searchArticlesToLink();
    }
  });

  // Global function to link all articles (accessible directly from inline onclick)
  function linkAllArticles(buttonElement, tagId, articleIds) {
    var $button = $(buttonElement);
    
    // Prevent multiple clicks
    if ($button.prop('disabled')) {
      return;
    }
    
    // Disable the button and show loading state
    $button.prop('disabled', true).html('<i class="bi bi-hourglass-split"></i> Linking...');
    
    console.log('Linking articles to tag', tagId, articleIds);
    
    // Call the backend to link all articles at once
    $.ajax({
      url: '/admin/tag/linkMultipleArticles',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-Auth-Token': '{{ auth_token }}'
      },
      data: {
        articleIds: articleIds,
        tagId: tagId
      },
      xhrFields: {
        withCredentials: true
      },
      success: function(response) {
        // Always reset the button state regardless of success or failure
        $button.prop('disabled', false).html('<i class="bi bi-link"></i> Link All');
        
        if (response.success) {
          // Show success message
          var message = response.message;
          
          // Add details about already linked and not found articles if available
          if (response.alreadyLinkedCount > 0 || response.notFoundCount > 0) {
            var details = [];
            if (response.alreadyLinkedCount > 0) {
              details.push(response.alreadyLinkedCount + ' already linked');
            }
            if (response.notFoundCount > 0) {
              details.push(response.notFoundCount + ' not found');
            }
            if (details.length > 0) {
              message += ' (' + details.join(', ') + ')';
            }
          }
          
          showArticleLinkNotification(message, 'success');

          // Refresh the articles list
          viewArticlesUsingTag(tagId, $('#view-tag-name').text());

          // Clear the search input and results
          $('#link-article-search').val('');
          $('#article-search-results').addClass('d-none');
          $('#article-search-results-list').empty();
        } else {
          // Show error message
          showArticleLinkNotification('Error: ' + (response.message || 'Failed to link articles'), 'error');
        }
      },
      error: function(xhr, status, error) {
        // Always reset the button state on error
        $button.prop('disabled', false).html('<i class="bi bi-link"></i> Link All');
        
        // Try to parse the response as JSON
        let errorMessage = 'An error occurred while linking articles';
        try {
          if (xhr.responseText) {
            const jsonResponse = JSON.parse(xhr.responseText);
            if (jsonResponse.message) {
              errorMessage = jsonResponse.message;
            }
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
        
        // Show error message
        showArticleLinkNotification('Error: ' + errorMessage, 'error');
        console.error('AJAX error:', status, error, xhr.responseText);
      }
    });
  }

  // Function to search for articles to link
  function searchArticlesToLink() {
    var searchText = $('#link-article-search').val().trim();
    var tagId = $('#view-tag-name').data('tag-id');
    
    if (!searchText) {
      $('#article-search-results').addClass('d-none');
      return;
    }
    
    // Show loading indicator
    $('#article-search-results').removeClass('d-none');
    $('#article-search-results-list').empty();
    $('#no-search-results').addClass('d-none');
    $('#search-loading').removeClass('d-none');
    
    $.ajax({
      url: '/admin/tag/searchArticles',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-Auth-Token': '{{ auth_token }}'
      },
      data: {
        search: searchText,
        tagId: tagId
      },
      xhrFields: {
        withCredentials: true
      },
      success: function(response) {
        $('#search-loading').addClass('d-none');
        
        if (response.success && response.articles && response.articles.length > 0) {
          // Add a "Link All" button at the top if there are multiple results
          if (response.articles.length > 1) {
            var linkAllContainer = $('<div class="list-group-item bg-light d-flex justify-content-between align-items-center"></div>');
            var linkAllText = $('<div><strong>Found ' + response.articles.length + ' articles</strong></div>');
            
            // Extract all article IDs for the Link All button
            var allArticleIds = response.articles.map(function(article) {
              return article.id;
            });
            
            // Create the Link All button with an inline onclick handler for immediate binding
            var linkAllButton = $('<button class="btn btn-primary" title="Link All Articles" onclick="linkAllArticles(this, \'' + 
              tagId + '\', ' + JSON.stringify(allArticleIds) + ')"></button>')
              .html('<i class="bi bi-link"></i> Link All');
            
            linkAllContainer.append(linkAllText).append(linkAllButton);
            $('#article-search-results-list').append(linkAllContainer);
          }
          
          // Populate the results list
          response.articles.forEach(function(article) {
            var listItem = $('<div class="list-group-item d-flex justify-content-between align-items-center"></div>');
            var articleInfo = $('<div></div>').text(article.id + ' - ' + article.title);
            var buttonGroup = $('<div class="btn-group btn-group-sm"></div>');
            
            var linkButton = $('<button class="btn btn-outline-primary link-article" title="Link Article"></button>')
              .data('id', article.id)
              .data('tag-id', tagId)
              .html('<i class="bi bi-link"></i>');
            
            buttonGroup.append(linkButton);
            listItem.append(articleInfo).append(buttonGroup);
            $('#article-search-results-list').append(listItem);
          });
          
          // If there's a message (like IDs not found), show it
          if (response.message) {
            $('#no-search-results').removeClass('d-none').text(response.message).addClass('alert-warning').removeClass('alert-info');
          }
        } else {
          $('#no-search-results').removeClass('d-none').addClass('alert-info').removeClass('alert-warning');
          
          // If there's a specific message, use it
          if (response.message) {
            $('#no-search-results').text(response.message);
          } else {
            $('#no-search-results').text('No articles found matching your search.');
          }
        }
      },
      error: function(xhr, status, error) {
        $('#search-loading').addClass('d-none');
        
        // Try to parse the response as JSON
        let errorMessage = 'An error occurred while searching articles';
        try {
          if (xhr.responseText) {
            const jsonResponse = JSON.parse(xhr.responseText);
            if (jsonResponse.message) {
              errorMessage = jsonResponse.message;
            }
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
        
        $('#no-search-results').removeClass('d-none').text('Error: ' + errorMessage).addClass('alert-danger').removeClass('alert-info');
        console.error('AJAX error:', status, error, xhr.responseText);
      }
    });
  }

  // Handle linking an article to a tag
  $(document).on('click', '.link-article', function() {
    var articleId = $(this).data('id');
    var tagId = $(this).data('tag-id');
    var $button = $(this);
    
    // Disable the button and show loading state
    $button.prop('disabled', true).html('<i class="bi bi-hourglass-split"></i>');
    
    $.ajax({
      url: '/admin/tag/linkArticle',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-Auth-Token': '{{ auth_token }}'
      },
      data: {
        articleId: articleId,
        tagId: tagId
      },
      xhrFields: {
        withCredentials: true
      },
      success: function(response) {
        if (response.success) {
          // Show success message with noReload option
          showArticleLinkNotification('Article linked successfully', 'success');

          // Remove the item from search results
          $button.closest('.list-group-item').fadeOut(300, function() {
            $(this).remove();

            // If no more results, show the no results message
            if ($('#article-search-results-list').children().length === 0) {
              $('#no-search-results').removeClass('d-none').text('No more articles found matching your search.');
            }
          });

          // Refresh the articles list
          viewArticlesUsingTag(tagId, $('#view-tag-name').text());

          // Clear the search input
          $('#link-article-search').val('');
        } else {
          // Show error message
          showArticleLinkNotification('Error: ' + (response.message || 'Failed to link article'), 'error');
          
          // Reset the button
          $button.prop('disabled', false).html('<i class="bi bi-link"></i> Link');
        }
      },
      error: function(xhr, status, error) {
        // Try to parse the response as JSON
        let errorMessage = 'An error occurred while linking the article';
        try {
          if (xhr.responseText) {
            const jsonResponse = JSON.parse(xhr.responseText);
            if (jsonResponse.message) {
              errorMessage = jsonResponse.message;
            }
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
        }
        
        // Show error message
        showArticleLinkNotification('Error: ' + errorMessage, 'error');
        
        // Reset the button
        $button.prop('disabled', false).html('<i class="bi bi-link"></i> Link');
        console.error('AJAX error:', status, error, xhr.responseText);
      }
    });
  });

  // Handle unlinking an article from a tag
  $(document).on('click', '.unlink-article', function() {
    var articleId = $(this).data('article-id');
    var tagId = $(this).data('tag-id');
    var $button = $(this);
    var $row = $button.closest('tr');

    // Show confirmation dialog
    if (!confirm('Are you sure you want to unlink this article from the tag?')) {
      return;
    }

    // Disable the button and show loading state
    $button.prop('disabled', true).html('<i class="bi bi-hourglass-split"></i>');

    $.ajax({
      url: '/admin/tag/unlinkArticle',
      type: 'POST',
      dataType: 'json',
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-Auth-Token': '{{ auth_token }}'
      },
      data: {
        articleId: articleId,
        tagId: tagId
      },
      xhrFields: {
        withCredentials: true
      },
      success: function(response) {
        console.log('Unlink response:', response); // Debug logging
        if (response.success) {
          // Show success message
          showArticleLinkNotification('Article unlinked successfully', 'success');

          // Remove the row from the table with animation
          $row.fadeOut(300, function() {
            $(this).remove();

            // Check if there are any articles left
            var remainingRows = $('#view-articles-list tr').length;
            console.log('Remaining rows after unlink:', remainingRows); // Debug logging

            if (remainingRows === 0) {
              // Hide the Unlink All section and show no articles message
              $('#unlink-all-container').addClass('d-none');
              $('#view-no-articles-found').removeClass('d-none').text('No articles found using this tag.');
              $('.table-body-container').hide();
            } else {
              // Update the count in the Unlink All section
              $('#linked-articles-count').text(remainingRows + ' article' + (remainingRows === 1 ? '' : 's') + ' linked to this tag');

              // Update the Unlink All button with new article IDs
              var remainingArticleIds = [];
              $('#view-articles-list tr').each(function() {
                var id = $(this).data('article-id');
                if (id) {
                  remainingArticleIds.push(id);
                }
              });

              $('#unlink-all-articles').off('click').on('click', function() {
                unlinkAllArticles(this, tagId, remainingArticleIds);
              });
            }
          });
        } else {
          // Show error message
          showArticleLinkNotification('Error: ' + (response.message || 'Failed to unlink article'), 'error');

          // Reset the button
          $button.prop('disabled', false).html('<i class="bi bi-x-circle"></i>');
        }
      },
      error: function(xhr, status, error) {
        // Try to parse the response as JSON
        let errorMessage = 'An error occurred while unlinking the article';
        try {
          if (xhr.responseText) {
            const jsonResponse = JSON.parse(xhr.responseText);
            if (jsonResponse.message) {
              errorMessage = jsonResponse.message;
            }
          }
        } catch (e) {
          console.error('Error parsing error response:', e);
        }

        // Show error message
        showArticleLinkNotification('Error: ' + errorMessage, 'error');

        // Reset the button
        $button.prop('disabled', false).html('<i class="bi bi-x-circle"></i>');
        console.error('AJAX error:', status, error, xhr.responseText);
      }
    });
  });

  // Add a new notification function specifically for article linking operations
  function showArticleLinkNotification(message, type) {
    // Create a notification element
    var notification = $('<div class="alert alert-' + (type === 'success' ? 'success' : 'danger') + ' alert-dismissible fade show" role="alert" data-notification-type="' + type + '">' +
      message +
      '<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
      '<span aria-hidden="true">&times;</span>' +
      '</button>' +
      '</div>');

    // Add the notification to the modal
    $('#articlesUsingTagModal .modal-body').prepend(notification);

    // Use the generic timing manager
    manageNotificationTiming(notification, type, 'articleLink', '#articlesUsingTagModal');
  }

  // Clear search when modal is hidden
  $('#viewArticlesModal').on('hidden.bs.modal', function() {
    $('#link-article-search').val('');
    $('#article-search-results').addClass('d-none');
    $('#article-search-results-list').empty();
    $('#no-search-results').addClass('d-none');
  });

  // Update delete button states based on DOM hierarchy
  updateDeleteButtonStates();
});

// Function to update delete button states based on DOM hierarchy
function updateDeleteButtonStates() {
  console.log('=== UPDATING DELETE BUTTON STATES BASED ON DOM ===');

  // First, enable all delete buttons
  $('.delete-tag').each(function() {
    var $btn = $(this);
    var tagId = $btn.data('id');

    // Reset to enabled state
    $btn.prop('disabled', false)
        .removeClass('btn-outline-secondary')
        .addClass('btn-outline-danger')
        .css('opacity', '1')
        .css('cursor', 'pointer')
        .removeAttr('title')
        .removeAttr('data-toggle')
        .removeAttr('data-placement');

    console.log('Reset delete button for tag:', tagId);
  });

  // Now check each tag to see if it has children in the DOM
  $('.tag-item').each(function() {
    var $tagItem = $(this);
    var tagId = $tagItem.data('id');

    // Look for child tags in the nested <ul> within this tag item
    var $childList = $tagItem.children('ul.sortable');
    var hasChildren = $childList.length > 0 && $childList.children('li.tag-item').length > 0;

    console.log('Tag ' + tagId + ' has children in DOM:', hasChildren);

    if (hasChildren) {
      var childCount = $childList.children('li.tag-item').length;
      console.log('Tag ' + tagId + ' has ' + childCount + ' children, disabling delete button');

      // Find and disable the delete button for this tag
      var $deleteBtn = $tagItem.find('> .tag-content .delete-tag');

      if ($deleteBtn.length > 0) {
        $deleteBtn.prop('disabled', true)
                  .removeClass('btn-outline-danger')
                  .addClass('btn-outline-secondary')
                  .css('opacity', '0.6')
                  .css('cursor', 'not-allowed')
                  .attr('title', 'Cannot delete tag with child tags. Delete children first.')
                  .attr('data-toggle', 'tooltip')
                  .attr('data-placement', 'top');

        console.log('Disabled delete button for parent tag:', tagId);
      }
    }
  });

  console.log('=== FINISHED UPDATING DELETE BUTTON STATES ===');
}
</script>

<!-- Remove the second script block that handles tag selection -->
<!-- <script>
$(function() {
  // Tag selection functionality
  $('#comm_tags b').on('click', function() {
    var tag = $(this).data('tag');
    var text = $(this).text();
    
    if ($(this).hasClass('btn-secondary')) {
      // Deselect tag
      $(this).removeClass('btn-secondary').addClass('btn-outline-secondary');
      $('#tag' + tag).remove();
    } else {
      // Select tag
      $(this).removeClass('btn-outline-secondary').addClass('btn-secondary');
      $('#sel_tags').append('<span id="tag' + tag + '"><b class="btn btn-secondary mb-1">' + text + '</b><input type="hidden" name="cats[]" value="' + tag + '"></span>');
    }
  });
});
</script> -->
{% endblock %}
