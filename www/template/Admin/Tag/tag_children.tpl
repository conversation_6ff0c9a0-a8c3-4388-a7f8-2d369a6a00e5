<ul class="sortable list-unstyled ml-4">
  {% for child_key, child_value in comm_tags %}
    {% if child_key|slice(0, parent_id|length) == parent_id and child_key|length == parent_id|length + 2 %}
      <li class="tag-item" data-id="{{ child_key }}">
        <div class="tag-content d-flex align-items-center p-2 mb-2 bg-light rounded">
          <i class="bi bi-grip-vertical handle mr-2"></i>
          <span class="tag-name">{{ child_value }}</span>
          <div class="ml-auto">
            <button class="btn btn-sm btn-outline-primary add-child-tag" data-id="{{ child_key }}" data-name="{{ child_value }}" title="Add Child Tag">
              <i class="bi bi-plus"></i>
            </button>
            <button class="btn btn-sm btn-outline-info view-articles" data-id="{{ child_key }}" data-name="{{ child_value }}" title="View Articles">
              <i class="bi bi-list-ul"></i>
            </button>
            <button class="btn btn-sm btn-outline-secondary edit-tag" data-id="{{ child_key }}" data-name="{{ child_value }}">
              <i class="bi bi-pencil"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger delete-tag" data-id="{{ child_key }}" data-name="{{ child_value }}">
              <i class="bi bi-trash"></i>
            </button>
          </div>
        </div>
        <!-- Recursively include children -->
        {% include 'Admin/Tag/tag_children.tpl' with {'parent_id': child_key, 'comm_tags': comm_tags} %}
      </li>
    {% endif %}
  {% endfor %}
</ul>
