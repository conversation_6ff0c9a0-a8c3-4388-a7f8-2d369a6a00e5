{{ include('/Admin/File/inc.nav.tpl') }}
{{ include('/Admin/File/inc.dir-tool.tpl') }}

<table id="tab_files">
  <tr class="bg-grey"><td></td>
  {% for k,v in cols %}
    <td><a href="?f={{ dir}}&#38;s={{ k }}{% if sort == k %}d{% endif %}"><b{% if sort == k or sort == k ~ 'd' %} class="red"{% endif %}>{{ v }}</b></a></td>
  {% endfor %}
  </tr>
{% for f in files %}
  <tr>
    <td><i class="ft f{{ f.icon }}" data-edit="{{ f.can_edit }}"{% if f.icon == 'img' %} title="{{ f.img_info.0 }} x {{ f.img_info.1 }}"{% endif %}></i></td>
    <td>
  {% if f.icon == 'folder' %}
<a href="?f={{ dir }}/{{ f.Name }}">{{ f.Name }}</a>
  {% elseif web == '*NONE*' %}
<a href="?f={{ dir }}/{{ f.Name }}">{{ f.Name }}</a>
  {% else %}
<a href="{{ web }}/{{ dir }}{% if dir|length %}/{% endif %}{{ f.Name }}" target="_blank">{{ f.Name }}</a>
  {% endif %}
    </td>
    <td class="grey mono small">{{ f.Type }}</td>
    <td class="ar mono small">{{ f.size_str | raw }}</td>
    <td class="grey mono small">{{ f.RWX }}</td>
    <td class="mono small {{ f.mt_color }} delM">{{ f.Modified | date('d/m/Y g:ia') }}</td>
    <td class="grey mono small delM fown">{{ f.Owner }}</td>
    <td class="grey mono small delM">{{ f.Group }}</td>
  </tr>
{% endfor %}
</table>

<div id="dir" class="hide" data-web="{{ web }}">/{{ dir }}{% if dir|length %}/{% endif %}</div>

{{ include('/Admin/File/inc.form-file.tpl') }}
{{ include('/Admin/File/inc.form-folder.tpl') }}
{{ include('/Admin/File/inc.form-add.tpl') }}
{{ include('/Admin/File/inc.form-upload.tpl') }}
{{ include('/Admin/File/inc.css.tpl') }}
{{ include('/Admin/File/inc.js.tpl') }}

