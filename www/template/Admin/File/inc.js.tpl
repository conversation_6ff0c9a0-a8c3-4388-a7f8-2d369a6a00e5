<script>
$(function(){
  $('.ft').on('click', function() {
    var fn = $(this).parent().next().find('a').text();
    $('#file-del').attr('href', $('#file-del').data('href') + fn);
    fn = $('#dir').text() + fn;
    $('#file-edit').attr('href', $('#file-edit').data('href') + fn);
    $('.file-name').val(fn);
    $('#edit-file').removeClass('hide');
    fn = $('#dir').data('web') + fn;

    var prev = $('#img-prev');
    if ($(this).hasClass('fimg')) {
      prev.html('<img src="' + fn + '">');
    } else if ($(this).hasClass('fmp3')) {
      prev.html('<audio controls class="cell-12"><source src="' + fn + '"></audio>');
    } else if ($(this).hasClass('fdvd')) {
      prev.html('<video controls class="cell-12"><source src="' + fn + '"></video>');
    } else if ($(this).hasClass('fpdf')) {
      prev.html('<iframe class="cell-6" src="' + fn + '"></iframe>');
    } else if ($(this).data('edit') && $('#edit-file').hasClass('prev-on')) {
      $.get('?prev=' + $('#old-file').val(), function(data) {
        prev.html('<textarea id="prev-txt" class="cell-12" style="height:300px">' + data + '</textarea>')
      });
    } else {
      prev.html('');
    }
    if ($(this).data('edit')) {
      $('#file-edit').removeClass('hide');
    } else {
      $('#file-edit').addClass('hide');
    }
  });
  $(document).on('click', '#prev-btn', function() {
    $('#edit-file').toggleClass('prev-on');
    if ($('#edit-file').hasClass('prev-on') && !$('#file-edit').hasClass('hide')) {
      $.get('?prev=' + $('#old-file').val(), function(data) {
        $('#img-prev').html('<textarea id="prev-txt" class="cell-12 mono" style="height:300px">' + data + '</textarea>')
      });
    }
  });
  $('#edit-file .x').on('click', function() {
    $('#edit-file').addClass('hide');
  });
  $('.delM').on('click', function() {
    $(this).parent().find('.fown').toggleClass('bg-red');
  });
  $('#delM').on('click', function() {
    if (confirm('Are you sure you want to delete selected files?')) {
      var fn = '';
      $('.fown').each(function() {
        if ($(this).hasClass('bg-red')) {
          fn += '*' + $(this).parent().find('a').text();
        }
      });
      if (fn) {
        window.location = $(this).data('url') + fn;
      }
    }
  })
});
</script>
