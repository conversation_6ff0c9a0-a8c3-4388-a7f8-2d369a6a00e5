<a href="/admin/price/-1" class="rc fr bg-green"><i class="fa fa-plus"></i>Create</a>

<h1>Price Management CMS</h1>

{% if msg %}<p class="rc b-green">{{ msg }}</p>{% endif %}

{% if vid %}

<form action="/admin/price/{{ vid }}" method="post" class="line-s rc bg" style="max-width:500px">

{% if vid > 0 %}
<span class="cell-4 b">ID</span>
<b class="cell-3 rc b-0">{{ vid }}</b>
<i class="cell-1"></i>
<a href="/admin/price/{{ vid}}/del" class="rc bg-red confirm" data-confirm="Are you sure to delete this price?">Delete</a>
<i class="cell-12"></i>
{% endif %}

<span class="cell-4 b">Short Description</span>
<input type="text" name="item_abbr" value="{{ item.item_abbr }}" class="cell-3">
<i class="cell-12"></i>
<span class="cell-4"></span>
<i class="cell-8">Make sure you have at least one price has <b>12M</b> and <b>12S</b> as short description</i>

<span class="cell-4 b">Text Description (PDF)</span>
<input type="text" name="ver_name" value="{{ item.ver_name }}" class="cell-8">
<span class="cell-4 b">Web Description</span>
<input type="text" name="ver_name_web" value="{{ item.ver_name_web }}" class="cell-8">

<span class="cell-4 b">Qty (min ~ max)</span>
<input type="text" name="qty_fm" value="{{ item.qty_fm }}" class="cell-3">
<i class="cell-1 ac">~</i>
<input type="text" name="qty_to" value="{{ item.qty_to }}" class="cell-3">
<i class="cell-12"></i>

<span class="cell-4"></span>
<i class="cell-8">If max Qty = 9999, means unlimited</i>
<span class="cell-4 b">Price</span>
<input type="text" name="price" value="{{ item.price }}" class="cell-3">
<i class="cell-12"></i>
<span class="cell-4 b">Duration in months</span>
<input type="text" name="exp_month" value="{{ item.exp_month }}" class="cell-3">
<i class="cell-12"></i>
<span class="cell-4 b">Web order</span>
<input type="text" name="subs" value="{{ item.subs }}" class="cell-3">
<i class="cell-12"></i>
<span class="cell-4"></span>
<i class="cell-8">shows on subscribe page if greater than 0 (single user subscription only, as multiple subscription is using <b>12M</b>)</i>
<span class="cell-4"></span>
<input type="submit" name="cmd" value="Save" class="rc bg-green cell-3">
</form>

{% endif %}

<table class="table">
  <tr class="bg b">
    <td><a href="?sort=vid">ID</a></td>
    <td><a href="?sort=ver_name">Text Description</a></td>
    <td><a href="?sort=ver_name_web">Web Description</a></td>
    <td><a href="?">Short Desc</a></td>
    <td><a href="?sort=qty_fm">Qty</a></td>
    <td class="ar"><a href="?sort=price">Price</a></td>
    <td class="ar"><a href="?sort=exp_month">Month</a></td>
    <td class="ar"><a href="?sort=subs">Web Order</a></td>
    <td class="ar">Sold</td>
  </tr>

{% for p in price %}
  <tr{% if p.subs %} class="b-green"{% endif %}>
    <td><a href="/admin/price/{{ p.vid }}?sort={{ sort }}">{{ p.vid }}</a></td>
    <td>{{ p.ver_name }}</td>
    <td>{{ p.ver_name_web }}</td>
    <td{% if p.item_abbr == '12M' or p.item_abbr == '12S' %} class="b red"{% endif %}>{{ p.item_abbr }}</td>
    <td>
      {{ p.qty_fm}}
      {% if p.qty_to == 9999 %}
        {% if p.qty_fm > 1 %}+{% endif %}
      {% elseif p.qty_to > 1 %}
        - {{ p.qty_to }}
      {% endif %}
    </td>
    <td class="ar">{{ (p.price / 100) | number_format(2) }}</td>
    <td class="ar">{{ p.exp_month }}</td>
    <td class="ar">{% if p.subs %}{{ p.subs }}{% endif %}</td>
    <td class="ar">{{ p.sold.qty }}</i></td>
  </tr>
{% endfor %}
</table>
