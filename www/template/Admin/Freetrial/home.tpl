<h2>Create a Free Trial (STAFF USE ONLY)</h2>

<script>
  $(document).ready(function(){
  
   $( "#freetrial" ).validate( {
    submitHandler: function(form) {
      form.submit();
    },
      rules: {
        fname: "required",
        lname: "required",
        email: {
          required: true,
          email: true
        },
        position: "required",
        company: "required",
        phone: "required",
        addr: "required",
        city: "required",
        state: "required",
        country: "required",
        zip: "required"
      },
      messages: {
        fname: "Please enter a firstname",
        lname: "Please enter a lastname",
        email: "Please enter a valid email address"
      },
      errorElement: "em",
      errorPlacement: function ( error, element ) {
        // Add the `invalid-feedback` class to the error element
        error.addClass( "invalid-feedback" );

        if ( element.prop( "type" ) === "checkbox" ) {
          error.insertAfter( element.next( "label" ) );
        } else {
          error.insertAfter( element );
        }
      },
      highlight: function ( element, errorClass, validClass ) {
        $( element ).addClass( "is-invalid" ).removeClass( "is-valid" );
      },
      unhighlight: function (element, errorClass, validClass) {
        $( element ).addClass( "is-valid" ).removeClass( "is-invalid" );
      }
    } );

    
  });
</script>

{% if err %}
<div class="alert alert-danger mt-3" role="alert">
<button type="button" class="close text-danger" data-dismiss="alert">&times;</button>
  <h4 class="alert-heading">Error</h4>
  <!--{#<p>{{ err | raw }}</p>#}-->
  <p>{{ err|striptags|raw|nl2br }}</p>
</div>
{% endif %}


<div class="row">

<form name="FreeTrial" id="freetrial" class="col-md-8 xml-auto xmr-auto" method="post" action="/admin/freetrial" autocomplete="off">
<input type="hidden" name="cmd" value="1">

  <div class="form-group formid d-none">
    <label class="formid" for="hnpt">Name:</label>
    <input type="text" name="hnpt" value="" class="hidden" autocomplete="none"> (anti-spam leave blank)
  </div>

  <fieldset>
    <legend class="border-bottom mb-3 pt-3">Subscriber details</legend>

    <div class="form-group {% if site.id != 3 %}hide{% endif %}">
      <label for="user_tag">I'm most interested in:</label>
      <select id="user_tag" name="user_tag" type="text" class="form-control validate[required] input-xlarge" >
      <option value="">-- Please select --</option>
      <option value="SL.AG"{% if form.user_tag == 'SL.AG' %} selected{% endif %}>Agency/third-party recruitment</option>
      <option value="SL.TA"{% if form.user_tag == 'SL.TA' %} selected{% endif %}>In-house talent acquisition and recruitment</option>
      </select>
    </div>


    <div class="form-row">

      <div class="col">
        <div class="form-group">
          <label class="font-weight-bold" for="fname">First name: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
          <input id="fname" name="fname" type="text" class="form-control validate[required] input-medium" size="20" value="{{ form.fname }}" />
        </div>

      </div> <!-- /.col -->
      <div class="col">

        <div class="form-group">
          <label class="font-weight-bold" for="lname">Last name: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
          <input id="lname" name="lname" type="text" class="form-control validate[required] input-large" size="30" value="{{ form.lname }}" />
        </div>

      </div> <!-- /.col -->
    </div> <!-- /.form-row -->

    <div class="form-group">
      <label class="font-weight-bold" for="email">Email: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
      <input id="email" name="email" type="email" class="form-control validate[required,custom[email]] input-xlarge" size="30" value="{{ form.email }}" placeholder="e.g. <EMAIL>" />
    </div>

    <div class="form-group">
      <label class="font-weight-bold" for="position">Job title: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
      <input id="position" name="position" type="text" class="form-control validate[required] input-large" size="30" value="{{ form.position }}" />
    </div>

    <div class="form-group">
      <label class="font-weight-bold" for="company">Company: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
      <input id="company" name="company" type="text" class="form-control validate[required]" size="20" value="{{ form.company }}" />
    </div>

    <div class="form-row">
      <div class="col">
        <div class="font-weight-bold" class="form-group">
          <label for="phone">Work phone: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
          <input id="phone" name="phone" type="tel" class="form-control validate[required]" size="20" value="{{ form.phone }}" />
        </div>
      </div> <!-- /.col -->
      <div class="col">
        <div class="form-group">
          <label class="font-weight-bold" for="mobile">Mobile phone:</label>
          <input id="mobile" name="mobile" type="tel" class="form-control validate[optional]" size="20" value="{{ form.mobile }}" />
        </div>
      </div> <!-- /.col -->
    </div> <!-- /.form-row -->

    <div class="form-row">
      <div class="col">

        <div class="form-group">
          <label class="font-weight-bold" for="addr">Postal address: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
          <input id="addr" name="addr" type="text" class="form-control validate[required]" size="40" value="{{ form.addr }}" />
        </div>

      </div> <!-- /.col -->
      <div class="col">

        <div class="form-group">
          <label class="font-weight-bold" for="city">City: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
          <input id="city" name="city" type="text" class="form-control validate[required]" size="20" value="{{ form.city }}" />
        </div>

      </div> <!-- /.col -->
    </div> <!-- /.form-row -->



    <div class="form-row">
      <div class="col-md-4">

        <div class="form-group">
          <label class="font-weight-bold" for="state">State: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
          <select id="state" name="state" class="form-control validate[required]">
            <option label="Please select" value="">-- Please select --</option>
            {{ state_options | raw }}
          </select>
        </div>
        
      </div> <!-- /.col -->
      <div class="col-md-5 col-sm-5">

        <div class="form-group">
          <label class="font-weight-bold" for="country">Country: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
          <select id="country" name="country" class="form-control validate[required]" data-validation="required">
            <option label="Please select" value="">-- Please select --</option>
            {{ country_options | raw }}              
          </select>

          <script>
          $(function() {
            $('#country').on('change', function() {
              if ($(this).val() == 'Other') {
                window.location.href = '/contact-to-subscribe';
              }
            });
          })
          </script>

        </div>
        
      </div> <!-- /.col -->
      <div class="col-md-3 col-sm-7">

        <div class="form-group">
          <label class="font-weight-bold" for="zip">Postcode: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
          <input id="zip" name="zip" type="text" class="form-control validate[required]" size="10" value="{{ form.zip }}" />
        </div>

      </div> <!-- /.col -->
    </div> <!-- /.form-row -->

  </fieldset>


  <fieldset>
    <legend class="border-bottom mb-3 pt-3">Password and alert frequency</legend>

    <div class="form-row">

      <div class="col">
        <div class="form-group">
          <label class="font-weight-bold" for="pass">Password:</label>
          <input id="" name="" type="text" class="form-control" value="" placeholder="Set by subscriber in welcome email" readonly />
        </div>
      </div> <!-- /.col -->

      <div class="col">
        <div class="form-group">
          <label class="font-weight-bold" for="email_repeat">Email frequency: <i class="bi bi-exclamation-circle text-danger ml-1" data-container="body" data-toggle="popover" data-placement="top" data-content="Required field!"></i></label>
          <select id="email_repeat" name="email_repeat" class="form-control">
          {{ email_repeat_options | raw }}
          </select>
        </div>

      </div> <!-- /.col -->
    </div> <!-- /.row-fluid -->

  </fieldset>

  <div class="bg-light border-top py-4 my-3 text-center">
    <input type="submit" class="btn btn-primary" value="Create a free trial" />
  </div>

  <input class="form-control form-control-sm col-2 float-right text-muted mb-3 t-50" tabindex="10" type="text" id="code" name="code" value="{{ form.code }}" placeholder="Code">
</form>
</div>
</div>
