<div class="fr">
  <a href="#" class="rc bg show" data-src="#edit_temp" style="display:inline-block"><i class="fa fa-pen"></i>Update Info</a>
  <a href="#" class="rc bg show" data-src="#edit_temp_txt" style="display:inline-block"><i class="fa fa-pen"></i>Update Text</a>
  <a href="/admin/email-temp/copy/{{ temp.id }}" class="rc bg" style="display:inline-block"><i class="fa fa-copy"></i>Duplicate</a>
</div>
<h1><a href="/admin/email-temp">Email Templates</a> # {{ temp.id }}</h1>

<p class="b-green rc">Update a template will <b>NOT</b> affect any campaigns which are already created.</p>

<table class="table">
  <tr class="bg b">
    <td>ID</td>
    <td>Created</td>
    <td>Cron</td>
    <td>Name</td>
    <td>Type</td>
    <td>Repeat</td>
    <td>Template</td>
    <td>Users</td>
    <td>State</td>
    <td>Tag</td>
    <td>News</td>
  </tr>
  <tr>
    <td><a href="/admin/email-temp/{{ temp.id }}">{{ temp.id }}</a></td>
    <td>{{ temp.created | date('d-M-Y') }}</td>
    <td>{% if temp.active %}<b class="red">Auto</b>{% endif %}</td>
    <td>{{ temp.name }}</td>
    <td class="{% if temp.temp_type == 'Daily' %}green{% elseif temp.temp_type == 'Weekly' %}blue{% elseif temp.temp_type == 'Manual' %}grey{% else %}red{% endif %}">{{ temp.temp_type }}</td>
    <td{% if not temp.active %} class="grey"{% endif %}>{{ temp.repeat_at }}</td>
    <td>{{ temp.view_tpl }}</td>
    <td class="{% if temp.users == 'ALL CUSTOMER' %}green{% elseif temp.users == 'ALL CUSTOMER SET' %}grey{% elseif temp.users == 'INACTIVE FREETRIAL' %}blue{% elseif temp.users == 'ALL PREMIUM' %}gold{% elseif temp.users == 'ALL FREETRIAL' %}dark{% else %}red{% endif %}">{{ temp.users | slice(0, 30) }}</td>
    <td>{{ temp.user_state }}</td>
    <td>{{ temp.user_tag }}</td>
    <td>{{ temp.news | slice(0, 13) }}{% if temp.news|length > 10 %}...{% endif %}</td>
  </tr>
</table>

<div class="line">
  <div class="cell-6 mr-3">
    <p class="rc b-blue"><b>Subject:</b> {{ temp.subject }}</p>
    <p class="rc b-green"><b>Preview text:</b> {{ temp.preview_txt }}</p>
    <pre class="b-gold rc"><b>News:</b><br>{{ temp.news }}</pre>
    <pre class="b-green rc">{{ temp.txt }}</pre>
  </div>
  <div class="cell-6">
    <div class="tab bg-dark b">Email preview:{% if camp_cur %}<i class="fr">Campaign # {{ camp_cur }}</i>{% endif %}</div>
    <iframe src="/admin/camp-preview/{{ camp_cur }}/{{ temp.id }}" class="w-100 b-0 bg-grey" style="height:300px"></iframe>
  </div>
</div>

<br>
<div class="fr rc bg"><a href="/admin/email-temp/addCamp/{{ temp.id }}"><i class="fa fa-plus"></i>New Campaign</a></div>
<h1>Email Campaigns using this template</h1>

<p class="b-gold rc">You are <b>NOT</b> able to update a campaign once emails are sent. You are able to <b>Stop</b> a campaign before emails finish sending.</p>

{% if err %}
  <p class="rc b-red">{{ err | raw }}</p>
{% endif %}

<table class="table">
<tr class="bg-grey b">
  <td>Subject</td>
  <td>Date Time</td>
  <td class="ar">Emails</td>
  <td class="ar">Sent</td>
  <td class="ar">Stop</td>
  <td class="ar">Error</td>
  <!--td class="ar">Bounced</td-->
  <td class="ar">Opened</td>
  <td class="ar">Clicked</td>
  <td class="ar">unsub</td>
</tr>
{% for c in camp %}
<tr{% if c.id == camp_cur %} class="bg"{% endif %}>
  <td>
    <i class="fa fa-pen show" data-src="#cid{{ c.id }}"></i>
    <a href="/admin/email-temp/{{ temp.id }}?cid={{ c.id }}" title="Preview">{{ c.subject }}</a>

    <!--a class="show fl edit_camp" data-id="{{ c.id }}" data-src="#edit_camp" title="Edit" data-txt="{{ c.txt }}" data-email="{{ c.num }}" data-sent="{{ c.sent }}"><i class="fa fa-pen"></i></a-->

  </td>
  <td>{{ c.ts | date('d-M-Y H:i') }}{% if c.num and c.ts != c.ts_send %}<br><span class="green" title="send on">{{ c.ts_send | date('d-M-Y H:i') }}</span>{% endif %}</td>
  <td class="ar">
    {% if c.num  %}
      <b class="help" data-help="{{ c.stat }}">{{ c.num  }}</b>
    {% endif %}
  </td>
  <td class="ar">{{ c.sent }}{% if c.num %}<br><i class="grey mono">{{ (c.sent / c.num * 100) | round(1) }}%</i>{% endif %}</td>
  <td class="ar red">{% if c.stop %}{{ c.stop }}{% endif %}</td>
  <td class="ar">{{ c.err }}{% if c.num %}<br><i class="grey mono">{{ (c.err / c.num * 100) | round(1) }}%</i>{% endif %}</td>
  <!--td class="ar">{{ c.bounced }}{% if c.num %}<br><i class="grey mono">{{ (c.bounced / c.num * 100) | round(1) }}%</i>{% endif %}</td-->
  <td class="ar">{{ c.opened }}{% if c.num %}<br><i class="grey mono">{{ (c.opened / c.num * 100) | round(1) }}%</i>{% endif %}</td>
  <td class="ar">{{ c.clicked }}{% if c.num %}<br><i class="grey mono">{{ (c.clicked / c.num * 100) | round(1) }}%</i>{% endif %}</td>
  <td class="ar">{{ c.unsub }}{% if c.num %}<br><i class="grey mono">{{ (c.unsub / c.num * 100) | round(1) }}%</i>{% endif %}</td>
</tr>
<tr id="cid{{ c.id }}" class="hide"><td colspan="8" class="b-gold">
  <form action="/admin/email-temp/test-email/{{ c.id }}">
    <a href="/admin/email-temp/del-camp/{{ c.id }}" class="confirm rc bg-red" data-confirm="You can only delete an email campaign which has not sent yet. Are you sure to delete this campaign?">Delete Campaign</a>
    <a href="/admin/email-temp/edit/{{ temp.id }}?camp_id={{ c.id }}&cmd=Stop+Send" class="rc bg-gold">Stop Send</a>
    <a href="/admin/email-temp/edit/{{ temp.id }}?camp_id={{ c.id }}&cmd=Queue+Email" class="rc bg-blue">Queue Email</a>
    <a href="/admin/email-temp/edit/{{ temp.id }}?camp_id={{ c.id }}&cmd=Send+Email" class="rc bg-green">Send Email</a>

    <input type="text" name="email" placeholder="Email" class="cell-3 rc">
    <input type="submit" name="cmd" value="Send Test Email" class="bg-grey">
  </form>
</td></tr>
{% endfor %}
</table>

{{ include('/Admin/EmailTemp/info.edit.tpl') }}

<form action="/admin/email-temp/edit/{{ temp.id }}" method="post">
<input type="hidden" name="cmd" value="save_temp_txt">
<div id="edit_temp_txt" class="pop hide">
  <div class="cell-6" style="margin-top:30px">
    <div class="bg-blue tab b">Update Email Template # {{ temp.id }}</div>
    <div class="bg tabB">
      <p>
        <b>Subject:</b><br>
        <input type="text" name="subject" value="{{ temp.subject }}" class="cell-12" placeholder="
          {% if temp.temp_type == 'Weekly' %}
            {{ email.subject.defa_weekly }}
          {% elseif temp.temp_type == 'Daily' %}
            {{ email.subject.defa_daily }}
          {% else %}
            leave blank to use news headline
          {% endif %}
        ">
      </p>
      <p>
        <b>Preview text in email folder:</b><br>
        <input type="text" name="preview_txt" value="{{ temp.preview_txt }}" class="cell-12">
      </p>
      <p>
        <b>Template file:</b> (if blank, use custom text)<br>
        <select class="cell-6 mr-2" name="view_tpl">
          {{ file_options | raw }}
        </select>
        .tpl
      </p>
      <p>
        <b>Custom Text:</b><br>
        <i class="grey">If template file is blank, use this text; otherwise add this text to template file</i><br>
        <textarea name="txt" class="cell-12 rc" style="height:100px" placeholder="optional custom text">{{ temp.txt }}</textarea>
      </p>
      <p>
        <b>News:</b>
        <i class="grey">eg<br><b>news_new:</b> 1,2,3,4<br><b>news_more:</b> 5,6,7,8<br><b>NO-NEWS:</b> no news, just send out emails contain customer text only</i>
        <textarea name="news" class="cell-12 rc" placeholder="leave blank to use latest news since last sent">{{ temp.news }}</textarea>
      </p>
      {% for x in 1..3 %}
        <p>
          <b>List {{ x }}:</b>
          <textarea name="list{{ x }}" class="cell-12 rc">{{ temp['list' ~ x] }}</textarea>
        </p>
      {% endfor %}
      <p><input type="submit" value="Save" class="rc bg-green"></p>
    </div>
  </div>
</div>
</form>

<form action="/admin/email-temp/edit/{{ temp.id }}" method="post">
<input type="hidden" name="camp_id" id="camp_id">
<div id="edit_camp" class="pop hide">
  <div style="margin-top:30px">
    <div class="bg-blue tab b">Edit Email Campaign</div>
    <div class="bg tabB">
      <!--input type="text" name="subject" value="{{ temp.subject }}" class="cell-12" id="camp_subj" title="Email Subject" placeholder="Email Subject">
      <textarea name="txt" class="cell-12 rc mono" style="height:350px" id="camp_txt">{{ temp.txt }}</textarea-->
      <input type="submit" name="cmd" value="Save Camp" class="rc bg-green mt-3" id="save_camp">
      <input type="submit" name="cmd" value="Queue Email" class="rc bg-blue mt-3" id="save_queue">
      <input type="submit" name="cmd" value="Send Email" class="rc bg-red mt-3" id="save_send">
      <input type="submit" name="cmd" value="Stop Send" class="rc bg-gold mt-3" id="save_stop">
    </div>
  </div>
</div>
</form>


