<h1>Email Templates</h1>

<p class="b-gold rc">Publish news will use <b>ID = 1</b> <i class="red">Instant</i> template -- please do not update this template</p>

<p class="b-green rc"><PERSON><PERSON> will auto creates campaigns from <b>Daily / Weekly</b> templates which is <b class="red">Auto</b></p>

<div class="pop hide" id="help-news" style="margin-top:30px">
  <div class="cell-4 rc b-gold">
    If news = <b>NO-NEWS</b>, email will skip news check, and send out without news.
  </div>
</div>
<div class="pop hide" id="help-repeat" style="margin-top:30px">
  <div class="cell-4">
    <div class="tab bg-dark">What does Repeat Mean</div>
    <div class="tabB bg">
      <p>For <b>Daily</b> template, repeat between <b>00:00</b> ~ <b>23:59</b>. eg repeat = <b>11:25</b>, email will send at 11:25 or after.</p>
      <p>For <b>Weekly</b> template, repeat = <b>Mon, Tue, ..., Fri</b>. eg repeat = <b>Fri 09:25</b>, email will send at 09:25 on Friday.</p>
      <p>For <b>Instant</b> template, no need repeat, as email is sent via click "Publish" button.</p>
      <p>For <b>Manual</b> template, if repeat is blank, email will send at click, or repeat can be a date eg 2020-06-20 11:22</p>
    </div>
  </div>
</div>
<div class="pop hide" id="help-user" style="margin-top:30px">
  <div class="cell-4">
    <div class="tab bg-dark">User Groups</div>
    <div class="tabB bg">
      <p>1 - Who are they?</p>
      <p>(a) <b>ALL CUSTOMER</b> = Basic / Freetrial / Premium
        <br><i class="green">(b)</i> <b>ALL CUSTOMER SET</b> <i class="mono red">[Default]</i> = Freetrial / Premium
        <br>(c) <b>ALL STAFF</b> = Staff
        <br><i class="green">(d)</i> <b>ALL BASIC</b> = Basic
        <br><i class="green">(e)</i> <b>ALL FREETRIAL</b> = Freetrial
        <br><i class="green">(f)</i> <b>ALL PREMIUM</b> = Premium
        <br><i class="blue">(g)</i> <b>INACTIVE FREETRIAL</b> = Freetrial
      </p>
      <p>2 - Status check</p>
      <p>(a-f) All status = Active
        <br><i class="blue">(g)</i> INACTIVE FREETRIAL status = Expired / Closed OR whose trials are expired
      </p>
      <p>3 - Email Type Check</p>
      <p>If email type = <u>Instant / Daily</u>, also check user set match for groups <b class="green">b d e f</b></p>
      <p>If email type = Weekly / Manual, skip to check</p>
    </div>
  </div>
</div>

<table class="table">
  <tr class="bg b">
    <td>ID</td>
    <td>Created</td>
    <td>Cron</td>
    <td>Name</td>
    <td>Type</td>
    <td><span class="show" data-src="#help-repeat">Repeat <i class="fa fa-question small blue"></i></span></td>
    <td>Template</td>
    <td><span class="show" data-src="#help-user">Users <i class="fa fa-question small blue"></i></span></td>
    <td>State</td>
    <td>Tag</td>
    <td><span class="show" data-src="#help-news">News <i class="fa fa-question small blue"></i></span></td>
    <td>Last Camp Added</td>
  </tr>

{% for t in temps %}
  <tr>
    <td><a href="/admin/email-temp/{{ t.id }}">{{ t.id }}</a></td>
    <td>{{ t.created | date('d-M-Y') }}</td>
    <td>{% if t.active %}<b class="red">Auto</b>{% endif %}</td>
    <td{% if t.name == 'Instant News' %} class="b"{% endif %}>{{ t.name }}</td>
    <td class="{% if t.temp_type == 'Daily' %}green{% elseif t.temp_type == 'Weekly' %}blue{% elseif t.temp_type == 'Manual' %}grey{% else %}red{% endif %}">{{ t.temp_type }}</td>
    <td{% if not t.active %} class="grey"{% endif %}>{{ t.repeat_at }}</td>
    <td>{{ t.view_tpl }}</td>
    <td class="{% if t.users == 'ALL CUSTOMER' %}green{% elseif t.users == 'ALL CUSTOMER SET' %}grey{% elseif t.users == 'INACTIVE FREETRIAL' %}blue{% elseif t.users == 'ALL PREMIUM' %}gold{% elseif t.users == 'ALL FREETRIAL' %}dark{% else %}red{% endif %}">{{ t.users | slice(0, 30) }}</td>
    <td>{{ t.user_state }}</td>
    <td>{{ t.user_tag }}</td>
    <td>{{ t.news | slice(0, 13) }}{% if t.news|length > 10 %}...{% endif %}</td>
    <td>{% if t.email %}
      <a href="/admin/camp-preview/{{ t.email.camp_id }}" target="_blank" title="{{ t.email.subject }}">{{ t.email.queued_ts | date('d-M H:i') }} ({{ t.email.num | default(0) }})</a>
    {% endif %}</td>
  </tr>
{% endfor %}
</table>

{{ include('/Admin/pgno.tpl') }}
