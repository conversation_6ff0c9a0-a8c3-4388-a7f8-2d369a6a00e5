<form action="/admin/email-temp/edit/{{ temp.id }}" method="post">
<input type="hidden" name="cmd" value="save_temp">
<div id="edit_temp" class="pop hide">
  <div class="cell-4" style="margin-top:30px">
    <div class="bg-blue tab b">Update Email Template # {{ temp.id }}</div>
    <div class="bg tabB">
      <p>
        <b>Name:</b><br>
        <input type="text" name="name" value="{{ temp.name }}" class="cell-12">
      </p>
      <div class="line-s mb-3">
        <p class="cell-6 mr-1">
          <b>Type:</b><br>
          <select class="cell-12" name="temp_type" id="t-type">
            {{ type_options | raw }}
          </select>
        </p>
      </div>
      <div class="mb-3 line-s{% if temp.temp_type == 'Instant' %} hide{% endif %}" id="t-repeat">
        <b class="cell-12">Email sent / repeat at:</b>
        <select class="cell-2 t-ymd{% if temp.temp_type != 'Manual' %} hide{% endif %}" name="repeat[d]">
          <option value="">Day</option>
          {% for d in 1..31 %}
            <option value="{{ d }}"{% if temp.repeat.d == d %} selected{% endif %}>{{ '%02d'|format(d) }}</option>
          {% endfor %}
        </select>
        <select class="cell-2 t-ymd{% if temp.temp_type != 'Manual' %} hide{% endif %}" name="repeat[m]">
          {% for i,m in ['Month', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
            <option value="{{ i }}"{% if temp.repeat.m == i %} selected{% endif %}>{{ m }}</option>
          {% endfor %}
        </select>
        <select class="cell-2 t-ymd{% if temp.temp_type != 'Manual' %} hide{% endif %}" name="repeat[y]">
          <option value="">Year</option>
          {% for y in 2000..2030 %}
            <option value="{{ y }}"{% if temp.repeat.y == y %} selected{% endif %}>{{ y }}</option>
          {% endfor %}
        </select>
        <select class="cell-2 t-wk{% if temp.temp_type != 'Weekly' %} hide{% endif %}" name="repeat[w]">
          <option value="">Week</option>
          {% for w in ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'] %}
            <option value="{{ w }}"{% if temp.repeat.w == w %} selected{% endif %}>{{ w }}</option>
          {% endfor %}
        </select>
        <select class="cell-2" name="repeat[h]">
          <option value="">Hour</option>
          {% for h in 0..23 %}
            <option value="{{ h }}"{% if temp.repeat.h == h %} selected{% endif %}>{{ '%02d'|format(h) }}</option>
          {% endfor %}
        </select>
        <select class="cell-2" name="repeat[i]">
          <option value="">Minute</option>
          {% for i in 0..59 %}
            <option value="{{ i }}"{% if temp.repeat.i == i %} selected{% endif %}>{{ '%02d'|format(i) }}</option>
          {% endfor %}
        </select>
      </div>

      <p>
        <b>Send campaign to:</b><br>
        <select class="cell-12" name="users">
          <option value="">List of specific users</option>
          {% for x in ['ALL CUSTOMER SET', 'ALL STAFF', 'ALL BASIC', 'ALL PREMIUM'] %}
            <option value="{{ x }}"{% if temp.users == x %} SELECTED{% endif %}>{{ x }}</option>
          {% endfor %}
        </select>
      </p>
      <p><label>
        <input type="checkbox" name="active" value="1"{% if temp.active %} checked{% endif %}>
        <b>Auto</b> run by cron (<b>daily, weekly</b> only)
      </lable></p>

      <p class="dot b">The following for manual email only:</p>
      <div class="line-s">
        <p class="cell-6 mr-1">
          <b>State:</b><br>
          <select class="cell-12" name="user_state">
            {{ state_options | raw }}
          </select>
        </p>
        <p class="cell-6 ml-1">
          <b>User Tag:</b><br>
          <select class="cell-12" name="user_tag">
            <option value="">Please select (optional)</option>
            {{ tag_options | raw }}
          </select>
        </p>
      </div>
      <p><b>List of specific users:</b> (IDs separated with new line or comma)</p>
      <textarea class="cell-12" name="emails">{{ temp.emails }}</textarea>

      <p><input type="submit" value="Save" class="rc bg-green"></p>
    </div>
  </div>
</div>
</form>

<script>
$(function() {
  $('#t-type').on('change', function() {
    var tt = $(this).val();
    if (tt == 'Instant') {
      $('#t-repeat').addClass('hide');
    } else {
      $('#t-repeat').removeClass('hide');
      if (tt == 'Daily') {
        $('.t-ymd').addClass('hide');
        $('.t-wk').addClass('hide');
      } else if (tt == 'Weekly') {
        $('.t-ymd').addClass('hide');
        $('.t-wk').removeClass('hide');
      } else {
        $('.t-ymd').removeClass('hide');
        $('.t-wk').addClass('hide');
      }
    }
  });
})
</script>
