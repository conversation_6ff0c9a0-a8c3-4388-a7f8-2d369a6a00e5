
<script>
$(document).ready(function(){
  $("#userDetails").validate({
    submitHandler: function(form) {
    
                 var purchaserId = $('#purchaser').val();
                 var orgin_purchaserId = $('#purchaser').attr('data-purchaser');
                 var sameCompany = 0;


                 if(orgin_purchaserId == purchaserId){
                      sameCompany = 1;
                 }
            
                 
               $.ajax({url: "/admin/user/purchaser_limit", type: "post", data: {purchaserId: purchaserId},
                    success: function(result) {
                      if(result == 'true' || sameCompany == 1){
                                  form.submit();
                      }else{
                        $("#purchaser").notify("The purchaser already reached the subscriber limit", "error", {showAnimation: 'slideDown', showDuration: 1000});

                      }
                    }
                    });
    },
    rules: {
      purchaser: "required",
    },
    messages: {
      purchaser: "Please enter purchaser",
    },
    errorElement: "em",
    errorPlacement: function ( error, element ) {
      // Add the `invalid-feedback` class to the error element
      error.addClass( "invalid-feedback" );

      if ( element.prop( "type" ) === "checkbox" ) {
        error.insertAfter( element.next( "label" ) );
      } else {
        error.insertAfter( element );
      }
    },
    highlight: function ( element, errorClass, validClass ) {
      $( element ).addClass( "is-invalid" ).removeClass( "is-valid" );
    },
    unhighlight: function (element, errorClass, validClass) {
      $( element ).addClass( "is-valid" ).removeClass( "is-invalid" );
    }
  });
  });
</script>
<form id="userDetails" action="/admin/user/edit/{{ cust.uid }}" method="post">
<input type="hidden" name="cmd" value="save_info">
<div class="pop hide" id="edit_secure">
  <div class="cell-4" style="margin-top:30px">
    <div class="bg-gold tab b">Update Profile</div>
    <div class="bg tabB">
      <div class="line-s">
        <p class="cell-6">
          <b>Access</b><br>
          <select class="cell-12" name="acs">
            {{ cust.acs_options | raw }}
          </select>
        </p>
        <p class="cell-6">
          <b>Status</b><br>
          <select class="cell-12" name="status">
            {{ cust.status_options | raw }}
          </select>
        </p>
      </div>
      <p class="line-s">
        <b class="cell-12">Name</b>
        <input type="text" placeholder="First Name" class="cell-6" name="fname" value="{{ cust.fname }}">
        <input type="text" placeholder="Last Name" class="cell-6"  name="lname" value="{{ cust.lname }}">
      </p>
      <p class="line-s">
        <b class="cell-12">User flags</b>
        {{ cust.tag_options | raw }}
      </p>
      <div class="line-s">
      <p class="cell-6">
        <label><input type="checkbox"{% if cust.is_admin %} checked{% endif %} name="is_admin" value="1"> Is Company Admin</label>
      </p>
      <p class="cell-6">
        <label><input type="checkbox"{% if cust.is_organisation %} checked{% endif %} name="is_organisation" value="1"> Manage Subscribers</label>
      </p>
      </div>
      <div class="line-s">
        <p class="cell-6">
          <b>Purchaser</b><br>
          <input type="text" id="purchaser" data-purchaser="{{cust.purchaser}}" placeholder="Purchaser company ID" class="cell-12 purchaser" name="purchaser" value="{{ cust.purchaser }}">
        </p>
        <p class="cell-6">
          <b>Email Repeat</b><br>
          <select class="cell-12" name="email_repeat">
            {{ cust.email_repeat_options | raw }}
          </select>
        </p>
      </div>
      <p><input type="submit" value="Save" class="rc bg-green"></p>
    </div>
  </div>
</div>
</form>
