<script>
$(document).ready(function(){
  $("#userEmail").validate({
    submitHandler: function(form) {
        var blocked_domain = '';
    var enable_subscriber_management = 0;

               $.ajax({url: "/manage/check_domain", type: "post", data: { uid : '{{ cust.uid }}' },
                    success: function(result) {
                      var result = $.parseJSON(result);
                        blocked_domain = result['sub_domian'];
                        enable_subscriber_management = result['enable_subscriber_management'];
                                             var email = $('#email').val();
                      var sub_domain = email.split("@");
                            if(sub_domain.length >= 2){
                                var subDomain = sub_domain[1];
                            }else{
                                var subDomain = '';
                            }
                      if(blocked_domain.includes(subDomain) || enable_subscriber_management == 0){
                                  form.submit();
                      }else{
                        $('#email').addClass( "is-invalid" ).removeClass( "is-valid" );
                        $("#email").notify("This email domain can't be used with this account. Use the subscribed email domain or contact us for help.", "error", {showAnimation: 'slideDown', showDuration: 1000});

                      }
                    }
                   });

    },
    rules: {
      email: {
        required:  {
                depends:function(){
                    $(this).val($.trim($(this).val()));
                    return true;
                }   
            },
            email: true,
      },
    },
    messages: {
      email: "Please enter a valid email address",
    },
    errorElement: "em",
    errorPlacement: function ( error, element ) {
      // Add the `invalid-feedback` class to the error element
      error.addClass( "invalid-feedback" );

      if ( element.prop( "type" ) === "checkbox" ) {
        error.insertAfter( element.next( "label" ) );
      } else {
        error.insertAfter( element );
      }
    },
    highlight: function ( element, errorClass, validClass ) {
      $( element ).addClass( "is-invalid" ).removeClass( "is-valid" );
    },
    unhighlight: function (element, errorClass, validClass) {
      $( element ).addClass( "is-valid" ).removeClass( "is-invalid" );
    }
  });
  });
</script>
<form id="userEmail" action="/admin/user/edit/{{ cust.uid }}" method="post">
<input type="hidden" name="cmd" value="save_acs">
<div class="pop hide" id="edit_acs">
  <div class="cell-4" style="margin-top:30px">
    <div class="bg-red tab b">Update Security</div>
    <div class="bg tabB">
      <p>
        <b>Email</b><br>
        <input type="text" id="email" placeholder="Email" class="cell-12" name="email" value="{{ cust.email }}">
      </p>
      <p>
        <b>Password</b><br>
        <input type="text" placeholder="Password, leave blank no change" class="cell-12" name="pass">
      </p>
      <p><input type="submit" value="Save" class="rc bg-green"></p>
    </div>
  </div>
</div>
</form>
