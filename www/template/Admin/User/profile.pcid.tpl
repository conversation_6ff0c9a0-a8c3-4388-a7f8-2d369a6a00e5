{{ include('/Admin/User/profile.pcid.limit.tpl') }}

<i class="blue show fr pr-2" data-src="#edit_pcid"><i class="fa fa-pen"></i>Edit</i>

{{ include('/Admin/User/profile.form.pcid.tpl') }}

<h2>Other info</h2>

<table class="hide">
  <tr class="bg">
    <td class="cell-3">
      {% if cust.pcid1 %}<a href="/admin/user/edit/{{ cust.uid }}?cmd=del_pcid&pcid=1" title="Delete PCID"><i class="fa fa-trash red"></i></a>{% endif %}
      {{ cust.pcid1 }}
      {% if cust.pcid1_ts and cust.pcid1_ts != '0000-00-00 00:00:00' %}
        <br>{{ cust.pcid1_ts | date('j-M-Y') }}
      {% endif %}
    </td>
    <td class="line-s">
    {% for i in 2..5 %}
    <div class="cell-3">
      {% if attribute(_context.cust, 'pcid' ~ i) %}<a href="/admin/user/edit/{{ cust.uid }}?cmd=del_pcid&pcid={{ i }}" title="Delete PCID"><i class="fa fa-trash red"></i></a>{% endif %}
      {{ attribute(_context.cust, 'pcid' ~ i) }}
      {% set pcid = attribute(_context.cust, 'pcid' ~ i ~ '_ts') %}
      {% if pcid and pcid != '0000-00-00 00:00:00' %}
        <br>{{ pcid | date('j-M-Y') }}
      {% endif %}
    </div>
    {% endfor %}
    </td>
  </tr>
</table>

<table class="table">
  <tr class="bg">
    <td class="cell-3">Freetrial Started</td>
    <td class="line-s">
    {% for i in 1..3 %}
      <div class="cell-3"><i class="grey">{{ i }} :</i>
      {% set trial = attribute(_context.cust, 'trial' ~ i) %}
      {% if trial and trial != '0000-00-00' %}
        {{ trial | date('j-M-Y') }}
      {% endif %}
      </div>
    {% endfor %}
      <div class="cell-3"></div>
    </td>
  </tr>
</table>

<table class="table">
  <tr class="bg">
    <td class="cell-3">Market Code</td>
    <td class="line-s">
    {% for i in 1..3 %}
      <div class="cell-3"><i class="grey">{{ i }} :</i>
      {{ attribute(_context.cust, 'market_code' ~ i) }}
      </div>
    {% endfor %}
    </td>
  </tr>
</table>
