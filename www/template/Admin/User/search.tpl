<form class="formH label-0 line-m pb-3" action="/admin/user" method="post" id="formS">
  <input type="text" name="keyw" placeholder="Name / Email / Addr / Company / Position / Code" class="cell-4">
  <input type="text" name="email" value="{{ form.email }}" placeholder="Email" class="cell-2">
  <input type="text" name="company" value="{{ form.company }}" placeholder="Company name or id" class="cell-2" title=">9 to exclude freetrial and archive">
  <input type="text" name="name" value="{{ form.name }}" placeholder="Name" class="cell-2">
  <select name="status" class="cell-2">
    {{ status_options | raw }}
    <option value="login_frozen"{% if form.status == 'login_frozen' %} selected{% endif %}>Login Frozen</option>
  </select>
  <select name="acs" class="cell-2">
    {{ acs_options | raw }}
  </select>

  <select name="state" class="cell-2">
    {{ state_options | raw }}
  </select>
  <select class="cell-2" name="email_repeat">
    <option value="">Email Repeat</option>
    {{ email_repeat_options | raw }}
  </select>

  <div class="cell-2 line-s" style="margin:0" title="Freetrial Expire on or before">
    <select class="cell-4" name="trial_d">
      <option value="">Day</option>
      {% for d in 01..31 %}
        <option value="{{ d }}"{% if form.trial_d == d %} selected{% endif %}>{{ d }}</option>
      {% endfor %}
    </select>
    <select class="cell-4" name="trial_m">
      <option value="">Month</option>
      {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
      {% if i %}
        <option value="{{ i }}"{% if form.trial_m == i %} selected{% endif %}>{{ m }}</option>
      {% endif %}
      {% endfor %}
    </select>
    <select class="cell-4" name="trial_y">
      <option value="">Year</option>
      {% for y in 2010..2030 %}
        <option value="{{ y }}"{% if form.trial_y == y %} selected{% endif %}>{{ y }}</option>
      {% endfor %}
    </select>
  </div>

  <select name="pcid_limit" class="cell-2">
    <option value="">Default PCID limit</option>
    <option value="5">PCID limit = 5</option>
    <option value="20">PCID limit = 20</option>
    <option value="999999">Unlimited</option>
  </select>

  <select name="company_status" class="cell-2">
    <option value="">Purchaser Status</option>
    {{ company_status_options | raw }}
  </select>

  <input type="text" name="uid" value="{{ form.uid }}" placeholder="ID" class="cell-2">
  <label class="cell-2">&nbsp;<input type="checkbox" name="is_admin" value="1"> Org Admin</label>
  <label class="cell-2">&nbsp;<input type="checkbox" name="is_organisation" value="1"> Manage Subscribers</label>


  <input type="submit" name="cmd" value="Search" class="bg-green">
  <input type="hidden" id="pgno" name="pgno">
  {% if can_download_csv %}
  <b class="rc bg-grey hand" id="csv" style="height:30px">Download CSV</b>
  {% endif %}
</form>

  {% if can_download_csv %}
<script>
$(function() {
  $('#csv').on('click', function() {
    var data = $("#formS :input")
      .filter(function(i, el) { return $(el).val() != ''; })
      .serialize();
    window.location.href = '/admin/user/?cmd=csv&' + data;
  });
});
</script>
  {% endif %}
