<i class="blue show fr pr-2" data-src="#edit_contact"><i class="fa fa-pen"></i>Edit</i>

{{ include('/Admin/User/profile.form.contact.tpl') }}

# Contact Details

<table class="table">
  <tr>
    <td class="cell-3">Company</td>
    <td class="cell-9">
      <a href="/admin/user?company={{ cust.company | replace({' ':'+', ' P/L':''}) }}" title="Search users with same company name">{{ cust.company | default('NA') }}</a>
    </td>
  </tr>
  <tr><td>Position</td><td>{{ cust.position }}</td></tr>
  <tr><td>Phone</td><td>{{ cust.phone }}</td></tr>
  <tr><td>Mobile</td><td>{{ cust.mobile }}</td></tr>
  <tr><td>Address</td><td>{{ cust.addr }}<br>{{ cust.city }} {{ cust.state }} {{ cust.zip}}<br>{{ cust.country }}</td></tr>

  <tr><td>Total hits</td><td>{{ cust.hit | number_format() }}</td></tr>
  <tr><td>Origin IP</td><td>{{ cust.created_ip }}</td></tr>
  <tr><td>Created</td><td>{{ cust.created | date('j-M-Y H:i') }}</td></tr>
  <tr><td>Last active</td><td>{% if cust.visited and cust.visited != '0000-00-00 00:00:00' %}{{ cust.visited | date('j-M-Y H:i') }}{% endif %}</td></tr>
  <tr><td>Last login</td><td>{% if cust.last_login and cust.last_login != '0000-00-00 00:00:00' %}{{ cust.last_login | date('j-M-Y H:i') }}{% endif %}</td></tr>
  <tr><td>Total active sessions</td><td><a href="/admin/session?uid={{ cust.uid }}">{{ cust.ttl_ses }}</a></td></tr>
  <tr><td>90 day hits</td><td>{{ cust.hit90 | number_format }}</td></tr>
  <tr><td>Total articles read</td><td>{{ cust.ttl_read | number_format }}</td></tr>
</table>
