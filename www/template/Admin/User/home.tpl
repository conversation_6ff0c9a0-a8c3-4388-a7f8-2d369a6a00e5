{{ include('/Admin/User/search.tpl') }}

<h1>Freetrials, subscribers, staff</h1>

{% if msg %}
  <p class="b-gold rc">{{ msg | raw }}</p>
{% endif %}

{% if keyw %}
  <p class="b-green rc">Search Terms: {{ keyw|replace({ 'Is Organisation = 1;' : 'Manage Subscribers = 1;' }) }}</p>
{% endif %}


<table class="table">
  <tr class="bg b">
    <td>ID</td>
    <td>Fname</td>
    <td>Lname</td>
    <td>Purchaser</td>
    <td>Email</td>
    <td>Tags</td>
    <td title="Market Code 1">M.Code</td>
    <td>Status</td>
    <td>Access</td>
  </tr>
{% set acs = ['Guest', '<i class="green">Basic</i>', '<i class="blue">Freetrial</i>', '<b class="gold">Premium</b>', 'NA', 'NA', 'Staff', '<b>Support</b>', '<b class="grey">Editor</b>', '<b class="red">Admin</b>'] %}
{% set status = {
  'active' : '<b class="green">Active</b>',
  'unsub'  : '<i class="blue">unsub</i>',
  'bounced': '<i class="gold">bounced</i>',
  'pending': '<i class="grey">Pending</i>',
  'expired': '<b class="red">Expired</b>',
  'closed' : '<s class="red">Closed</s>'
} %}
{% for u in users %}
  <tr>
    <td><a href="/admin/user/{{ u.uid }}">{{ u.uid }}</a></td>
    <td{% if u.is_admin %} class="b" title="Org Admin"{% endif %}><span{% if u.status != 'active' %} class="grey"{% endif %}>{{ u.fname }}</span></td>
    <td{% if u.is_admin %} class="b" title="Org Admin"{% endif %}><span{% if u.status != 'active' %} class="grey"{% endif %}>{{ u.lname }}</span></td>
    <td>
      <a href="/admin/company/{{ u.purchaser }}"
        {% if u.purchaser > 9 %} class="gold"
        {% elseif u.purchaser == 5 %} class="green"
        {% elseif u.purchaser == 2 %} class="grey"
        {% endif %}
      >{{ cids[u.purchaser] | default('NA') }}</a>
    </td>
    <td class="{% if u.email_repeat == 'Instant' %}dark{% elseif u.email_repeat == 'Daily' %}green{% elseif u.email_repeat == 'Weekly' %}gold{% else %}grey{% endif %}" title="{{ u.email_repeat }}">{{ u.email }}</td>
    <td>{{ u.user_tag | trim(';') | replace({';':' ; '}) }}</td>
    <td>{{ u.market_code1 }}
    {#
    {% if u.trial and u.trial != '0000-00-00' %}
      <span class="{% if date(u.trial) < date('-30 days') %}grey{% elseif date(u.trial) < date('-10 days') %}red{% endif %}">
      {{ u.trial | date_modify('+30 day') | date('jMy') }}
      </span>
    {% endif %}
    #}
    </td>
    <td>{{ status[u.status] | raw }}</td>
    <td>{{ acs[u.acs] | raw }}</td>
  </tr>
{% endfor %}
</table>

{{ include('/Admin/pgno.tpl') }}
