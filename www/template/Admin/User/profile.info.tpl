<a href="/myaccount/{{ cust.uid }}" class="rc bg-gold fr" target="_blank"><i class="fa fa-search"></i>Preview</a>
<i class="rc bg-green show fr mr-1" data-src="#edit_secure"><i class="fa fa-pen"></i>Edit</i>
<a href="/admin/user/{{ cust.uid }}/del" class="rc bg-red fr mr-1 confirm" data-confirm="Are you sure you want to delete this user? This action cannot be undone."><i class="fa fa-times"></i>Delete</a>
<a href="/admin/session?uid={{ cust.uid }}" class="rc bg fr mr-1" target="_blank"><i class="fa fa-user-clock"></i>Sessions</a>

{{ include('/Admin/User/profile.form.info.tpl') }}

<h1>User Profile</h1>

{% if err %}
  <p class="b-red rc">{{ err }}</p>
{% endif %}

{% set acs = ['Guest', '<i class="green">Basic</i>', '<i class="blue">Freetrial</i>', '<b class="gold">Premium</b>', 'NA', 'NA', 'Staff', '<b>Support</b>', '<b class="grey">Editor</b>', '<b class="red">Admin</b>'] %}
{% set status = {
  'active' : '<b class="green">Active</b>',
  'unsub'  : '<i class="blue">Unsub</i>',
  'bounced': '<i class="gold">Bounced</i>',
  'pending': '<i class="grey">Pending</i>',
  'expired': '<b class="red">Expired</b>',
  'closed' : '<s class="red">Closed</s>'
} %}

<table class="table">
  <tr><td class="cell-3">ID</td><td class="cell-5"><b>{{ cust.uid }}</b></td><td class="cell-4"></td></tr>
  <tr><td>Access Level</td><td>{{ acs[cust.acs] | raw }}</td></tr>
  <tr><td>Email Status</td><td>
    {{ status[cust.status] | raw }}
    <br><i class="grey">Only <b>Active</b> users will receive instant / daily / weekly emails</i>;
    <br><i class="grey"><u>Pending, Expired, Closed</u> don't have access to news details
  </td><td>
    {% if cust.status != 'active' %}
      <a href="{{ url_edit }}active" class="b-green rc">» Set as Active</a>
    {% else %}
      <a href="{{ url_edit }}closed" class="b-red rc">» Set as Closed</a>
    {% endif %}
  </td></tr>
  <tr><td>Account Login</td><td>
    {% if cust.closed >= 'now'|date('Y-m-d') %}<i class="red">Frozen</i>{% else %}<b class="green">Normal</b>{% endif %}
    <br><i class="grey">If Login Frozen, user won't be able to login, but able to reset password</i>;
    <br>FYI - this will not affect email auto login
  </td><td>
    {% if cust.closed >= 'now'|date('Y-m-d') %}
      <a href="{{ url_edit }}reset" class="b-green rc">» Reset to Normal</a>
    {% else %}
      <a href="{{ url_edit }}frozen" class="b-red rc">» Frozen Login</a>
    {% endif %}
  </td></tr>
  <tr><td>User tag</td><td>{{ cust.user_tag | trim(';') | replace({';':' ; '}) }}</td></tr>
  <tr>
    <td>Purchaser</td><td>
      <a href="/admin/company/{{ cust.purchaser }}">{{ cust.purchaser }} : {{ cust.purchaser_company }}</a>
      <br>» <a href="/admin/user?company={{ cust.purchaser }}">List Users</a>
      <br>» <a href="/admin/inv/{{ last_inv }}">List Invoices</a>
    </td><td>
    {% if cust.purchaser in [1,5] %}
      <a href="{{ url_edit}}archive" class="b-red rc">» Move to User Archive</a>
    {% endif %}
  </td></tr>
  <tr><td>Name</td><td>{{ cust.fname }} {{ cust.lname }}</td><td></td></tr>
  <tr><td>Email</td><td>{{ cust.email }}</td><td class="blue show" data-src="#edit_acs"><i class="fa fa-pen"></i>Edit</td></tr>
  <tr><td></td><td><a href="/admin/user/welcome/{{ cust.uid }}">Send welcome email</a></td></tr>
  <tr><td>Org Admin</td><td colspan="2">{% if cust.is_admin %}<b>Yes. I m Orgnisation Admin</b>{% else %}No{% endif %}</td></tr>
  <tr><td>Manage Subscribers</td><td colspan="2">{% if cust.is_organisation %}<b>Yes</b>{% else %}No{% endif %}</td></tr>
  <tr><td>Email Repeat</td><td>{{ cust.email_repeat }}</td><td></td></tr>
</table>

{{ include('/Admin/User/profile.form.acs.tpl') }}
