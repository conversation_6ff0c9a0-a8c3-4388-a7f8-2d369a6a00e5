{% if staff_pcid_global or staff_pcid_user %}
<i class="blue show fr pr-2" data-src="#edit_pcid_limit"><i class="fa fa-pen"></i>Edit</i>

<form action="/admin/user/edit/{{ cust.uid }}" method="post">
<input type="hidden" name="cmd" value="save_pcid_limit">
<div class="pop hide" id="edit_pcid_limit">
  <div class="cell-4" style="margin-top:30px">
{% if staff_pcid_user %}
    <div class="bg-blue tab b">Update PCID limit</div>
    <div class="bg box">
      <select name="pcid_limit" class="cell-8">
      {% for k,v in pcid_limit %}
        <option value="{{ k }}"{% if k == cust.pcid_limit %} selected{% endif %}>{{ v }}</option>
      {% endfor %}
      </select>
      <input type="submit" value="Save" class="rc bg-green">
    </div>
{% endif %}
{% if staff_pcid_global %}
    <div class="bg-gold box b">Max PCID limit for site global default</div>
    <div class="bg tabB">
      <select name="pcid_max" class="cell-8">
      {% for k in [5,8,10,12,20,999999] %}
        <option value="{{ k }}"{% if k == PCID_MAX %} selected{% endif %}>{% if k == 999999 %}Unlimited{% else %}{{ k }}{% endif %}</option>
      {% endfor %}
      </select>
      <input type="submit" value="Save" class="rc bg-green">
    </div>
{% endif %}
  </div>
</div>
</form>
{% endif %}

<h2>New PCID usage -- Max limit:
  {% if cust.pcid_limit == 999999 %}Unlimited
  {% elseif cust.pcid_limit %}{{ cust.pcid_limit }}
  {% else %}Default{% endif %}</h2>

<table class="table">
<tr class="bg"><td>PCID</td><td>IP</td><td>First read</td><td>Last read</td><td>Count</td><td>Active</td></tr>
{% for p in cust.pcid %}
<tr{% if p.logout or p.updated < ('now'|date('Y-m-d 00:00:00')) %} class="grey"{% endif %}>
  <td>{{ p.pcid }}</td><td>{{ p.ip }}</td><td>{{ p.created | date('d-M-Y H:i') }}</td><td>{{ p.updated | date('d-M-Y H:i') }}</td><td>{{ p.count }}</td><td>
  {% if not p.logout %}
    <a href="/admin/user/edit/{{ cust.uid }}?cmd=kill&pcid={{ p.pcid }}">Logout</a>
  {% else %}NO
  {% endif %}
</td></tr>
{% endfor %}
</table>
