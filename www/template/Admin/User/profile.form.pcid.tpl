<form action="/admin/user/edit/{{ cust.uid }}" method="post">
<input type="hidden" name="cmd" value="save_pcid">
<div class="pop hide" id="edit_pcid">
  <div class="cell-4" style="margin-top:30px">
    <div class="bg-blue tab b">Update Other Info</div>
    <div class="bg tabB">
    {% for i in 1..3 %}
      {% set ymd = attribute(_context.cust, 'trial' ~ i) %}
      <div class="line-s">
        <p class="cell-6 b line-s">
          <b class="cell-12">Freetrial #{{ i }}</b>
          <select class="cell-4" name="trial{{ i }}d">
            <option value="">Day</option>
            {% for d in 1..31 %}
              <option value="{{ d }}"{% if ymd[8:] == d %} selected{% endif %}>{{ '%02d'|format(d) }}</option>
            {% endfor %}
          </select>
          <select class="cell-4" name="trial{{ i }}m">
            <option value="">Month</option>
            {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
            {% if i %}
              <option value="{{ i }}"{% if ymd[5:2] == i %} selected{% endif %}>{{ m }}</option>
            {% endif %}
            {% endfor %}
          </select>
          <select class="cell-4" name="trial{{ i }}y">
            <option value="">Year</option>
            {% for y in 2000..2030 %}
              <option value="{{ y }}"{% if ymd[:4] == y %} selected{% endif %}>{{ y }}</option>
            {% endfor %}
          </select>
        </p>
        <p class="cell-6 b">
          <b>Market Code #{{ i }}</b><br>
          <input type="text" class="cell-12" name="market_code{{ i }}" value="{{ attribute(_context.cust, 'market_code' ~ i)  }}">
        </p>
      </div>
    {% endfor %}
      <p><input type="submit" value="Save" class="rc bg-green"></p>
    </div>
  </div>
</div>
</form>
