<h1>
  Event Registrations for #{{ form.tid }}
  {% if can_del %}
  <a href="/admin/attendee/{{ form.tid }}?cmd=del-webinar" class="badge bg-red confirm fr" data-confirm="Are you sure you want to continue? This action cannot be undone.">Delete</a>
  {% endif %}
</h1>

<p class="b-green rc"><a href="/news-event/{{ form.tid }}" target="_blank">{{ title }}</a></p>

{% if msg %}
<p class="rc b-gold">{{ msg }}</p>
{% endif %}

<form action="/admin/attendee/{{ form.tid }}" method="post" class="line pb-3">
<input type="text" name="name" placeholder="Name" class="cell-2">
<input type="text" name="email" placeholder="Email" class="cell-2">
<input type="text" name="company" placeholder="Company" class="cell-2">
<input type="text" name="uid" placeholder="UID" class="cell-2">
<input type="text" name="pay_ref" placeholder="Pay Ref" class="cell-2">
<select name="status" class="cell-2">
{% for s in ['', 'Premium', 'Paid', 'Fail', 'Premium + Paid'] %}
  <option value="{{ s }}">{{ s | default('Status') }}</option>
{% endfor %}
</select>
<input type="submit" name="cmd" value="Search" class="bg-green cell-2">
<input type="submit" name="cmd" value="Download CSV" class="bg-gold cell-2">
</form>

<table class="table">
  <tr class="bg b">
    <td>RegID</td><td>First name</td><td>Last name</td><td>Email</td><td>Company</td><td>Pay Ref</td><td>Date</td><td>UID</td>
  </tr>
{% for r in data %}
  <tr{% if r.status == 'Paid' %} class="b-green"{% elseif r.status == 'Fail' %} class="b-red"{% endif %}>
    <td><a href="/admin/attendee/{{ r.tid }}-{{ r.seq }}">{{ r.tid }}-{{ r.seq }}</a></td>
    <td><a href="/admin/attendee?name={{ r.fname }}">{{ r.fname}}</a></td>
    <td><a href="/admin/attendee?name={{ r.lname }}">{{ r.lname }}</a></td>
    <td><a href="/admin/attendee?email={{r.email }}">{{ r.email }}</a></td>
    <td><a href="/admin/attendee?company={{ r.company }}">{{ r.company }}</a></td>
    <td>{% if r.status == 'Paid' %}<a href="/admin/attendee?pay_ref={{ r.pay_ref }}">{{ r.pay_ref }}</a>{% else %}{{ r.status }}{% endif %}</td>
    <td>{{ r.pay_date | date('d/m/y H:i') }}</td>
    <td><a href="/admin/attendee?uid={{ r.uid }}">{{ r.uid }}</a></td>
  </tr>
{% endfor %}
</table>

{{ include('/Admin/pgno.tpl') }}
