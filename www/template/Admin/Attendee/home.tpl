<h1>{{ site.name }} Events</h1>

<form action="/admin/attendee" method="post" class="line pb-3">
<input type="text" name="tid" placeholder="Event ID" class="cell-2">
<input type="text" name="name" placeholder="Name" class="cell-2">
<input type="text" name="email" placeholder="Email" class="cell-2">
<input type="text" name="company" placeholder="Company" class="cell-2">
<input type="text" name="uid" placeholder="UID" class="cell-2">
<input type="text" name="pay_ref" placeholder="Pay Ref" class="cell-2">
<select name="year" class="cell-2">
  <option value="">Year</option>
{% for y in -1..6 %}
  {% set y = 'now'|date('Y') - y %}
  {% if y > 2020 %}
  <option value="{{ y }}">{{ y }}</option>
  {% endif %}
{% endfor %}
</select>
<input type="submit" name="cmd" value="Search" class="bg-green cell-2">
</form>

<table class="table">
  <tr class="bg b">
    <td>EventID</td><td>Date</td><td>Title</td><td>Registered</td><td>Paid</td><td>Premium</td>
  </tr>
{% for r in data %}
  <tr{% if r.exp %} class="grey"{% endif %}>
    <td><a href="/admin/attendee/{{ r.tid }}"{% if r.exp %} class="grey"{% endif %}>{{ r.tid }}</a></td>
    <td>{{ r.start | date('d-M-y') }}</td><td>{{ r.title }}</td><td>{{ r.reg }}</td><td>{{ r.paid }}</td><td>{{ r.Cost }}</td>
  </tr>
{% endfor %}
</table>

{{ include('/Admin/pgno.tpl') }}
