<h1>Event Registration #<a href="/admin/attendee/{{ form.tid }}">{{ form.tid }}</a>-{{ form.seq }}</h1>

<p class="b-green rc"><a href="/news-event/{{ form.tid }}" target="_blank">{{ title}}</a></p>

{% if msg %}
<p class="b-gold rc">{{ msg }}</p>
{% endif %}

<form class="line-s bg js-kid" data-kid="p-1 cell-3" style="max-width:600px" method="post" action="/admin/attendee/{{ form.tid }}-{{ form.seq}}">

<b>Registration ID:</b>
<b class="cell-9">{{ form.tid}}-{{ form.seq }}</b>
<b>Status:</b>
<b class="cell-9{% if form.status == 'Fail' %} red{% endif %}">{{ form.status }}</b>

<b>First name:</b>
<input type="text" name="fname" value="{{ form.fname }}" class="cell-9">
<b>Last name:</b>
<input type="text" name="lname" value="{{ form.lname }}" class="cell-9">
<b>Email address:</b>
<input type="text" name="email" value="{{ form.email }}" class="cell-9">
<b>Contact phone:</b>
<input type="text" name="phone" value="{{ form.phone }}" class="cell-9">
<b>Position:</b>
<input type="text" name="position" value="{{ form.position }}" class="cell-9">
<b>Company:</b>
<input type="text" name="company" value="{{ form.company }}" class="cell-9">
<b>Postal address:</b>
<input type="text" name="addr" value="{{ form.addr }}" class="cell-9">

<b>Suburb:</b>
<input type="text" name="city" value="{{ form.city }}" class="cell-6"><b></b>
<b>State:</b>
<input type="text" name="state" value="{{ form.state }}" class="cell-6"><b></b>
<b>Postcode:</b>
<input type="text" name="zip" value="{{ form.zip }}" class="cell-6"><b></b>
<b>Country:</b>
<input type="text" name="country" value="{{ form.country }}" class="cell-6"><b></b>

<b>Consent:</b>
<b class="cell-9">{{ form.consent }}</b>
<b>Pay IP:</b>
<b class="cell-9">{{ form.pay_ip }}</b>
<b>Pay date:</b>
<b class="cell-9 blue">{{ form.pay_date | date('d/m/Y H:i') }}</b>
<b>Type of card:</b>
<b class="cell-9">{{ form.pay_method }}</b>

<b>Name on the card:</b>
<b class="cell-9">{{ form.pay_name }}</b>
<b>Card number:</b>
<b class="cell-9">{{ form.pay_card[:4]}} #### #### {{ form.pay_card[4:] }}</b>
<b>Expiry date:</b>
<b class="cell-9">{{ form.pay_exp_m }} / {{ form.pay_exp_y }}</b>
<b>Amount:</b>
<b class="cell-9">{% if form.status == 'Paid' %}{{ form.pay_amt / 100 }}{% endif %}</b>
<b>Pay ref:</b>
<b class="cell-9 blue">{{ form.pay_ref }}</b>
<b>Bank notes:</b>
<b class="cell-9 green">{{ form.pay_response }}</b>

<b>Question 1:</b>
<b class="cell-9">{{ form.q1 }}</b>
<b>Question 2:</b>
<b class="cell-9">{{ form.q2 }}</b>
<b>Question 3:</b>
<b class="cell-9">{{ form.q3 }}</b>
<b>Notes:</b>
<textarea name="notes" class="cell-9" style="height:300px">{{ form.notes }}</textarea>

<b></b>
<input type="submit" name="cmd" value="Save" class="bg-green">
{% if can_del %}
<input type="submit" name="cmd" value="Delete" class="bg-red confirm" data-confirm="Are you sure you want to continue? This action cannnot be undone.">
{% endif %}
</form>

