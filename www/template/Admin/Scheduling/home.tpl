{# Email Scheduling Manager #}

<!-- Page Title -->
<h1 class="mb-4">
  <i class="bi bi-clock text-info"></i> Email Scheduling Manager
</h1>

<!-- Sub-Navigation -->
<ul class="nav nav-tabs mb-4">
  <li class="nav-item">
    <a class="nav-link active" href="/admin/scheduling">
      <i class="bi bi-calendar-check"></i> Scheduled
    </a>
  </li>
  <li class="nav-item">
    <a class="nav-link" href="/admin/preview?tpl=/Admin/Campaign/sent">
      <i class="bi bi-send"></i> Sent
    </a>
  </li>
</ul>

<!-- Quick Actions -->
<div class="row mb-4">
  <div class="col-md-6">
    <div class="card">
      <div class="card-body">
        <h5 class="card-title">
          <i class="bi bi-lightning text-warning"></i> Schedule Instant Email
        </h5>
        <p class="card-text">Schedule an instant email for a specific article to be sent at a future time.</p>
        <a href="/admin/news/post" class="btn btn-warning">
          <i class="bi bi-plus-circle"></i> Create Scheduled Article
        </a>
      </div>
    </div>
  </div>
  <div class="col-md-6">
    <div class="card">
      <div class="card-body">
        <h5 class="card-title">
          <i class="bi bi-arrow-repeat text-primary"></i> Manage Templates
        </h5>
        <p class="card-text">Configure daily, weekly, and recurring email templates and schedules.</p>
        <a href="/admin/email-temp" class="btn btn-primary">
          <i class="bi bi-gear"></i> Email Templates
        </a>
      </div>
    </div>
  </div>
</div>

<!-- Filters Form (collapsed by default) -->
<div class="collapse" id="filterForm">
  <form class="card card-body mb-5 bg-light">
    <div class="form-row">
      <div class="form-group col-md-6">
        <label for="typeFilter" class="font-weight-bold">Type</label>
        <select id="typeFilter" class="form-control">
          <option value="">All</option>
          <option>Instant</option>
          <option>Daily</option>
          <option>Weekly</option>
          <option>Invoice</option>
          <option>Workflow</option>
          <option>Report</option>
        </select>
      </div>
      <div class="form-group col-md-6">
        <label for="statusFilter" class="font-weight-bold">Status</label>
        <select id="statusFilter" class="form-control">
          <option value="">All</option>
          <option>Sending</option>
          <option>Scheduled</option>
          <option>Paused</option>
          <option>Draft</option>
          <option>Unpublished</option>
        </select>
      </div>
    </div>
    <div class="d-flex justify-content-end">
      <button type="button" id="applyFilter" class="btn btn-primary">Apply Filters</button>
      <button type="button" id="resetFilter" class="btn btn-secondary ml-2">Reset</button>
    </div>
  </form>
</div>

<!-- Filter Toggle Button -->
<div class="d-flex justify-content-between align-items-center mb-3">
  <div>
    <button class="btn btn-outline-secondary" type="button" data-toggle="collapse" data-target="#filterForm">
      <i class="bi bi-funnel"></i> Filters
    </button>
  </div>
  <div>
    <button class="btn btn-outline-primary" onclick="location.reload()">
      <i class="bi bi-arrow-clockwise"></i> Refresh
    </button>
  </div>
</div>

<!-- Main Table -->
<table id="schedulingTable" class="table table-bordered table-striped my-2">
  <thead>
    <tr>
      <th class="text-center"></th>
      <th class="d-none">Type</th>
      <th>Campaign</th>
      <th>Subject</th>
      <th class="text-nowrap">Send Time</th>
      <th>Status</th>
      <th class="text-nowrap">Actions</th>
    </tr>
  </thead>
</table>

<!-- Loading State -->
<div id="loadingState" class="text-center py-5" style="display: none;">
  <div class="spinner-border text-primary" role="status">
    <span class="sr-only">Loading...</span>
  </div>
  <p class="mt-2">Loading scheduled emails...</p>
</div>

<!-- Empty State -->
<div id="emptyState" class="text-center py-5" style="display: none;">
  <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
  <h4 class="text-muted mt-3">No Scheduled Emails</h4>
  <p class="text-muted">Get started by scheduling your first email campaign.</p>
  <a href="/admin/news/post" class="btn btn-primary">
    <i class="bi bi-plus-circle"></i> Schedule Email
  </a>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" role="dialog" aria-labelledby="previewModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document" style="max-width:90%;">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title mb-0" id="previewModalLabel">Email Preview</h5>
        <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
      </div>
      <div class="modal-body p-0">
        <iframe
          id="previewFrame"
          style="width:100%; height:600px; border:none;"
          sandbox="allow-same-origin allow-scripts allow-popups"
          src="">
        </iframe>
      </div>
      <div class="modal-footer">
        <div class="input-group w-50">
          <input id="previewEmailAddress" type="email" class="form-control" placeholder="Enter email address">
          <div class="input-group-append">
            <button class="btn btn-primary send-preview-btn" data-toggle="tooltip" title="Send Preview">
              <i class="bi bi-send"></i>
            </button>
          </div>
        </div>
        <button class="btn btn-secondary ml-3" data-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<script>
$(function(){
  // Initialize DataTable with placeholder data
  var table = $('#schedulingTable').DataTable({
    data: [], // Start with empty data
    columns: [
      // Icon + tooltip = Type
      { data:'icon', orderable:false, className:'text-center',
        render: function(icon,_,row){
          return '<i class="bi bi-'+icon+'" data-toggle="tooltip" title="'+ row.type +'"></i>';
        }
      },
      // Type (hidden but used for filtering)
      { data:'type', visible:false },
      // Campaign (now visible)
      { data:'campaign', orderable:false },
      // Subject + stats + preview
      { data:null, orderable:false, render:function(r){
          return '<a href="#" class="preview-link">'+r.subject+'</a>'
               +'<br><small class="text-muted">'
               +'<strong>Recipients:</strong> '+r.user_group+' ('+r.num_users.toLocaleString()+')'
               +'</small>';
        }
      },
      // Send Time
      { data:'send_time', className:'text-nowrap', orderable:true },
      // Status badge
      { data:'status', orderable:true, render:function(s){
          var m={'Sending':'danger','Scheduled':'success','Paused':'warning',
                 'Draft':'secondary','Unpublished':'secondary'};
          return '<span class="badge badge-'+(m[s]||'primary')+'">'+s+'</span>';
        }
      },
      // Actions
      { data:'actions', orderable:false, className:'text-nowrap',
        render:function(actions,_,row){
          return '<button class="btn btn-sm btn-primary" title="Edit"><i class="bi bi-pencil"></i></button> ' +
                 '<button class="btn btn-sm btn-warning" title="Pause"><i class="bi bi-pause"></i></button>';
        }
      }
    ],
    order:[[4,'asc']],
    paging:true, info:true, searching:true,
    language: {
      emptyTable: "No scheduled emails found"
    },
    drawCallback:()=>$('[data-toggle="tooltip"]').tooltip()
  });

  // Show empty state initially
  $('#emptyState').show();
  
  // TODO: Replace with real data loading
  console.log('Scheduling Manager initialized - ready for Phase 2 data integration');
});
</script>
