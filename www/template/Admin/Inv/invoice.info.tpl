<table class="fr">
  <tr><td class="pr-3">Invoice</td><td>{{ inv.inv_id }}</td></tr>
  <tr><td>Date</td><td>{{ inv.inv_date | date('j-M-Y') }}</td></tr>
  <tr><td>Amount</td><td class="b">${{ (inv.amt / 100) | number_format(2) }}</td></tr>
  <tr class="b-{% if inv.status == 'Paid' %}green{% elseif inv.status == 'Unpaid' %}gold{% elseif inv.status == 'Cancelled' %}blue{% elseif inv.status == 'Refunded' %}red{% endif %}">
    <td>Status</td>
    <td>
      {{ inv.status }}
      <i class="show fr blue" data-src="#edit_status">&nbsp; <i class="fa fa-pen"></i></i>
    </td>
  </tr>
  <tr><td>Due Date</td><td{% if inv.date_due < inv.inv_date | date('Y-m-d') %} class="red"{% endif %}>
    {{ inv.date_due | date('j-M-Y') }}
  </td></tr>
  <tr><td>Start</td><td{% if inv.date_fm < inv.inv_date | date('Y-m-d') %} class="red"{% endif %}>
    {{ inv.date_fm | date('j-M-Y') }}
  </td></tr>
  <tr>
    <td>Expire</td>
    <td{% if inv.date_to < inv.date_fm %} class="red"{% endif %}>
      {{ inv.date_to | date('j-M-Y') }}
      <i class="show fr blue" data-src="#edit_dates">&nbsp; <i class="fa fa-pen"></i></i>
    </td>
  </tr>
</table>

<div class="pop hide" id="edit_status">
  <div class="cell-5" style="margin-top:30px">
    <div class="bg-dark tab b">Update Status</div>
    <div class="bg tabB" style="line-height:3em">
    {% for s in ['Draft','Unpaid','Paid','Cancelled', 'Refunded', 'Paused'] %}
      <a href="/admin/inv/edit/{{ inv.inv_id }}?status={{ s }}" class="rc bg-{% if s == inv.status %}red{% else %}grey{% endif %}">{{ s }}</a>
    {% endfor %}
    </div>
  </div>
</div>

<form action="/admin/inv/edit/{{ inv.inv_id }}" method="post">
<input type="hidden" name="cmd" value="save_dates">
<div class="pop hide" id="edit_dates">
  <div class="cell-4" style="margin-top:30px">
    <div class="bg-dark tab b">Update Invoice</div>
    <div class="bg tabB">
      <div class="line-s">
        <b class="cell-3">Date</b>
        <select class="cell-3" name="inv_date_d">
          <option value="">Day</option>
          {% for d in 1..31 %}
            <option value="{{ d }}"{% if inv.inv_date[8:2] == d %} selected{% endif %}>{{ '%02d'|format(d) }}</option>
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_date_m">
          <option value="">Month</option>
          {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
          {% if i %}
            <option value="{{ i }}"{% if inv.inv_date[5:2] == i %} selected{% endif %}>{{ m }}</option>
          {% endif %}
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_date_y">
          <option value="">Year</option>
          {% for y in 2000..2030 %}
            <option value="{{ y }}"{% if inv.inv_date[0:4] == y %} selected{% endif %}>{{ y }}</option>
          {% endfor %}
        </select>
      </div>
      <div class="line-s">
        <b class="cell-3">Due Date</b>
        <select class="cell-3" name="date_due_d">
          <option value="">Day</option>
          {% for d in 1..31 %}
            <option value="{{ d }}"{% if inv.date_due[8:2] == d %} selected{% endif %}>{{ '%02d'|format(d) }}</option>
          {% endfor %}
        </select>
        <select class="cell-3" name="date_due_m">
          <option value="">Month</option>
          {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
          {% if i %}
            <option value="{{ i }}"{% if inv.date_due[5:2] == i %} selected{% endif %}>{{ m }}</option>
          {% endif %}
          {% endfor %}
        </select>
        <select class="cell-3" name="date_due_y">
          <option value="">Year</option>
          {% for y in 2000..2030 %}
            <option value="{{ y }}"{% if inv.date_due[0:4] == y %} selected{% endif %}>{{ y }}</option>
          {% endfor %}
        </select>
      </div>
      <div class="line-s">
        <b class="cell-3">Start</b>
        <select class="cell-3" name="date_fm_d">
          <option value="">Day</option>
          {% for d in 1..31 %}
            <option value="{{ d }}"{% if inv.date_fm[8:2] == d %} selected{% endif %}>{{ '%02d'|format(d) }}</option>
          {% endfor %}
        </select>
        <select class="cell-3" name="date_fm_m">
          <option value="">Month</option>
          {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
          {% if i %}
            <option value="{{ i }}"{% if inv.date_fm[5:2] == i %} selected{% endif %}>{{ m }}</option>
          {% endif %}
          {% endfor %}
        </select>
        <select class="cell-3" name="date_fm_y">
          <option value="">Year</option>
          {% for y in 2000..2030 %}
            <option value="{{ y }}"{% if inv.date_fm[0:4] == y %} selected{% endif %}>{{ y }}</option>
          {% endfor %}
        </select>
      </div>
      <div class="line-s">
        <b class="cell-3">Expire</b>
        <select class="cell-3" name="date_to_d">
          <option value="">Day</option>
          {% for d in 1..31 %}
            <option value="{{ d }}"{% if inv.date_to[8:2] == d %} selected{% endif %}>{{ '%02d'|format(d) }}</option>
          {% endfor %}
        </select>
        <select class="cell-3" name="date_to_m">
          <option value="">Month</option>
          {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
          {% if i %}
            <option value="{{ i }}"{% if inv.date_to[5:2] == i %} selected{% endif %}>{{ m }}</option>
          {% endif %}
          {% endfor %}
        </select>
        <select class="cell-3" name="date_to_y">
          <option value="">Year</option>
          {% for y in 2000..2030 %}
            <option value="{{ y }}"{% if inv.date_to[0:4] == y %} selected{% endif %}>{{ y }}</option>
          {% endfor %}
        </select>
      </div>
      <div class="line-s">
        <b class="cell-3">Remark</b>
        <textarea class="cell-9" name="remark">{{ inv.remark }}</textarea>
      </div>
      <!--div class="line-s">
        <b class="cell-3">Item</b>
        <input type="text" class="cell-4" name="item" value="{{ inv.item }}">
      </div-->
      <div class="line-s">
        <b class="cell-3"><!--Users (Qty)--></b>
        <!--input type="text" class="cell-4" value="{{ inv.qty }}" name="qty"-->
        <input type="submit" class="bg-green cell-3" value="Save">
      </div>
    </div>
  </div>
</div>
</form>
