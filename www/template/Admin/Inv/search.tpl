<form class="formH label-0 line-m" data-kid="cell-2" action="/admin/inv" method="post" id="formS">
  <input type="text" name="keyw" value="{{ form.id }}" placeholder="Name / Email / Addr / Remark">
  <input type="text" name="id" value="{{ form.id }}" placeholder="Invoice ID">
  <input type="text" name="ids" value="{{ form.ids }}" placeholder="Partial Invoice ID">
  <input type="text" name="email" value="{{ form.email }}" placeholder="Email">
  <input type="text" name="company" value="{{ form.company }}" placeholder="Company name or id">
  <input type="text" name="name" value="{{ form.name }}" placeholder="Name">
  <input type="text" name="phone" value="{{ form.phone }}" placeholder="Phone or Mobile">
  <select name="item">
    {{ item_options | raw }}
  </select>
  <select name="pay_method">
    {{ pay_options | raw }}
  </select>
  <select name="status">
    {{ status_options | raw }}
  </select>
  <input type="text" name="pay_ref" value="{{ form.pay_ref }}" placeholder="Pay Ref">
  <select name="company_status">
    <option value="">Comp Status</option>
    <option value="active">Active</option>
    <option value="cancelled">Cancelled</option>
  </select>
  <label title="Invoice Qty not match with Order"><input type="checkbox" name="err_qty" value="1"{% if form.err_qty %} checked{% endif %}> Qty Err</label>
  <label title="Invoice Qty less than active Users"><input type="checkbox" name="short_qty" value="1"{% if form.short_qty %} checked{% endif %}> Qty Short</label>
  <input type="hidden" id="pgno" name="pgno">
  <input type="text" name="amt" value="{{ form.amt }}" placeholder="AMT from .. to eg 123..345">
  {% if can_download_csv %}
  <input type="button" value="CSV Invoice" class="bg-grey csv" data-csv="csv">
  <input type="button" value="CSV Payment" class="bg-grey csv" data-csv="csv_pay">
  {% endif %}
  <input type="submit" name="cmd" value="Search" class="bg-green">
  {{ include('/Admin/Inv/search.dates.tpl') }}
</form>

  {% if can_download_csv %}
<script>
$(function() {
  $('.csv').on('click', function() {
    var data = $("#formS :input")
      .filter(function(i, el) { return $(el).val() != ''; })
      .serialize();
    window.location.href = '/admin/inv/?cmd=' + $(this).data('csv') + '&' + data;
  });
});
</script>
  {% endif %}
