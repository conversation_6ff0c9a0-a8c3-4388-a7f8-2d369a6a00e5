<table class="table mt-3 mr-3">
  <tr class="bg b">
    <td>Item descriptions &nbsp; <i class="show fa fa-pen blue" data-src="#edit_item"></i></td>
    <td class="ar">Unit&nbsp;Price</td>
    <td class="ar">Qty</td>
    <td class="ar">Amount</td>
  </tr>
{% set qty = 0 %}
{% set amt = 0 %}
{% for item in ord %}
  <tr>
    <td>{{ item.name_his }}</td>
    <td class="ar">${{ item.uprice | number_format(2) }}</td>
    <td class="ar">
      {% if item.vid > 100 %}
        {{ item.qty }}
        {% set qty = qty + item.qty %}
      {% endif %}
    </td>
    <td class="ar{% if item.uprice * item.qty != item.amt %} red{% endif %}">${{ item.amt | number_format(2) }}</td>
  </tr>
  {% set amt = amt + item.amt %}
{% endfor %}

{% if qty > 0 %}
  <tr class="bg b">
    <td>Total</td>
    <td></td>
    <td class="ar">{{ qty }}</td>
    <td class="ar">${{ amt | number_format(2) }}</td>
  </tr>
{% endif %}

<tr><td colspan="4" class="ar i grey">
{% if inv.gst > 0 %}
  (GST ${{ (inv.gst/100) | number_format(2) }})
{% else %}
  NO GST
{% endif %}
</td></tr>

</table>

{% if qty != inv.qty %}
  <p class="rc b-red">Total <b>{{ qty }}</b> users not matching with Invoice Qty <b>{{ inv.qty }}</b></p>
{% endif %}

{% if amt * 100 != inv.amt %}
  <p class="b-red rc">Total <b>${{ amt | number_format(2) }}</b> not matching with Invoice Amount <b>${{ (inv.amt / 100) | number_format(2) }}</b></p>
{% endif %}

<form action="/admin/inv/edit/{{ inv.inv_id }}" method="post">
<input type="hidden" name="cmd" value="save_item">
<div class="pop hide" id="edit_item">
  <div style="margin-top:30px;max-width:500px;width:90%">
    <div class="bg-dark tab b">Update item descriptions</div>
    <div class="bg tabB">
      <p class="line-s rc bg-grey">
        <b class="cell-9">Item description</b>
        <b class="cell-2 pl-3">Qty</b>
        <b class="cell-1">AMT</b>
      </p>
{% set ord = ord | merge([0, 0]) %}
{% for i,item in ord %}
      <div class="line-s">
        <input type="hidden" name="item[{{ i }}][0]" value="{{ item.vid }}" class="cell-1 vid">
        <input type="text" name="item[{{ i }}][3]" value="{{ item.name_his }}" class="cell-8 ver_name">
        <b class="cell-1 pt-1 px-2 sel-upd"><i class="fa fa-angle-down fa-2x"></i></b>
        <input type="text" class="cell-1 ar qty" value="{{ item.qty }}" name="item[{{ i }}][1]">
        <input type="text" class="cell-2 ar dollar" value="{{ item.amt | number_format(2) }}" name="item[{{ i }}][2]">
      </div>
      <div class="line-s hide">
        <ul class="cell-9 rc bg-grey upd">
          <li>Please select</li>
        {% for p in price %}
          <li data-vid="{{ p.vid }}" data-name="{{ p.ver_name }}" data-price="{{ p.price }}">{{ p.ver_name }} @ ${{ (p.price / 100) | number_format(2) }}</li>
        {% endfor %}
        </ul>
      </div>
{% endfor %}
      <div class="line-s">
        <div class="cell-10"><label>
          <input type="checkbox" name="gst" value="1"{% if inv.gst %} checked{% endif %}> Include GST
        </label></div>
        <input type="submit" class="bg-green" value="Save">
      </div>
    </div>
  </div>
</div>
</form>

<script>
$(function() {
  $('.sel-upd').on('click', function() {
    $(this).parent().next().toggleClass('hide');
  });
  $('ul.upd li').on('click', function() {
    if ($(this).data('vid')) {
      var p = $(this).parent().parent();
      p.addClass('hide');
      var pp = p.prev();
      pp.find('.ver_name').val($(this).data('name'));
      var qty = pp.find('.qty');
      if (qty.val() < 1) {
        qty.val(1);
      }
      var amt = qty.val() * $(this).data('price') / 100;
      amt = amt.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      pp.find('.dollar').val(amt);
      pp.find('.vid').val($(this).data('vid'));
    }
  });
})
</script>
