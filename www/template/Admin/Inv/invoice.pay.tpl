<div class="fr">
  <a href="/pay/{{ inv.inv_id }}/{{ inv.pay_code }}" target="_blank" class="rc bg-gold"><i class="fa fa-credit-card"></i>Pay</a>
  <i class="show fr ml-1" data-src="#add_pay" id="pay_eft"><i class="rc bg-dark"><i class="fa fa-plus"></i>EFT</i></i>
  <i class="show fr ml-1" data-src="#add_pay" id="pay_chq"><i class="rc bg-grey"><i class="fa fa-plus"></i>CHQ</i></i>
</div>

<h2>Payment</h2>
<table class="table">
{% set paid = 0 %}
{% for pay in payment %}
  <tr class="showN hand
    {% if pay.status != 'Paid' %}
      grey
    {% else %}
      {% set paid = paid + pay.pay_amt %}
    {% endif %}
  " title="Ref: {{ pay.pay_ref }}">
    <td>{{ pay.pay_date | date('j-M-y H:i') }}</td>
    <td>
      {% if pay.pay_method == 1 %}
        {% if pay.pay_name or pay.pay_response %}<i class="grey">CHQ</i>{% else %}EFT{% endif %}
      {% elseif pay.pay_method == 11 %}
        EFT
      {% elseif pay.pay_method == 12 %}
        <i class="grey">CHQ</i>
      {% elseif pay.pay_method == 23 %}
        <i class="blue">Amex</i>
      {% elseif pay.pay_method == 24 %}
        <i class="gold">Visa</i>
      {% elseif pay.pay_method == 25 %}
        <i class="green">MC</i>
      {% else %}
        NA
      {% endif %}
    </td>
    <td{% if pay.pay_amt < 1%} class="red"{% endif %}>${{ (pay.pay_amt/100) | number_format(2) }}</td>
    <td>{{ pay.status }}</td>
  </tr>
  <tr class="hide"><td colspan="4" style="padding-left:40px">
    <b class="badge bg cell-2">Pay ID</b>: <i class="grey">{{ pay.id }}</i><br>
    <b class="badge bg cell-2">IP</b>: <i class="grey">{{ pay.ip }}</i><br>
    <b class="badge bg cell-2">Card</b>:
      {% if pay.pay_method in [23,24,25] %}
        {% set card = pay.pay_card %}
        {{ pay.pay_card | slice(0,5) }} ... {{ card | slice(-3) }}
      {% else %}
        {{ pay.pay_card }}
      {% endif %}<br>
    <b class="badge bg cell-2">Ref</b>: <i class="green">{{ pay.pay_ref }}</i><br>
    <b class="badge bg cell-2">Name</b>: {{ pay.pay_name }}<br>
    <b class="badge bg cell-2">Sett</b>: <i class="grey">{{ pay.pay_date_sett }}</i><br>
    <b class="badge bg cell-2">Bank</b>: <i class="small {% if pay.pay_response == 'Honour with identification' %}green{% else %}grey{% endif %}">{{ pay.pay_response }}</i><br>
    <b class="badge bg cell-2">Audit</b>: {{ pay.audit_name }} / <i class="grey">{% if pay.audit_date != '0000-00-00 00:00:00' %}{{ pay.audit_date }}{% endif %}</i>
  </td></tr>
{% endfor %}
</table>

{% if paid and paid != inv.amt %}
  <p class="b-red rc">Total Paid: <b>${{ (paid / 100) | number_format(2) }}</b> not matches with invoice amount. Balance = <b>{{ ((inv.amt - paid) / 100) | number_format(2) }}</b></p>
{% endif %}

<script>
$(function() {
  $('tr.showN').on('click', function() {
    $(this).next().toggleClass('hide');
  });
})
</script>

<form action="/admin/inv/edit/{{ inv.inv_id }}" method="post" class="{{ pay.method }}">
<input type="hidden" name="cmd" value="add_pay">
<div class="pop{% if not err_pay %} hide{% endif %}" id="add_pay">
  <div class="cell-4" style="margin-top:30px">
    <div class="bg-dark tab b">Add Payment : <span class="pay_eft">EFT</span><span class="pay_chq">Cheque</span></div>
    <div class="bg tabB">
      {{ err_pay | raw }}
      <div class="line-s pay_chq">
        <b class="cell-4">Bank Name</b>
        <input type="text" class="cell-8" name="txt" value="{{ pay.txt }}">
      </div>
      <div class="line-s pay_chq">
        <b class="cell-4">Drawer</b>
        <input type="text" class="cell-8" name="name" value="{{ pay.name }}">
      </div>
      <hr class="pay_chq">
      <div class="line-s">
        <b class="cell-4">Ref. No.</b>
        <input type="text" class="cell-8" name="ref" value="{{ pay.ref }}" placeholder="Bank statement ref or Inv ID">
      </div>
      <div class="line-s">
        <b class="cell-4">Pay Date</b>
        <div class="cell-8 line-s">
          <select class="cell-4" name="date_d">
            {% for d in 01..31 %}
              <option value="{{ d }}"{% if d == pay.date_d or (not pay.date_d and d == 'now'|date('d')) %} selected{% endif %}>{{ d }}</option>
            {% endfor %}
          </select>
          <select class="cell-4" name="date_m">
            {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
            {% if i %}
              <option value="{{ i }}"{% if i == pay.date_m or (not pay.date_m and i == 'now'|date('m')) %} selected{% endif %}>{{ m }}</option>
            {% endif %}
            {% endfor %}
          </select>
          <select class="cell-4" name="date_y">
            {% for y in 2010..2030 %}
              <option value="{{ y }}"{% if y == pay.date_y or (not pay.date_y and y == 'now'|date('Y')) %} selected{% endif %}>{{ y }}</option>
            {% endfor %}
          </select>
        </div>
      </div>
      <div class="line-s">
        <b class="cell-4">Amount</b>
        <input type="text" class="cell-4" name="amt" value="{{ pay.amt }}" required>
        <input type="hidden" name="method" id="pay_method" value="{{ pay.method }}">
        <input type="submit" class="bg-green cell-4" value="Save">
      </div>
    </div>
  </div>
</div>
</form>

<script>
$(function() {
  $('#pay_eft').on('click', function() {
    $('.pay_chq').hide();
    $('.pay_eft').show();
    $('#pay_method').val('eft');
  });
  $('#pay_chq').on('click', function() {
    $('.pay_chq').show();
    $('.pay_eft').hide();
    $('#pay_method').val('chq');
  });
});
</script>
<style>
form.eft .pay_chq{display:none}
form.chq .pay_eft{display:none}
</style>
