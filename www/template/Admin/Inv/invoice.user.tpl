<div class="fr">
  <i class="show fr" data-src="#add_user_list"><i class="rc bg-green"><i class="fa fa-plus"></i>Add Recipients</i></i>
  <i class="show fr pr-1" data-src="#sub_users tr.grey"><i class="rc bg">All ({{ user_num }}/{{ users|length }})</i></i>
</div>

<form action="/admin/inv/add-user-list/{{ inv.inv_id }}/{{ inv.purchaser }}" method="post">
<div class="pop hide" id="add_user_list">
  <div style="margin-top:30px;max-width:500px;width:90%">
    <div class="bg-dark tab b">Add recipients</div>
    <div class="bg tabB">
      <p>Add more recipients to this subscription. Any existing emails OR invalid emails will be ignored.</p>
{% for i in 0..9 %}
      <div class="line-s">
        <input class="cell-3 px-1" name="users[{{ i }}][0]" placeholder="First Name">
        <input class="cell-3 px-1" name="users[{{ i }}][1]" placeholder="Last Name">
        <input class="cell-6 px-1" name="users[{{ i }}][2]" placeholder="Email">
      </div>
{% endfor %}
      <input type="submit" class="cell-3 bg-green" value="Add">
    </div>
  </div>
</div>
</form>

<h2>Subscribers</h2>

{% if user_num > inv.qty %}
  <p class="b-red rc"><b>{{ user_num }}</b> active subscribers &gt; Inv Qty <b>{{ inv.qty }}</b></i>
{% endif %}

{% set acs = ['Guest', '<i class="grey">Basic</i>', 'Freetrial', '<b class="gold">Premium</b>', 'NA', 'NA', 'Staff', '<b>Support</b>', '<b class="green">Editor</b>', '<b class="red">Admin</b>'] %}

<table class="table" id="sub_users">
{% for u in users %}
  <tr class="{% if u.status in ['closed', 'pending', 'expired'] %}grey hide bg{% endif%}" title="{{ u.status }}">
    <td>{{ acs[u.acs] | raw }}</td>
    <td><a href="/admin/user/{{ u.uid }}">{% if u.is_admin %}<i class="fa fa-user" title="Company Admin User"></i>{% endif %}{{ u.fname }} {{ u.lname }}</a></td>
    <td><i class="fa fa-{% if u.status in ['closed', 'pending', 'expired'] %}times{% else %}check{% endif %}"></i>{{ u.email }}</td>
    <td><a href="/admin/user/welcome/{{ u.uid }}" target="_blank" title="Send Welcome Email"><i class="fa fa-envelope"></i></a></td>
  </tr>
{% endfor %}
</table>
