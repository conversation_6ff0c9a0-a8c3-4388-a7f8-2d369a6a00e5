{{ include('/Admin/Inv/search.tpl') }}

{% if msg_del %}
  <p class="b-gold rc">{{ msg_del | raw }}</p>
{% endif %}

<div class="fr">
  <a href="/admin/inv/{{ inv.inv_id }}/del" class="rc bg-red confirm" data-confirm="Are you sure to delete this invoice?"><i class="fa fa-times"></i> Delete</a>
  <a href="/admin/inv/profile/{{ inv.purchaser }}" class="rc b-blue"><i class="fa fa-file-invoice-dollar"></i> Profile</a>
  <a href="/admin/inv/pdf/{{ inv.inv_id }}" class="rc bg-blue"><i class="fa fa-file-download"></i> PDF</a>
  <span class="show" data-src="#add_user" style="display:inline-block">
    <span class="rc b-green"><i class="fa fa-dollar-sign"></i>Add Qty</span>
  </span>
  <a href="/admin/inv/new/{{ inv.purchaser }}" class="rc bg"><i class="fa fa-plus"></i> New Invoice</a>
  {% if inv.status != 'Unpaid' %}
    <a href="/admin/inv/renew/{{ inv.purchaser }}" class="rc bg-gold"><i class="fa fa-history"></i> Renew</a>
  {% endif %}
  <a href="/admin/inv/send/{{ inv.inv_id }}" class="rc bg-grey"><i class="fa fa-envelope"></i> Send</a>
</div>

<form action="/admin/inv/add-user/{{ inv.purchaser }}" method="post">
<div class="pop hide" id="add_user">
  <div class="cell-3" style="margin-top:30px">
    <div class="bg-green tab b">Add Qty of subscribers</div>
    <div class="bg tabB">
      <div class="line-s">
        <b class="cell-8">Current User Qty</b>
        <input class="cell-4 ac" value="{{ inv.qty }}" readonly>
      </div>
      <div class="line-s">
        <b class="cell-8">Add Extra Qty</b>
        <input type="text" class="cell-4" name="qty">
      </div>
      <input type="submit" class="bg-green cell-12" value="Make a new Inv">
      {% if inv.qty > user_num %}
      <br>&nbsp;
      <p class="rc b-gold">Please check note, there might be <b class="green">{{ inv.qty - user_num }}</b> free addon users available</p>
      {% endif %}
    </div>
  </div>
</div>
</form>

<h1>Invoices # {{ inv.inv_id }}</h1>

{% if msg %}
  <p class="rc b-gold"><i class="fa fa-info"></i>{{ msg | raw }}</p>
{% endif %}

<div class="line-m">
  <div class="cell-6">
    {{ include('/Admin/Inv/invoice.addr.tpl') }}
  </div>
  <div class="cell-6">
    {{ include('/Admin/Inv/invoice.info.tpl') }}
  </div>
</div>

{% if inv.remark %}
  <div class="line">
    <p class="rc b-blue cell-8"><b>Remark</b>: {{ inv.remark }}</p>
  </div>
{% endif %}

{{ include('/Admin/Inv/invoice.item.tpl') }}

<div class="line">
  <div class="cell-6">
    {{ include('/Admin/Inv/invoice.note.tpl') }}
  </div>
  <div class="cell-6 ml-3 pl-3 bl-1 b-grey">
    {{ include('/Admin/Inv/invoice.pay.tpl') }}
    {{ include('/Admin/Inv/invoice.his.tpl') }}
    {{ include('/Admin/Inv/invoice.user.tpl') }}
  </div>
</div>
