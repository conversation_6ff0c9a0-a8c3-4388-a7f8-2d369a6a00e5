<tr><td colspan="4"><b>Current subscription period</b><br>&nbsp;</td></tr>
<tr class="bg"><td><b>Invoice ID:</b></td><td colspan="3"><a href="/admin/inv/{{ inv1.inv_id }}">{{ inv1.inv_id }} <i class="fa fa-pen"></i></a></td></tr>
<tr><td><b>Start date:</b></td><td colspan="3">{{ inv1.date_fm | date('j-M-Y') }}</td></tr>
<tr><td><b>End date:</b></td><td colspan="3">{{ inv1.date_to | date('j-M-Y') }}</td></tr>
<tr><td><b>Amount:</b></td><td colspan="3">${{ (inv1.amt / 100) | number_format(2) }}</td></tr>
<tr class="b-{% if inv1.status == 'Paid' %}green{% elseif inv1.status == 'Unpaid' %}gold{% elseif inv1.status == 'Cancelled' %}blue{% elseif inv1.status == 'Refunded' %}red{% endif %}">
  <td><b>Status:</b></td><td colspan="3">{{ inv1.status }}</td></tr>
<tr><td><b>Invoice due:</b></td><td colspan="3">{{ inv1.date_due | date('j-M-Y') }}</td></tr>
<tr><td><b>Date paid:</b></td><td colspan="3">
  {% if inv1.date_paid %}{{ inv1.date_paid | date('j-M-Y') }}
  {% else %}NA{% endif %}
  </td></tr>
<tr><td colspan="4">&nbsp;</td></tr>
<tr><td colspan="4">&nbsp;</td></tr>
<tr class="bg b"><td>Item descriptions</td><td class="ar">Unit&nbsp;price</td><td class="ar">Qty</td><td class="ar">Amount</td></tr>

{% set qty = 0 %}
{% set amt = 0 %}
{% for item in ord %}
  <tr>
    <td>{{ item.name_his }}</td>
    <td class="ar">${{ item.uprice | number_format(2) }}</td>
    <td class="ar">
      {% if item.vid > 100 %}
        {{ item.qty }}
        {% set qty = qty + item.qty %}
      {% endif %}
    </td>
    <td class="ar{% if item.uprice * item.qty != item.amt %} red{% endif %}">${{ item.amt | number_format(2) }}</td>
  </tr>
  {% set amt = amt + item.amt %}
{% endfor %}

<tr class="bg b"><td>Total</td><td></td><td class="ar">{{ qty }}</td><td class="ar">${{ amt | number_format(2) }}</td></tr>
<tr><td colspan="4" class="ar i grey">
{% if inv1.gst > 0 %}
  (GST ${{ (inv1.gst/100) | number_format(2) }})
{% else %}
  *NO GST
{% endif %}
</td></tr>

