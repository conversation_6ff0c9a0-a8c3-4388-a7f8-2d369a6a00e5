<h2>Invoice History</h2>
<table class="table">
<tr class="bg">
  <td>ID</td>
  <td>DueDate</td>
  <td>DatePaid</td>
  <td class="ar">Amount</td>
  <td style="text-align:center">User</td>
  <td>Status</td>
  <td>Type</td>
</tr>
{% for his in inv_his %}
  <tr class="b-{% if his.status == 'Paid' %}green{% elseif his.status == 'Unpaid' %}gold{% elseif his.status == 'Cancelled' %}blue{% elseif his.status == 'Refunded' %}red{% endif %}">
    <td>
      <a href="/admin/inv/{{ his.inv_id }}">{{ his.inv_id }}</a>
      {% if his.inv_id == inv.inv_id %} <b class="red">*</b>{% endif %}
    </td>
    <td class="small">{{ his.date_due | date('j-M-y') }}</td>
    <!--td{% if his.date_to < his.date_fm %} class="red"{% endif %}>{{ his.date_to | date('j-M-y') }}</td-->
    <td class="small">{% if his.pdate %}
      {{ his.pdate | date('j-M-y') }}{% if his.pnum > 1 %}<b class="red">*</b>{% endif %}
    {% else %}NA{% endif %}</td>
    <td class="ar">${{ his.amt | number_format(2) }}</td>
    <td style="text-align:center">{{ his.qty }}</td>
    <td class="small">{{ his.status }}</td>
    <td class="small">{{ his.item }}</td>
  </tr>
{% endfor %}
</table>
