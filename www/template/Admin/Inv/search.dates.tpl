<div class="cell-12 line">
  <div class="cell-6 line-m">
    <div class="cell-6 mr-1">
      <div class="tab b-blue b ac">Invoice Date</div>
      <div class="line-s p-1 b-blue">
        <b class="cell-3 ac fm" title="Today">From</b>
        <select class="cell-3" name="inv_date_fm_d">
          <option value="">Day</option>
          {% for d in 01..31 %}
            <option value="{{ d }}"{% if form.inv_date_fm_d == d %} selected{% endif %}>{{ d }}</option>
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_date_fm_m">
          <option value="">Month</option>
          {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
          {% if i %}
            <option value="{{ i }}"{% if form.inv_date_fm_m == i %} selected{% endif %}>{{ m }}</option>
          {% endif %}
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_date_fm_y">
          <option value="">Year</option>
          {% for y in 2010..2030 %}
            <option value="{{ y }}"{% if form.inv_date_fm_y == y %} selected{% endif %}>{{ y }}</option>
          {% endfor %}
        </select>
        <b class="cell-3 ac to" title="This Month">To</b>
        <select class="cell-3" name="inv_date_to_d">
          <option value="">Day</option>
          {% for d in 01..31 %}
            <option value="{{ d }}"{% if form.inv_date_to_d == d %} selected{% endif %}>{{ d }}</option>
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_date_to_m">
          <option value="">Month</option>
          {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
          {% if i %}
            <option value="{{ i }}"{% if form.inv_date_to_m == i %} selected{% endif %}>{{ m }}</option>
          {% endif %}
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_date_to_y">
          <option value="">Year</option>
          {% for y in 2010..2030 %}
            <option value="{{ y }}"{% if form.inv_date_to_y == y %} selected{% endif %}>{{ y }}</option>
          {% endfor %}
        </select>
      </div>
    </div>
    <div class="cell-6 bg-grey mr-1">
      <div class="tab b-green b ac">Invoice Start</div>
      <div class="line-s p-1 b-green">
        <b class="cell-3 ac fm" title="Today">From</b>
        <select class="cell-3" name="inv_due_fm_d">
          <option value="">Day</option>
          {% for d in 01..31 %}
            <option value="{{ d }}"{% if form.inv_due_fm_d == d %} selected{% endif %}>{{ d }}</option>
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_due_fm_m">
          <option value="">Month</option>
          {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
          {% if i %}
            <option value="{{ i }}"{% if form.inv_due_fm_m == i %} selected{% endif %}>{{ m }}</option>
          {% endif %}
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_due_fm_y">
          <option value="">Year</option>
          {% for y in 2010..2030 %}
            <option value="{{ y }}"{% if form.inv_due_fm_y == y %} selected{% endif %}>{{ y }}</option>
          {% endfor %}
        </select>
        <b class="cell-3 ac to" title="This Month">To</b>
        <select class="cell-3" name="inv_due_to_d">
          <option value="">Day</option>
          {% for d in 01..31 %}
            <option value="{{ d }}"{% if form.inv_due_to_d == d %} selected{% endif %}>{{ d }}</option>
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_due_to_m">
          <option value="">Month</option>
          {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
          {% if i %}
            <option value="{{ i }}"{% if form.inv_due_to_m == i %} selected{% endif %}>{{ m }}</option>
          {% endif %}
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_due_to_y">
          <option value="">Year</option>
          {% for y in 2010..2030 %}
            <option value="{{ y }}"{% if form.inv_due_to_y == y %} selected{% endif %}>{{ y }}</option>
          {% endfor %}
        </select>
      </div>
    </div>
  </div>
  <div class="cell-6 line-m">
    <div class="cell-6 mr-1">
      <div class="tab b-gold b ac">Invoice Expire</div>
      <div class="line-s p-1 b-gold">
        <b class="cell-3 ac fm" title="Today">From</b>
        <select class="cell-3" name="inv_exp_fm_d">
          <option value="">Day</option>
          {% for d in 01..31 %}
            <option value="{{ d }}"{% if form.inv_exp_fm_d == d %} selected{% endif %}>{{ d }}</option>
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_exp_fm_m">
          <option value="">Month</option>
          {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
          {% if i %}
            <option value="{{ i }}"{% if form.inv_exp_fm_m == i %} selected{% endif %}>{{ m }}</option>
          {% endif %}
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_exp_fm_y">
          <option value="">Year</option>
          {% for y in 2010..2030 %}
            <option value="{{ y }}"{% if form.inv_exp_fm_y == y %} selected{% endif %}>{{ y }}</option>
          {% endfor %}
        </select>
        <b class="cell-3 ac to" title="This Month">To</b>
        <select class="cell-3" name="inv_exp_to_d">
          <option value="">Day</option>
          {% for d in 01..31 %}
            <option value="{{ d }}"{% if form.inv_exp_to_d == d %} selected{% endif %}>{{ d }}</option>
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_exp_to_m">
          <option value="">Month</option>
          {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
          {% if i %}
            <option value="{{ i }}"{% if form.inv_exp_to_m == i %} selected{% endif %}>{{ m }}</option>
          {% endif %}
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_exp_to_y">
          <option value="">Year</option>
          {% for y in 2010..2030 %}
            <option value="{{ y }}"{% if form.inv_exp_to_y == y %} selected{% endif %}>{{ y }}</option>
          {% endfor %}
        </select>
      </div>
    </div>
    <div class="cell-6 mr-1">
      <div class="tab b-green b ac">Pay Date</div>
      <div class="line-s p-1 b-green">
        <b class="cell-3 ac fm" title="Today">From</b>
        <select class="cell-3" name="inv_pay_fm_d">
          <option value="">Day</option>
          {% for d in 01..31 %}
            <option value="{{ d }}"{% if form.inv_pay_fm_d == d %} selected{% endif %}>{{ d }}</option>
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_pay_fm_m">
          <option value="">Month</option>
          {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
          {% if i %}
            <option value="{{ i }}"{% if form.inv_pay_fm_m == i %} selected{% endif %}>{{ m }}</option>
          {% endif %}
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_pay_fm_y">
          <option value="">Year</option>
          {% for y in 2010..2030 %}
            <option value="{{ y }}"{% if form.inv_pay_fm_y == y %} selected{% endif %}>{{ y }}</option>
          {% endfor %}
        </select>
        <b class="cell-3 ac to" title="This Month">To</b>
        <select class="cell-3" name="inv_pay_to_d">
          <option value="">Day</option>
          {% for d in 01..31 %}
            <option value="{{ d }}"{% if form.inv_pay_to_d == d %} selected{% endif %}>{{ d }}</option>
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_pay_to_m">
          <option value="">Month</option>
          {% for i,m in ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'] %}
          {% if i %}
            <option value="{{ i }}"{% if form.inv_pay_to_m == i %} selected{% endif %}>{{ m }}</option>
          {% endif %}
          {% endfor %}
        </select>
        <select class="cell-3" name="inv_pay_to_y">
          <option value="">Year</option>
          {% for y in 2010..2030 %}
            <option value="{{ y }}"{% if form.inv_pay_to_y == y %} selected{% endif %}>{{ y }}</option>
          {% endfor %}
        </select>
      </div>
    </div>
  </div>
</div>

<script>
$(function() {
  $('.fm').on('click', function() {
     var ymd = new Date();
     var d = ymd.getDate(), m = ymd.getMonth() + 1, y = ymd.getFullYear();
     $(this).next().val(d).next().val(m).next().val(y).next().next().val(d).next().val(m).next().val(y);
  });
  $('.to').on('click', function() {
     var ymd = new Date();
     var m = ymd.getMonth() + 1, y = ymd.getFullYear();
     $(this).prev().val(y).prev().val(m).prev().val(1);
     $(this).next().val(31).next().val(m).next().val(y);
  })
})
</script>
