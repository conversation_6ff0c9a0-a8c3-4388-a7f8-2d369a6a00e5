{{ include('/Admin/Inv/search.tpl') }}

{{ chart | raw }}

<h1>Invoices</h1>

{% if msg %}
  <p class="b-gold rc">{{ msg | raw }}</p>
{% endif %}

{% if keyw %}
  <p class="b-green rc">Search Terms: {{ keyw }}</p>
{% endif %}

<table class="table">
  <tr class="bg b">
    <td>ID</td>
    <td>Start</td>
    <td>Expire</td>
    <td>Orgnisation</td>
    <td>Email</td>
    <td>Status</td>
    <td class="ar">Amount</td>
  </tr>

{% for inv in invs %}
  <tr class="b-{% if inv.status == 'Paid' %}green{% elseif inv.status == 'Unpaid' %}gold{% elseif inv.status == 'Cancelled' %}blue{% elseif inv.status == 'Refunded' %}red{% endif %}">
    <td><a href="/admin/inv/{{ inv.inv_id }}">{{ inv.inv_id }}</a></td>
    <td>{{ inv.date_fm | date('d-M-Y') }}</td>
    <td>{{ inv.date_to | date('d-M-Y') }}</td>
    <td><a href="/admin/inv/profile/{{ inv.purchaser }}">{{ inv.company }}</a></td>
    <td>{{ inv.email }}</td>
    <td>{{ inv.status }}</td>
    <td class="ar">{{ inv.amt / 100 }}</td>
  </tr>
{% endfor %}
</table>

{{ include('/Admin/pgno.tpl') }}
