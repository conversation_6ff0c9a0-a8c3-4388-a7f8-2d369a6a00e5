<tr><td colspan="4"><b>Next/renewal subscription period</b><br>&nbsp;</td></tr>
<tr class="bg"><td><b>Invoice ID:</b></td><td colspan="3">
  {% if inv2.inv_id %}<a href="/admin/inv/{{ inv2.inv_id }}">{{ inv2.inv_id }} <i class="fa fa-pen"></i></a>
  {% else %}NA{% endif %}
  </td></tr>
<tr><td><b>Start date:</b></td><td colspan="3">{{ inv2.date_fm | date('j-M-Y') }}</td></tr>
<tr><td><b>End date:</b></td><td colspan="3">{{ inv2.date_to | date('j-M-Y') }}</td></tr>
<tr><td><b>Amount:</b></td><td colspan="3">${{ (inv2.amt / 100) | number_format(2) }}</td></tr>
<tr class="b-{% if inv2.status == 'Paid' %}green{% elseif inv2.status == 'Unpaid' %}gold{% elseif inv2.status == 'Cancelled' %}blue{% elseif inv2.status == 'Refunded' %}red{% endif %}">
  <td><b>Status:</b></td><td colspan="3">{{ inv2.status }}</td></tr>
<tr><td><b>Invoice due:</b></td><td colspan="3">
  {% if inv2.inv_id %}{{ inv2.date_due | date('j-M-Y') }}
  {% else %}NA{% endif %}
  </td></tr>
<tr><td><b>Date paid:</b></td><td colspan="3">
  {% if inv2.date_paid %}{{ inv2.date_paid | date('j-M-Y') }}
  {% else %}NA{% endif %}
  </td></tr>
<tr><td><b>Number of users:</b></td><td colspan="3">
  {{ inv1.qty }} x {{ inv1.item }}
  <i class="fa fa-pen show" data-src="#edit-item"></i>
  </td></tr>
<tr><td colspan="4">&nbsp;</td></tr>
<tr class="bg b"><td>Item descriptions</td><td class="ar">Unit&nbsp;price</td><td class="ar">Qty</td><td class="ar">Amount</td></tr>

{% set qty = 0 %}
{% set amt = 0 %}
{% for item in ord2 %}
  <tr>
    <td>{{ item.name_his }}</td>
    <td class="ar">${{ item.uprice | number_format(2) }}</td>
    <td class="ar">
      {% if item.vid > 100 %}
        {{ item.qty }}
        {% set qty = qty + item.qty %}
      {% endif %}
    </td>
    <td class="ar{% if item.uprice * item.qty != item.amt %} red{% endif %}">${{ item.amt | number_format(2) }}</td>
  </tr>
  {% set amt = amt + item.amt %}
{% endfor %}

<tr class="bg b"><td colspan="2">Total(NB: "next" prices are subject to change)</td><td class="ar">{{ qty }}</td><td class="ar">${{ amt | number_format(2) }}</td></tr>
<tr><td colspan="4" class="ar i grey">
{% if inv2.gst > 0 %}
  (GST ${{ (inv2.gst/100) | number_format(2) }})
{% else %}
  *NO GST
{% endif %}
</td></tr>

<form action="/admin/inv/edit/{{ inv1.last_inv_id }}" method="post">
<input type="hidden" name="cmd" value="save_qty">
<div class="pop hide" id="edit-item">
  <div class="cell-4" style="margin-top:30px">
    <div class="bg-dark tab b">Update Qty for next invoice</div>
    <div class="bg tabB">
      <div class="line-s">
        <b class="cell-4">Type</b>
        <select class="cell-8" name="item">
          <option value="">Please select</option>
        {% for p in price %}
          <option value="{{ p.item_abbr }}"{% if p.item_abbr == inv1.item %} selected{% endif %}>{{ p.ver_name }}</option>
        {% endfor %}
        </select>
      </div>
      <div class="line-s">
        <b class="cell-4">Users (Qty)</b>
        <input type="text" class="cell-4" value="{{ inv1.qty }}" name="qty">
        <input type="submit" class="bg-green cell-4" value="Save">
      </div>
    </div>
  </div>
</div>
</form>
