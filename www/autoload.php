<?php
spl_autoload_register(function ($class) {
    $classmap = cms_classmap();
    $arr = explode('\\', $class);
    $num = count($arr);
    $arr_hold = [];
    for ($i = $num; $i > 0; $i--) {
        $class = implode('\\', $arr);
        if (isset($classmap[$class])) {
            return cms_classload($classmap[$class] . ($arr_hold ? DS . implode(DS, $arr_hold) : ''));
        } else {
            array_unshift($arr_hold, array_pop($arr));
        }
    }
});

function cms_classload($class) {
    $class .= (substr($class, -4) == '.php') ? '' : '.php';
    //echo '<br>' . $class;
    if (file_exists($class)) {
        include_once $class;
    }
}

function cms_classmap() {
    $vendor = ROOT . 'vendor' . DS;
    return [
        'App'     => ROOT . 'app',

        // dompdf
        'Dompdf\Cpdf'       => $vendor . 'dompdf/dompdf/lib/Cpdf.php',
        'HTML5_Data'        => $vendor . 'dompdf/dompdf/lib/html5lib/Data.php',
        'HTML5_InputStream' => $vendor . 'dompdf/dompdf/lib/html5lib/InputStream.php',
        'HTML5_Parser'      => $vendor . 'dompdf/dompdf/lib/html5lib/Parser.php',
        'HTML5_Tokenizer'   => $vendor . 'dompdf/dompdf/lib/html5lib/Tokenizer.php',
        'HTML5_TreeBuilder' => $vendor . 'dompdf/dompdf/lib/html5lib/TreeBuilder.php',
        'Sabberworm\\CSS'   => $vendor . 'sabberworm/php-css-parser/src',
        'Dompdf'  => $vendor . 'dompdf/dompdf/src',
        'Masterminds'  => $vendor . 'masterminds/html5/src',
        'FontLib' => $vendor . 'phenx/php-font-lib/src/FontLib',
        'Svg'     => $vendor . 'phenx/php-svg-lib/src/Svg',

        'Diff'    => $vendor . 'phpspec/php-diff/lib/Diff.php',
        'Diff_Renderer_Html_Inline' => $vendor . 'phpspec/php-diff/lib/Diff/Renderer/Html/Inline.php',

        'Michelf' => $vendor . 'michelf/php-markdown/Michelf',

        // manually installed and pls read head of processCard.php
        'Payway'  => $vendor . 'payway/processCard.php',

        'PHPMailer\\PHPMailer' => $vendor . 'phpmailer/phpmailer/src',

        'Topnew'  => $vendor . 'topnew/src',

        // twig
        'Twig'    => $vendor . 'twig/twig/src',
        'Twig\\Extra\\Markdown' => $vendor . 'twig/markdown-extra',
        'Symfony\\Polyfill\\Mbstring' => $vendor . 'symfony/polyfill-mbstring',
        'Symfony\\Polyfill\\Ctype'    => $vendor . 'symfony/polyfill-ctype',
    ];
}
